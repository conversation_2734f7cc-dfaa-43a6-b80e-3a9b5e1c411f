# -*- coding: utf-8 -*-
"""
数据处理器
处理和预处理市场数据，为技术分析做准备

Author: SuperBot Team
Date: 2025-01-04
"""

import numpy as np
import pandas as pd
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.utils.logger import get_logger

logger = get_logger(__name__)


class DataProcessingError(Exception):
    """数据处理错误"""
    pass


@dataclass
class ProcessedData:
    """处理后的数据"""
    timestamps: np.ndarray
    opens: np.ndarray
    highs: np.ndarray
    lows: np.ndarray
    closes: np.ndarray
    volumes: np.ndarray
    data_quality: Dict[str, Any]
    processing_info: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamps': self.timestamps.tolist(),
            'opens': self.opens.tolist(),
            'highs': self.highs.tolist(),
            'lows': self.lows.tolist(),
            'closes': self.closes.tolist(),
            'volumes': self.volumes.tolist(),
            'data_quality': self.data_quality,
            'processing_info': self.processing_info
        }


class MarketDataProcessor:
    """市场数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.min_data_points = 20
        self.max_price_change_threshold = 0.5  # 50%的价格变化阈值
        self.volume_outlier_threshold = 10.0   # 成交量异常值阈值
        
        logger.info("市场数据处理器初始化完成")
    
    def validate_ohlcv_data(self, ohlcv_data: List[List]) -> Dict[str, Any]:
        """验证OHLCV数据质量"""
        try:
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'data_points': len(ohlcv_data) if ohlcv_data else 0,
                'quality_score': 0.0
            }
            
            if not ohlcv_data:
                validation_result['errors'].append("数据为空")
                validation_result['is_valid'] = False
                return validation_result
            
            if len(ohlcv_data) < self.min_data_points:
                validation_result['errors'].append(f"数据点不足，需要至少 {self.min_data_points} 个，实际 {len(ohlcv_data)} 个")
                validation_result['is_valid'] = False
            
            # 检查数据格式
            invalid_rows = 0
            price_anomalies = 0
            volume_anomalies = 0
            
            for i, row in enumerate(ohlcv_data):
                if len(row) != 6:
                    invalid_rows += 1
                    continue
                
                timestamp, open_price, high, low, close, volume = row
                
                # 检查价格逻辑
                try:
                    open_price, high, low, close, volume = float(open_price), float(high), float(low), float(close), float(volume)
                    
                    if not (low <= open_price <= high and low <= close <= high):
                        price_anomalies += 1
                    
                    if volume < 0:
                        volume_anomalies += 1
                    
                    # 检查价格变化幅度
                    if i > 0:
                        prev_close = float(ohlcv_data[i-1][4])
                        price_change = abs(close - prev_close) / prev_close
                        if price_change > self.max_price_change_threshold:
                            validation_result['warnings'].append(f"第 {i+1} 行价格变化异常: {price_change:.2%}")
                
                except (ValueError, TypeError, ZeroDivisionError):
                    invalid_rows += 1
            
            # 统计质量问题
            if invalid_rows > 0:
                validation_result['errors'].append(f"发现 {invalid_rows} 行无效数据")
                validation_result['is_valid'] = False
            
            if price_anomalies > 0:
                validation_result['warnings'].append(f"发现 {price_anomalies} 行价格逻辑异常")
            
            if volume_anomalies > 0:
                validation_result['warnings'].append(f"发现 {volume_anomalies} 行成交量异常")
            
            # 计算质量分数
            total_issues = invalid_rows + price_anomalies + volume_anomalies
            if len(ohlcv_data) > 0:
                validation_result['quality_score'] = max(0, 100 - (total_issues / len(ohlcv_data) * 100))
            
            return validation_result
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return {
                'is_valid': False,
                'errors': [f"验证过程异常: {e}"],
                'warnings': [],
                'data_points': 0,
                'quality_score': 0.0
            }
    
    def clean_ohlcv_data(self, ohlcv_data: List[List]) -> List[List]:
        """清理OHLCV数据"""
        try:
            if not ohlcv_data:
                return []
            
            cleaned_data = []
            removed_count = 0
            
            for i, row in enumerate(ohlcv_data):
                try:
                    if len(row) != 6:
                        removed_count += 1
                        continue
                    
                    timestamp, open_price, high, low, close, volume = row
                    
                    # 转换数据类型
                    timestamp = int(timestamp)
                    open_price = float(open_price)
                    high = float(high)
                    low = float(low)
                    close = float(close)
                    volume = float(volume)
                    
                    # 检查数据有效性
                    if not all(np.isfinite([open_price, high, low, close, volume])):
                        removed_count += 1
                        continue
                    
                    if not (low <= open_price <= high and low <= close <= high):
                        # 尝试修复价格逻辑错误
                        if high < max(open_price, close):
                            high = max(open_price, close)
                        if low > min(open_price, close):
                            low = min(open_price, close)
                    
                    if volume < 0:
                        volume = 0  # 将负成交量设为0
                    
                    cleaned_data.append([timestamp, open_price, high, low, close, volume])
                    
                except (ValueError, TypeError):
                    removed_count += 1
                    continue
            
            # 按时间戳排序
            cleaned_data.sort(key=lambda x: x[0])
            
            if removed_count > 0:
                logger.info(f"数据清理完成: 移除 {removed_count} 行无效数据，保留 {len(cleaned_data)} 行")
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
            raise DataProcessingError(f"数据清理失败: {e}")
    
    def fill_missing_data(self, ohlcv_data: List[List], method: str = 'forward') -> List[List]:
        """填充缺失数据"""
        try:
            if not ohlcv_data or len(ohlcv_data) < 2:
                return ohlcv_data
            
            filled_data = []
            
            for i, row in enumerate(ohlcv_data):
                if i == 0:
                    filled_data.append(row)
                    continue
                
                current_timestamp = row[0]
                prev_timestamp = filled_data[-1][0]
                
                # 检查是否有时间间隔缺失（这里简化处理）
                filled_data.append(row)
            
            return filled_data
            
        except Exception as e:
            logger.error(f"填充缺失数据失败: {e}")
            return ohlcv_data
    
    def detect_outliers(self, data: np.ndarray, method: str = 'iqr') -> np.ndarray:
        """检测异常值"""
        try:
            if method == 'iqr':
                q1 = np.percentile(data, 25)
                q3 = np.percentile(data, 75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = (data < lower_bound) | (data > upper_bound)
                return outliers
            
            elif method == 'zscore':
                z_scores = np.abs((data - np.mean(data)) / np.std(data))
                outliers = z_scores > 3
                return outliers
            
            else:
                return np.zeros(len(data), dtype=bool)
                
        except Exception as e:
            logger.error(f"异常值检测失败: {e}")
            return np.zeros(len(data), dtype=bool)

    def normalize_data(self, data: np.ndarray, method: str = 'minmax') -> Tuple[np.ndarray, Dict[str, float]]:
        """数据归一化"""
        try:
            if method == 'minmax':
                # Min-Max归一化 (0-1)
                data_min = np.min(data)
                data_max = np.max(data)

                if data_max == data_min:
                    normalized = np.zeros_like(data)
                    params = {'min': data_min, 'max': data_max, 'range': 0}
                else:
                    normalized = (data - data_min) / (data_max - data_min)
                    params = {'min': data_min, 'max': data_max, 'range': data_max - data_min}

                return normalized, params

            elif method == 'zscore':
                # Z-Score标准化
                data_mean = np.mean(data)
                data_std = np.std(data)

                if data_std == 0:
                    normalized = np.zeros_like(data)
                    params = {'mean': data_mean, 'std': data_std}
                else:
                    normalized = (data - data_mean) / data_std
                    params = {'mean': data_mean, 'std': data_std}

                return normalized, params

            elif method == 'robust':
                # 鲁棒标准化（使用中位数和IQR）
                data_median = np.median(data)
                q1 = np.percentile(data, 25)
                q3 = np.percentile(data, 75)
                iqr = q3 - q1

                if iqr == 0:
                    normalized = np.zeros_like(data)
                    params = {'median': data_median, 'q1': q1, 'q3': q3, 'iqr': iqr}
                else:
                    normalized = (data - data_median) / iqr
                    params = {'median': data_median, 'q1': q1, 'q3': q3, 'iqr': iqr}

                return normalized, params

            else:
                raise DataProcessingError(f"不支持的归一化方法: {method}")

        except Exception as e:
            logger.error(f"数据归一化失败: {e}")
            raise DataProcessingError(f"数据归一化失败: {e}")

    def denormalize_data(self, normalized_data: np.ndarray, params: Dict[str, float], method: str = 'minmax') -> np.ndarray:
        """数据反归一化"""
        try:
            if method == 'minmax':
                if params['range'] == 0:
                    return np.full_like(normalized_data, params['min'])
                else:
                    return normalized_data * params['range'] + params['min']

            elif method == 'zscore':
                if params['std'] == 0:
                    return np.full_like(normalized_data, params['mean'])
                else:
                    return normalized_data * params['std'] + params['mean']

            elif method == 'robust':
                if params['iqr'] == 0:
                    return np.full_like(normalized_data, params['median'])
                else:
                    return normalized_data * params['iqr'] + params['median']

            else:
                raise DataProcessingError(f"不支持的反归一化方法: {method}")

        except Exception as e:
            logger.error(f"数据反归一化失败: {e}")
            raise DataProcessingError(f"数据反归一化失败: {e}")

    def convert_to_returns(self, prices: np.ndarray, method: str = 'simple') -> np.ndarray:
        """转换为收益率"""
        try:
            if len(prices) < 2:
                return np.array([])

            if method == 'simple':
                # 简单收益率
                returns = np.diff(prices) / prices[:-1]
            elif method == 'log':
                # 对数收益率
                returns = np.diff(np.log(prices))
            else:
                raise DataProcessingError(f"不支持的收益率计算方法: {method}")

            return returns

        except Exception as e:
            logger.error(f"收益率计算失败: {e}")
            raise DataProcessingError(f"收益率计算失败: {e}")

    def convert_timeframe(self, ohlcv_data: List[List], from_timeframe: str, to_timeframe: str) -> List[List]:
        """时间周期转换"""
        try:
            if not ohlcv_data:
                return []

            # 时间周期映射（分钟）
            timeframe_minutes = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30,
                '1h': 60, '4h': 240, '1d': 1440
            }

            from_minutes = timeframe_minutes.get(from_timeframe)
            to_minutes = timeframe_minutes.get(to_timeframe)

            if not from_minutes or not to_minutes:
                raise DataProcessingError(f"不支持的时间周期: {from_timeframe} -> {to_timeframe}")

            if to_minutes <= from_minutes:
                raise DataProcessingError("目标时间周期必须大于源时间周期")

            # 计算聚合比例
            ratio = to_minutes // from_minutes
            if to_minutes % from_minutes != 0:
                raise DataProcessingError(f"时间周期不能整除: {to_timeframe} / {from_timeframe}")

            # 聚合数据
            converted_data = []
            for i in range(0, len(ohlcv_data), ratio):
                chunk = ohlcv_data[i:i+ratio]
                if len(chunk) < ratio:
                    break  # 不完整的周期跳过

                # 聚合OHLCV
                timestamp = chunk[0][0]  # 使用第一个时间戳
                open_price = chunk[0][1]  # 使用第一个开盘价
                high_price = max(row[2] for row in chunk)  # 最高价
                low_price = min(row[3] for row in chunk)   # 最低价
                close_price = chunk[-1][4]  # 使用最后一个收盘价
                volume = sum(row[5] for row in chunk)      # 成交量求和

                converted_data.append([timestamp, open_price, high_price, low_price, close_price, volume])

            logger.info(f"时间周期转换完成: {from_timeframe} -> {to_timeframe}, {len(ohlcv_data)} -> {len(converted_data)}")
            return converted_data

        except Exception as e:
            logger.error(f"时间周期转换失败: {e}")
            raise DataProcessingError(f"时间周期转换失败: {e}")

    def process_ohlcv_data(self, ohlcv_data: List[List],
                          clean_data: bool = True,
                          fill_missing: bool = False,
                          detect_outliers: bool = True) -> ProcessedData:
        """处理OHLCV数据"""
        try:
            start_time = datetime.now()
            processing_info = {
                'start_time': start_time.isoformat(),
                'original_data_points': len(ohlcv_data) if ohlcv_data else 0,
                'processing_steps': []
            }
            
            # 验证原始数据
            validation_result = self.validate_ohlcv_data(ohlcv_data)
            if not validation_result['is_valid']:
                raise DataProcessingError(f"原始数据验证失败: {validation_result['errors']}")
            
            processed_data = ohlcv_data.copy()
            
            # 清理数据
            if clean_data:
                processed_data = self.clean_ohlcv_data(processed_data)
                processing_info['processing_steps'].append('data_cleaning')
            
            # 填充缺失数据
            if fill_missing:
                processed_data = self.fill_missing_data(processed_data)
                processing_info['processing_steps'].append('missing_data_filling')
            
            # 转换为numpy数组
            if not processed_data:
                raise DataProcessingError("处理后数据为空")

            data_array = np.array(processed_data)

            # 安全地转换时间戳
            try:
                timestamps = data_array[:, 0].astype(np.int64)
            except (ValueError, OverflowError):
                # 如果转换失败，尝试先转换为float再转换为int
                timestamps = data_array[:, 0].astype(float).astype(np.int64)

            opens = data_array[:, 1].astype(float)
            highs = data_array[:, 2].astype(float)
            lows = data_array[:, 3].astype(float)
            closes = data_array[:, 4].astype(float)
            volumes = data_array[:, 5].astype(float)
            
            # 数据质量分析
            data_quality = {
                'final_data_points': len(processed_data),
                'data_completeness': len(processed_data) / processing_info['original_data_points'] if processing_info['original_data_points'] > 0 else 0,
                'price_range': {
                    'min': float(np.min(lows)),
                    'max': float(np.max(highs)),
                    'avg': float(np.mean(closes))
                },
                'volume_stats': {
                    'min': float(np.min(volumes)),
                    'max': float(np.max(volumes)),
                    'avg': float(np.mean(volumes)),
                    'total': float(np.sum(volumes))
                },
                'time_range': {
                    'start': datetime.fromtimestamp(timestamps[0] / 1000).isoformat(),
                    'end': datetime.fromtimestamp(timestamps[-1] / 1000).isoformat(),
                    'duration_hours': (timestamps[-1] - timestamps[0]) / (1000 * 3600)
                }
            }
            
            # 异常值检测
            if detect_outliers:
                price_outliers = self.detect_outliers(closes)
                volume_outliers = self.detect_outliers(volumes)
                
                data_quality['outliers'] = {
                    'price_outliers': int(np.sum(price_outliers)),
                    'volume_outliers': int(np.sum(volume_outliers)),
                    'total_outliers': int(np.sum(price_outliers | volume_outliers))
                }
                processing_info['processing_steps'].append('outlier_detection')
            
            # 完成处理
            processing_time = (datetime.now() - start_time).total_seconds()
            processing_info.update({
                'end_time': datetime.now().isoformat(),
                'processing_time': processing_time,
                'final_data_points': len(processed_data)
            })
            
            result = ProcessedData(
                timestamps=timestamps,
                opens=opens,
                highs=highs,
                lows=lows,
                closes=closes,
                volumes=volumes,
                data_quality=data_quality,
                processing_info=processing_info
            )
            
            logger.info(f"数据处理完成: {processing_info['original_data_points']} -> {len(processed_data)} 数据点, 耗时 {processing_time:.3f}s")
            return result

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            raise DataProcessingError(f"数据处理失败: {e}")

    def convert_to_dataframe(self, ohlcv_data: List[List]) -> pd.DataFrame:
        """转换为pandas DataFrame"""
        try:
            if not ohlcv_data:
                return pd.DataFrame()

            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # 转换数据类型
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['open'] = df['open'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(float)

            # 设置时间戳为索引
            df.set_index('timestamp', inplace=True)

            return df

        except Exception as e:
            logger.error(f"转换为DataFrame失败: {e}")
            raise DataProcessingError(f"转换为DataFrame失败: {e}")

    def convert_from_dataframe(self, df: pd.DataFrame) -> List[List]:
        """从pandas DataFrame转换"""
        try:
            if df.empty:
                return []

            # 重置索引以获取时间戳列
            df_reset = df.reset_index()

            # 确保列名正确
            expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df_reset.columns for col in expected_columns):
                raise DataProcessingError(f"DataFrame缺少必需列: {expected_columns}")

            # 转换时间戳为毫秒
            if pd.api.types.is_datetime64_any_dtype(df_reset['timestamp']):
                df_reset['timestamp'] = df_reset['timestamp'].astype('int64') // 1000000

            # 转换为列表
            ohlcv_data = df_reset[expected_columns].values.tolist()

            return ohlcv_data

        except Exception as e:
            logger.error(f"从DataFrame转换失败: {e}")
            raise DataProcessingError(f"从DataFrame转换失败: {e}")

    def resample_data(self, ohlcv_data: List[List], target_points: int, method: str = 'interpolate') -> List[List]:
        """重采样数据到指定数据点数"""
        try:
            if not ohlcv_data or len(ohlcv_data) <= target_points:
                return ohlcv_data

            if method == 'downsample':
                # 下采样：等间隔选择数据点
                step = len(ohlcv_data) // target_points
                indices = np.arange(0, len(ohlcv_data), step)[:target_points]
                resampled_data = [ohlcv_data[i] for i in indices]

            elif method == 'interpolate':
                # 插值重采样
                df = self.convert_to_dataframe(ohlcv_data)

                # 创建新的时间索引
                start_time = df.index[0]
                end_time = df.index[-1]
                new_index = pd.date_range(start=start_time, end=end_time, periods=target_points)

                # 重新索引并插值
                df_resampled = df.reindex(new_index).interpolate(method='linear')

                resampled_data = self.convert_from_dataframe(df_resampled)

            else:
                raise DataProcessingError(f"不支持的重采样方法: {method}")

            logger.info(f"数据重采样完成: {len(ohlcv_data)} -> {len(resampled_data)} 数据点")
            return resampled_data

        except Exception as e:
            logger.error(f"数据重采样失败: {e}")
            raise DataProcessingError(f"数据重采样失败: {e}")

    def calculate_data_statistics(self, ohlcv_data: List[List]) -> Dict[str, Any]:
        """计算数据统计信息"""
        try:
            if not ohlcv_data:
                return {}

            data_array = np.array(ohlcv_data)
            opens = data_array[:, 1].astype(float)
            highs = data_array[:, 2].astype(float)
            lows = data_array[:, 3].astype(float)
            closes = data_array[:, 4].astype(float)
            volumes = data_array[:, 5].astype(float)

            # 价格统计
            price_stats = {
                'open': {
                    'mean': float(np.mean(opens)),
                    'std': float(np.std(opens)),
                    'min': float(np.min(opens)),
                    'max': float(np.max(opens)),
                    'median': float(np.median(opens))
                },
                'high': {
                    'mean': float(np.mean(highs)),
                    'std': float(np.std(highs)),
                    'min': float(np.min(highs)),
                    'max': float(np.max(highs)),
                    'median': float(np.median(highs))
                },
                'low': {
                    'mean': float(np.mean(lows)),
                    'std': float(np.std(lows)),
                    'min': float(np.min(lows)),
                    'max': float(np.max(lows)),
                    'median': float(np.median(lows))
                },
                'close': {
                    'mean': float(np.mean(closes)),
                    'std': float(np.std(closes)),
                    'min': float(np.min(closes)),
                    'max': float(np.max(closes)),
                    'median': float(np.median(closes))
                }
            }

            # 成交量统计
            volume_stats = {
                'mean': float(np.mean(volumes)),
                'std': float(np.std(volumes)),
                'min': float(np.min(volumes)),
                'max': float(np.max(volumes)),
                'median': float(np.median(volumes)),
                'total': float(np.sum(volumes))
            }

            # 价格变化统计
            returns = self.convert_to_returns(closes, 'simple')
            if len(returns) > 0:
                return_stats = {
                    'mean': float(np.mean(returns)),
                    'std': float(np.std(returns)),
                    'min': float(np.min(returns)),
                    'max': float(np.max(returns)),
                    'skewness': float(self._calculate_skewness(returns)),
                    'kurtosis': float(self._calculate_kurtosis(returns))
                }
            else:
                return_stats = {}

            return {
                'data_points': len(ohlcv_data),
                'price_statistics': price_stats,
                'volume_statistics': volume_stats,
                'return_statistics': return_stats,
                'data_range': {
                    'start_timestamp': int(data_array[0, 0]),
                    'end_timestamp': int(data_array[-1, 0]),
                    'duration_hours': (data_array[-1, 0] - data_array[0, 0]) / (1000 * 3600)
                }
            }

        except Exception as e:
            logger.error(f"计算数据统计失败: {e}")
            return {'error': str(e)}

    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        try:
            n = len(data)
            if n < 3:
                return 0.0

            mean = np.mean(data)
            std = np.std(data, ddof=0)

            if std == 0:
                return 0.0

            skewness = np.sum(((data - mean) / std) ** 3) / n
            return skewness

        except Exception:
            return 0.0

    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        try:
            n = len(data)
            if n < 4:
                return 0.0

            mean = np.mean(data)
            std = np.std(data, ddof=0)

            if std == 0:
                return 0.0

            kurtosis = np.sum(((data - mean) / std) ** 4) / n - 3
            return kurtosis

        except Exception:
            return 0.0

    def get_processor_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        return {
            'processor_name': 'MarketDataProcessor',
            'min_data_points': self.min_data_points,
            'max_price_change_threshold': self.max_price_change_threshold,
            'volume_outlier_threshold': self.volume_outlier_threshold,
            'supported_methods': {
                'data_cleaning': True,
                'missing_data_filling': True,
                'outlier_detection': ['iqr', 'zscore'],
                'data_validation': True,
                'normalization': ['minmax', 'zscore', 'robust'],
                'returns_calculation': ['simple', 'log'],
                'timeframe_conversion': True,
                'dataframe_conversion': True,
                'data_resampling': ['downsample', 'interpolate'],
                'statistical_analysis': True
            },
            'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
            'data_formats': ['list', 'numpy_array', 'pandas_dataframe'],
            'status': 'ready'
        }


# 全局数据处理器实例
market_data_processor = MarketDataProcessor()


def get_market_data_processor() -> MarketDataProcessor:
    """获取市场数据处理器实例"""
    return market_data_processor
