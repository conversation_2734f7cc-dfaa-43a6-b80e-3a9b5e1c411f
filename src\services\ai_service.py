# -*- coding: utf-8 -*-
"""
AI服务
封装DeepSeek API调用，提供智能分析功能

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import time
import requests
import logging
import asyncio
import hashlib
from typing import Any, Dict, List, Optional
from datetime import datetime, timed<PERSON>ta
from threading import Lock
from concurrent.futures import ThreadPoolExecutor, Future

from src.data.repositories.trading_repository import get_api_keys_repository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AIServiceError(Exception):
    """AI服务错误"""
    pass


class AIRateLimitError(Exception):
    """AI服务频率限制错误"""
    pass


class AIService:
    """AI服务类"""
    
    def __init__(self):
        """初始化AI服务"""
        self.api_keys_repo = get_api_keys_repository()
        self._request_lock = Lock()
        self._last_request_time = 0
        self._rate_limit_delay = 1.0  # 默认请求间隔1秒

        # DeepSeek API配置
        self.api_base_url = "https://api.deepseek.com"
        self.api_version = "v1"
        self.model_name = "deepseek-chat"
        self.max_tokens = 4096
        self.temperature = 0.3
        self.timeout = 30

        # 异步调用支持
        self._executor = ThreadPoolExecutor(max_workers=5)

        # 结果缓存
        self._cache: Dict[str, Dict] = {}
        self._cache_lock = Lock()
        self._cache_ttl = 300  # 缓存5分钟
        self._max_cache_size = 100

        # 重试配置
        self._max_retries = 3
        self._retry_delay = 2.0
        self._backoff_factor = 2.0

        logger.info("AI服务初始化完成")
    
    def _get_api_key(self) -> str:
        """获取DeepSeek API密钥"""
        try:
            api_keys = self.api_keys_repo.find_by_exchange("deepseek", is_active=True)
            if not api_keys:
                raise AIServiceError("未找到有效的DeepSeek API密钥")
            
            return api_keys.api_key
            
        except Exception as e:
            logger.error(f"获取DeepSeek API密钥失败: {e}")
            raise AIServiceError(f"获取API密钥失败: {e}")

    def _generate_cache_key(self, messages: List[Dict], temperature: float = None) -> str:
        """生成缓存键"""
        content = json.dumps(messages, sort_keys=True) + str(temperature or self.temperature)
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cache(self, cache_key: str) -> Optional[Dict]:
        """获取缓存"""
        with self._cache_lock:
            cache_entry = self._cache.get(cache_key)
            if cache_entry:
                # 检查缓存是否过期
                if time.time() - cache_entry['timestamp'] < self._cache_ttl:
                    logger.debug(f"使用缓存结果: {cache_key[:8]}...")
                    return cache_entry['data']
                else:
                    # 删除过期缓存
                    del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, data: Dict):
        """设置缓存"""
        with self._cache_lock:
            # 限制缓存大小
            if len(self._cache) >= self._max_cache_size:
                # 删除最旧的缓存项
                oldest_key = min(self._cache.keys(),
                               key=lambda k: self._cache[k]['timestamp'])
                del self._cache[oldest_key]

            self._cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }

    def _clear_expired_cache(self):
        """清理过期缓存"""
        with self._cache_lock:
            current_time = time.time()
            expired_keys = [
                key for key, entry in self._cache.items()
                if current_time - entry['timestamp'] >= self._cache_ttl
            ]
            for key in expired_keys:
                del self._cache[key]

            if expired_keys:
                logger.debug(f"清理过期缓存: {len(expired_keys)} 项")

    def _rate_limit_check(self):
        """检查频率限制"""
        with self._request_lock:
            current_time = time.time()
            time_diff = current_time - self._last_request_time
            
            if time_diff < self._rate_limit_delay:
                sleep_time = self._rate_limit_delay - time_diff
                logger.debug(f"AI服务频率限制等待: {sleep_time:.2f}秒")
                time.sleep(sleep_time)
            
            self._last_request_time = time.time()

    def _make_api_request_with_retry(self, messages: List[Dict[str, str]],
                                   temperature: Optional[float] = None,
                                   max_tokens: Optional[int] = None) -> Dict[str, Any]:
        """带重试的API请求"""
        last_exception = None

        for attempt in range(self._max_retries):
            try:
                return self._make_api_request(messages, temperature, max_tokens)

            except AIRateLimitError as e:
                last_exception = e
                if attempt < self._max_retries - 1:
                    delay = self._retry_delay * (self._backoff_factor ** attempt)
                    logger.warning(f"AI服务频率限制，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
                    time.sleep(delay)
                else:
                    logger.error("AI服务频率限制，重试次数已用完")

            except Exception as e:
                last_exception = e
                if attempt < self._max_retries - 1:
                    delay = self._retry_delay * (self._backoff_factor ** attempt)
                    logger.warning(f"AI请求失败，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒: {e}")
                    time.sleep(delay)
                else:
                    logger.error(f"AI请求失败，重试次数已用完: {e}")

        raise last_exception

    def _make_api_request(self, messages: List[Dict[str, str]],
                         temperature: Optional[float] = None,
                         max_tokens: Optional[int] = None) -> Dict[str, Any]:
        """发送API请求"""
        try:
            self._rate_limit_check()
            
            api_key = self._get_api_key()
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens,
                "stream": False
            }
            
            url = f"{self.api_base_url}/{self.api_version}/chat/completions"
            
            logger.debug(f"发送AI请求: {len(messages)} 条消息")
            
            response = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 429:
                logger.warning("AI服务频率限制")
                raise AIRateLimitError("AI服务频率限制，请稍后重试")
            
            response.raise_for_status()
            
            result = response.json()
            
            logger.debug("AI请求成功")
            return result
            
        except requests.exceptions.Timeout:
            logger.error("AI服务请求超时")
            raise AIServiceError("AI服务请求超时")
        
        except requests.exceptions.RequestException as e:
            logger.error(f"AI服务请求失败: {e}")
            raise AIServiceError(f"AI服务请求失败: {e}")
        
        except Exception as e:
            logger.error(f"AI服务调用异常: {e}")
            raise AIServiceError(f"AI服务调用异常: {e}")
    
    def _extract_response_content(self, response: Dict[str, Any]) -> str:
        """提取响应内容"""
        try:
            choices = response.get("choices", [])
            if not choices:
                raise AIServiceError("AI响应格式错误：无choices字段")
            
            message = choices[0].get("message", {})
            content = message.get("content", "")
            
            if not content:
                raise AIServiceError("AI响应内容为空")
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"提取AI响应内容失败: {e}")
            raise AIServiceError(f"提取响应内容失败: {e}")
    
    def analyze_market_for_opening(self, symbol: str, indicators_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场开仓机会"""
        try:
            # 构建开仓分析提示词
            system_prompt = """你是一个专业的加密货币交易分析师。基于提供的技术指标数据，分析市场趋势并给出开仓建议。

请严格按照以下JSON格式返回分析结果：
{
  "direction": "long/short/none",
  "confidence": 85,
  "reasoning": "详细分析理由"
}

分析要点：
1. 综合多时间周期技术指标
2. 识别趋势方向和强度
3. 评估入场时机
4. 给出置信度评分（0-100）
5. 提供详细的分析理由"""

            user_prompt = f"""请分析 {symbol} 的市场数据并给出开仓建议：

技术指标数据：
{json.dumps(indicators_data, indent=2, ensure_ascii=False)}

请基于以上数据给出专业的开仓分析。"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self._make_api_request(messages, temperature=0.1)
            content = self._extract_response_content(response)
            
            # 尝试解析JSON响应
            try:
                # 清理可能的代码块标记
                cleaned_content = content.strip()
                if cleaned_content.startswith('```json'):
                    cleaned_content = cleaned_content[7:]
                if cleaned_content.endswith('```'):
                    cleaned_content = cleaned_content[:-3]
                cleaned_content = cleaned_content.strip()

                analysis_result = json.loads(cleaned_content)
                
                # 验证响应格式
                required_fields = ["direction", "confidence", "reasoning"]
                if not all(field in analysis_result for field in required_fields):
                    raise ValueError("响应格式不完整")
                
                # 验证direction字段
                if analysis_result["direction"] not in ["long", "short", "none"]:
                    analysis_result["direction"] = "none"
                
                # 验证confidence字段
                confidence = analysis_result.get("confidence", 0)
                if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 100):
                    analysis_result["confidence"] = 0
                
                logger.info(f"AI开仓分析完成: {symbol}, 方向: {analysis_result['direction']}, 置信度: {analysis_result['confidence']}")
                
                return analysis_result
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"AI响应JSON解析失败: {e}, 原始响应: {content}")
                
                # 返回默认结果
                return {
                    "direction": "none",
                    "confidence": 0,
                    "reasoning": f"AI响应解析失败: {content[:200]}..."
                }
            
        except Exception as e:
            logger.error(f"AI开仓分析失败: {symbol}, 错误: {e}")
            raise AIServiceError(f"AI开仓分析失败: {e}")
    
    def analyze_position_for_holding(self, symbol: str, position_data: Dict[str, Any], 
                                   market_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析持仓管理"""
        try:
            # 构建持仓分析提示词
            system_prompt = """你是一个专业的仓位管理专家。基于当前持仓信息和市场数据，给出持仓管理建议。

请严格按照以下JSON格式返回分析结果：
{
  "action": "hold/close",
  "confidence": 75,
  "stop_loss": 45000,
  "take_profit": 52000,
  "reasoning": "详细分析理由"
}

分析要点：
1. 评估当前持仓的盈亏状况
2. 分析市场趋势是否发生变化
3. 判断是否需要调整止盈止损
4. 决定是否继续持有或平仓
5. 给出置信度评分（0-100）"""

            user_prompt = f"""请分析 {symbol} 的持仓管理策略：

当前持仓信息：
{json.dumps(position_data, indent=2, ensure_ascii=False)}

市场技术指标：
{json.dumps(market_data, indent=2, ensure_ascii=False)}

请基于以上数据给出专业的持仓管理建议。"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self._make_api_request(messages, temperature=0.1)
            content = self._extract_response_content(response)
            
            # 尝试解析JSON响应
            try:
                # 清理可能的代码块标记
                cleaned_content = content.strip()
                if cleaned_content.startswith('```json'):
                    cleaned_content = cleaned_content[7:]
                if cleaned_content.endswith('```'):
                    cleaned_content = cleaned_content[:-3]
                cleaned_content = cleaned_content.strip()

                analysis_result = json.loads(cleaned_content)
                
                # 验证响应格式
                required_fields = ["action", "confidence", "reasoning"]
                if not all(field in analysis_result for field in required_fields):
                    raise ValueError("响应格式不完整")
                
                # 验证action字段
                if analysis_result["action"] not in ["hold", "close"]:
                    analysis_result["action"] = "hold"
                
                # 验证confidence字段
                confidence = analysis_result.get("confidence", 0)
                if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 100):
                    analysis_result["confidence"] = 0
                
                logger.info(f"AI持仓分析完成: {symbol}, 动作: {analysis_result['action']}, 置信度: {analysis_result['confidence']}")
                
                return analysis_result
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"AI响应JSON解析失败: {e}, 原始响应: {content}")
                
                # 返回默认结果
                return {
                    "action": "hold",
                    "confidence": 0,
                    "stop_loss": None,
                    "take_profit": None,
                    "reasoning": f"AI响应解析失败: {content[:200]}..."
                }
            
        except Exception as e:
            logger.error(f"AI持仓分析失败: {symbol}, 错误: {e}")
            raise AIServiceError(f"AI持仓分析失败: {e}")
    
    def test_connection(self) -> bool:
        """测试AI服务连接"""
        try:
            messages = [
                {"role": "system", "content": "你是一个测试助手。"},
                {"role": "user", "content": "请回复'连接测试成功'"}
            ]
            
            response = self._make_api_request(messages, max_tokens=50)
            content = self._extract_response_content(response)
            
            logger.info("AI服务连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"AI服务连接测试失败: {e}")
            return False

    def analyze_market_for_opening_async(self, symbol: str, indicators_data: Dict[str, Any]) -> Future:
        """异步分析市场开仓机会"""
        return self._executor.submit(self.analyze_market_for_opening, symbol, indicators_data)

    def analyze_market_for_holding_async(self, symbol: str, position_data: Dict[str, Any],
                                       market_data: Dict[str, Any]) -> Future:
        """异步分析持仓管理"""
        return self._executor.submit(self.analyze_market_for_holding, symbol, position_data, market_data)

    def analyze_market_for_opening_cached(self, symbol: str, indicators_data: Dict[str, Any]) -> Dict[str, Any]:
        """带缓存的开仓分析"""
        # 生成缓存键
        cache_key = self._generate_cache_key([
            {"role": "system", "content": "opening_analysis"},
            {"role": "user", "content": f"{symbol}:{json.dumps(indicators_data, sort_keys=True)}"}
        ], 0.1)

        # 检查缓存
        cached_result = self._get_cache(cache_key)
        if cached_result:
            logger.debug(f"使用缓存的开仓分析结果: {symbol}")
            return cached_result

        # 执行分析
        result = self.analyze_market_for_opening(symbol, indicators_data)

        # 缓存结果
        if result.get('confidence', 0) > 0:  # 只缓存有效结果
            self._set_cache(cache_key, result)

        return result

    def clear_cache(self):
        """清除所有缓存"""
        with self._cache_lock:
            self._cache.clear()
            logger.info("AI服务缓存已清除")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._cache_lock:
            return {
                'cache_size': len(self._cache),
                'max_cache_size': self._max_cache_size,
                'cache_ttl': self._cache_ttl,
                'cache_keys': list(self._cache.keys())[:5]  # 只显示前5个键
            }

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            api_key = self._get_api_key()
            masked_key = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:] if len(api_key) > 12 else "*" * len(api_key)
            
            cache_stats = self.get_cache_stats()

            return {
                "service_name": "DeepSeek AI",
                "api_base_url": self.api_base_url,
                "model_name": self.model_name,
                "api_key_masked": masked_key,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "timeout": self.timeout,
                "rate_limit_delay": self._rate_limit_delay,
                "last_request_time": self._last_request_time,
                "max_retries": self._max_retries,
                "retry_delay": self._retry_delay,
                "backoff_factor": self._backoff_factor,
                "cache_stats": cache_stats,
                "executor_threads": self._executor._max_workers,
                "status": "ready"
            }
            
        except Exception as e:
            return {
                "service_name": "DeepSeek AI",
                "status": "error",
                "error": str(e)
            }


# 全局AI服务实例
ai_service = AIService()


def get_ai_service() -> AIService:
    """获取AI服务实例"""
    return ai_service
