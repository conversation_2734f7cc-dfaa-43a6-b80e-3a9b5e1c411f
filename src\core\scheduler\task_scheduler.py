# -*- coding: utf-8 -*-
"""
任务调度器
使用APScheduler实现定时任务调度，管理所有定时任务的执行和调度

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import traceback

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import ThreadPoolExecutor

from src.utils.logger import get_logger

logger = get_logger(__name__)


class TaskType(Enum):
    """任务类型枚举"""
    INTERVAL = "interval"    # 间隔任务
    CRON = "cron"           # 定时任务
    DATE = "date"           # 一次性任务


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 待执行
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 执行失败
    PAUSED = "paused"        # 已暂停
    REMOVED = "removed"      # 已移除


@dataclass
class TaskInfo:
    """任务信息数据结构"""
    task_id: str
    name: str
    task_type: TaskType
    func: Callable
    args: tuple
    kwargs: dict
    trigger_config: dict
    status: TaskStatus
    created_at: datetime
    last_run: Optional[datetime]
    next_run: Optional[datetime]
    run_count: int
    success_count: int
    error_count: int
    max_retries: int
    retry_count: int
    description: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'task_type': self.task_type.value,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'run_count': self.run_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'max_retries': self.max_retries,
            'retry_count': self.retry_count,
            'description': self.description,
            'success_rate': (self.success_count / self.run_count * 100) if self.run_count > 0 else 0
        }


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        """初始化任务调度器"""
        # 配置APScheduler
        jobstores = {
            'default': MemoryJobStore()
        }
        
        executors = {
            'default': ThreadPoolExecutor(max_workers=10)
        }
        
        job_defaults = {
            'coalesce': True,
            'max_instances': 1,
            'misfire_grace_time': 30
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 任务信息存储
        self.tasks = {}  # task_id -> TaskInfo
        self.task_lock = threading.RLock()
        
        # 调度器配置
        self.config = {
            'max_workers': 10,
            'default_max_retries': 3,
            'retry_delay': 60,  # 重试延迟（秒）
            'job_timeout': 300,  # 任务超时（秒）
            'cleanup_interval': 3600,  # 清理间隔（秒）
            'max_completed_tasks': 1000  # 最大完成任务数
        }
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'running_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'scheduler_start_time': None,
            'last_cleanup_time': None
        }
        
        # 添加事件监听器
        self.scheduler.add_listener(self._job_executed_listener, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._job_error_listener, EVENT_JOB_ERROR)
        self.scheduler.add_listener(self._job_missed_listener, EVENT_JOB_MISSED)
        
        logger.info("任务调度器初始化完成")
    
    def start(self):
        """启动调度器"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                self.stats['scheduler_start_time'] = datetime.now()
                
                # 添加清理任务
                self.add_interval_task(
                    task_id='cleanup_task',
                    name='清理已完成任务',
                    func=self._cleanup_completed_tasks,
                    seconds=self.config['cleanup_interval'],
                    description='定期清理已完成的任务记录'
                )
                
                logger.info("任务调度器已启动")
            else:
                logger.warning("任务调度器已经在运行")
                
        except Exception as e:
            logger.error(f"启动任务调度器失败: {e}")
            raise
    
    def stop(self, wait: bool = True):
        """停止调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown(wait=wait)
                logger.info("任务调度器已停止")
            else:
                logger.warning("任务调度器未在运行")
                
        except Exception as e:
            logger.error(f"停止任务调度器失败: {e}")
            raise
    
    def add_interval_task(self, task_id: str, name: str, func: Callable,
                         seconds: Optional[int] = None, minutes: Optional[int] = None,
                         hours: Optional[int] = None, days: Optional[int] = None,
                         args: tuple = (), kwargs: dict = None,
                         max_retries: int = None, description: str = "") -> bool:
        """添加间隔任务"""
        try:
            if kwargs is None:
                kwargs = {}
            
            if max_retries is None:
                max_retries = self.config['default_max_retries']
            
            # 检查任务是否已存在
            if task_id in self.tasks:
                logger.warning(f"任务 {task_id} 已存在")
                return False
            
            # 创建触发器配置
            trigger_config = {}
            if seconds is not None:
                trigger_config['seconds'] = seconds
            if minutes is not None:
                trigger_config['minutes'] = minutes
            if hours is not None:
                trigger_config['hours'] = hours
            if days is not None:
                trigger_config['days'] = days
            
            # 包装任务函数
            wrapped_func = self._wrap_task_function(task_id, func)
            
            # 添加到调度器
            self.scheduler.add_job(
                func=wrapped_func,
                trigger=IntervalTrigger(**trigger_config),
                id=task_id,
                name=name,
                args=args,
                kwargs=kwargs,
                max_instances=1,
                replace_existing=False
            )
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                name=name,
                task_type=TaskType.INTERVAL,
                func=func,
                args=args,
                kwargs=kwargs,
                trigger_config=trigger_config,
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                last_run=None,
                next_run=None,
                run_count=0,
                success_count=0,
                error_count=0,
                max_retries=max_retries,
                retry_count=0,
                description=description
            )
            
            with self.task_lock:
                self.tasks[task_id] = task_info
                self.stats['total_tasks'] += 1
            
            # 更新下次运行时间
            self._update_next_run_time(task_id)
            
            logger.info(f"间隔任务添加成功: {task_id} - {name}")
            return True
            
        except Exception as e:
            logger.error(f"添加间隔任务失败: {e}")
            return False
    
    def add_cron_task(self, task_id: str, name: str, func: Callable,
                     minute: str = '*', hour: str = '*', day: str = '*',
                     month: str = '*', day_of_week: str = '*',
                     args: tuple = (), kwargs: dict = None,
                     max_retries: int = None, description: str = "") -> bool:
        """添加定时任务"""
        try:
            if kwargs is None:
                kwargs = {}
            
            if max_retries is None:
                max_retries = self.config['default_max_retries']
            
            # 检查任务是否已存在
            if task_id in self.tasks:
                logger.warning(f"任务 {task_id} 已存在")
                return False
            
            # 创建触发器配置
            trigger_config = {
                'minute': minute,
                'hour': hour,
                'day': day,
                'month': month,
                'day_of_week': day_of_week
            }
            
            # 包装任务函数
            wrapped_func = self._wrap_task_function(task_id, func)
            
            # 添加到调度器
            self.scheduler.add_job(
                func=wrapped_func,
                trigger=CronTrigger(**trigger_config),
                id=task_id,
                name=name,
                args=args,
                kwargs=kwargs,
                max_instances=1,
                replace_existing=False
            )
            
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                name=name,
                task_type=TaskType.CRON,
                func=func,
                args=args,
                kwargs=kwargs,
                trigger_config=trigger_config,
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                last_run=None,
                next_run=None,
                run_count=0,
                success_count=0,
                error_count=0,
                max_retries=max_retries,
                retry_count=0,
                description=description
            )
            
            with self.task_lock:
                self.tasks[task_id] = task_info
                self.stats['total_tasks'] += 1
            
            # 更新下次运行时间
            self._update_next_run_time(task_id)
            
            logger.info(f"定时任务添加成功: {task_id} - {name}")
            return True
            
        except Exception as e:
            logger.error(f"添加定时任务失败: {e}")
            return False

    def add_date_task(self, task_id: str, name: str, func: Callable,
                     run_date: datetime, args: tuple = (), kwargs: dict = None,
                     max_retries: int = None, description: str = "") -> bool:
        """添加一次性任务"""
        try:
            if kwargs is None:
                kwargs = {}

            if max_retries is None:
                max_retries = self.config['default_max_retries']

            # 检查任务是否已存在
            if task_id in self.tasks:
                logger.warning(f"任务 {task_id} 已存在")
                return False

            # 检查运行时间是否在未来
            if run_date <= datetime.now():
                logger.error(f"运行时间必须在未来: {run_date}")
                return False

            # 创建触发器配置
            trigger_config = {
                'run_date': run_date
            }

            # 包装任务函数
            wrapped_func = self._wrap_task_function(task_id, func)

            # 添加到调度器
            self.scheduler.add_job(
                func=wrapped_func,
                trigger=DateTrigger(run_date=run_date),
                id=task_id,
                name=name,
                args=args,
                kwargs=kwargs,
                max_instances=1,
                replace_existing=False
            )

            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                name=name,
                task_type=TaskType.DATE,
                func=func,
                args=args,
                kwargs=kwargs,
                trigger_config=trigger_config,
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                last_run=None,
                next_run=run_date,
                run_count=0,
                success_count=0,
                error_count=0,
                max_retries=max_retries,
                retry_count=0,
                description=description
            )

            with self.task_lock:
                self.tasks[task_id] = task_info
                self.stats['total_tasks'] += 1

            logger.info(f"一次性任务添加成功: {task_id} - {name}")
            return True

        except Exception as e:
            logger.error(f"添加一次性任务失败: {e}")
            return False

    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        try:
            # 从调度器中移除
            if self.scheduler.get_job(task_id):
                self.scheduler.remove_job(task_id)

            # 更新任务状态
            with self.task_lock:
                if task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.REMOVED
                    logger.info(f"任务移除成功: {task_id}")
                    return True
                else:
                    logger.warning(f"任务不存在: {task_id}")
                    return False

        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            return False

    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        try:
            if self.scheduler.get_job(task_id):
                self.scheduler.pause_job(task_id)

                with self.task_lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.PAUSED

                logger.info(f"任务暂停成功: {task_id}")
                return True
            else:
                logger.warning(f"任务不存在: {task_id}")
                return False

        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            return False

    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        try:
            if self.scheduler.get_job(task_id):
                self.scheduler.resume_job(task_id)

                with self.task_lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.PENDING

                logger.info(f"任务恢复成功: {task_id}")
                return True
            else:
                logger.warning(f"任务不存在: {task_id}")
                return False

        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return False

    def modify_task(self, task_id: str, **changes) -> bool:
        """修改任务"""
        try:
            job = self.scheduler.get_job(task_id)
            if not job:
                logger.warning(f"任务不存在: {task_id}")
                return False

            # 修改任务
            job.modify(**changes)

            # 更新任务信息
            with self.task_lock:
                if task_id in self.tasks:
                    task_info = self.tasks[task_id]

                    # 更新触发器配置
                    if 'trigger' in changes:
                        trigger = changes['trigger']
                        if hasattr(trigger, '__dict__'):
                            task_info.trigger_config = trigger.__dict__.copy()

                    # 更新下次运行时间
                    self._update_next_run_time(task_id)

            logger.info(f"任务修改成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"修改任务失败: {e}")
            return False

    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        with self.task_lock:
            return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[TaskInfo]:
        """获取所有任务"""
        with self.task_lock:
            return list(self.tasks.values())

    def get_running_tasks(self) -> List[TaskInfo]:
        """获取运行中的任务"""
        with self.task_lock:
            return [task for task in self.tasks.values() if task.status == TaskStatus.RUNNING]

    def get_pending_tasks(self) -> List[TaskInfo]:
        """获取待执行的任务"""
        with self.task_lock:
            return [task for task in self.tasks.values() if task.status == TaskStatus.PENDING]

    def _wrap_task_function(self, task_id: str, func: Callable) -> Callable:
        """包装任务函数，添加监控和异常处理"""
        def wrapped_func(*args, **kwargs):
            start_time = datetime.now()

            with self.task_lock:
                if task_id in self.tasks:
                    task_info = self.tasks[task_id]
                    task_info.status = TaskStatus.RUNNING
                    task_info.last_run = start_time
                    task_info.run_count += 1
                    self.stats['running_tasks'] += 1
                    self.stats['total_executions'] += 1

            try:
                logger.info(f"开始执行任务: {task_id}")

                # 执行原始函数
                result = func(*args, **kwargs)

                # 执行成功
                with self.task_lock:
                    if task_id in self.tasks:
                        task_info = self.tasks[task_id]
                        task_info.status = TaskStatus.COMPLETED if task_info.task_type == TaskType.DATE else TaskStatus.PENDING
                        task_info.success_count += 1
                        task_info.retry_count = 0  # 重置重试计数
                        self.stats['running_tasks'] -= 1
                        self.stats['successful_executions'] += 1

                        if task_info.task_type == TaskType.DATE:
                            self.stats['completed_tasks'] += 1

                execution_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"任务执行成功: {task_id}, 耗时: {execution_time:.2f}秒")

                return result

            except Exception as e:
                # 执行失败
                error_msg = f"任务执行失败: {task_id}, 错误: {e}"
                logger.error(error_msg)
                logger.error(f"错误堆栈: {traceback.format_exc()}")

                with self.task_lock:
                    if task_id in self.tasks:
                        task_info = self.tasks[task_id]
                        task_info.error_count += 1
                        task_info.retry_count += 1
                        self.stats['running_tasks'] -= 1
                        self.stats['failed_executions'] += 1

                        # 检查是否需要重试
                        if task_info.retry_count < task_info.max_retries:
                            task_info.status = TaskStatus.PENDING
                            logger.info(f"任务将重试: {task_id}, 重试次数: {task_info.retry_count}/{task_info.max_retries}")

                            # 安排重试
                            retry_time = datetime.now() + timedelta(seconds=self.config['retry_delay'])
                            self._schedule_retry(task_id, retry_time)
                        else:
                            task_info.status = TaskStatus.FAILED
                            self.stats['failed_tasks'] += 1
                            logger.error(f"任务重试次数已达上限，标记为失败: {task_id}")

                raise

        return wrapped_func

    def _schedule_retry(self, task_id: str, retry_time: datetime):
        """安排任务重试"""
        try:
            with self.task_lock:
                if task_id in self.tasks:
                    task_info = self.tasks[task_id]

                    # 创建重试任务
                    retry_task_id = f"{task_id}_retry_{task_info.retry_count}"

                    self.scheduler.add_job(
                        func=self._wrap_task_function(task_id, task_info.func),
                        trigger=DateTrigger(run_date=retry_time),
                        id=retry_task_id,
                        name=f"{task_info.name} (重试 {task_info.retry_count})",
                        args=task_info.args,
                        kwargs=task_info.kwargs,
                        max_instances=1,
                        replace_existing=True
                    )

                    logger.info(f"重试任务已安排: {retry_task_id}, 重试时间: {retry_time}")

        except Exception as e:
            logger.error(f"安排重试失败: {e}")

    def _update_next_run_time(self, task_id: str):
        """更新任务的下次运行时间"""
        try:
            job = self.scheduler.get_job(task_id)
            if job and job.next_run_time:
                with self.task_lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].next_run = job.next_run_time.replace(tzinfo=None)

        except Exception as e:
            logger.error(f"更新下次运行时间失败: {e}")

    def _job_executed_listener(self, event):
        """任务执行完成监听器"""
        try:
            task_id = event.job_id
            logger.debug(f"任务执行完成事件: {task_id}")

            # 更新下次运行时间
            self._update_next_run_time(task_id)

        except Exception as e:
            logger.error(f"处理任务执行完成事件失败: {e}")

    def _job_error_listener(self, event):
        """任务执行错误监听器"""
        try:
            task_id = event.job_id
            exception = event.exception
            logger.error(f"任务执行错误事件: {task_id}, 异常: {exception}")

        except Exception as e:
            logger.error(f"处理任务执行错误事件失败: {e}")

    def _job_missed_listener(self, event):
        """任务错过执行监听器"""
        try:
            task_id = event.job_id
            logger.warning(f"任务错过执行事件: {task_id}")

        except Exception as e:
            logger.error(f"处理任务错过执行事件失败: {e}")

    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            logger.info("开始清理已完成的任务")

            with self.task_lock:
                completed_tasks = [
                    task_id for task_id, task_info in self.tasks.items()
                    if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.REMOVED]
                ]

                # 保留最近的任务
                max_completed = self.config['max_completed_tasks']
                if len(completed_tasks) > max_completed:
                    # 按创建时间排序，删除最旧的任务
                    sorted_tasks = sorted(
                        completed_tasks,
                        key=lambda tid: self.tasks[tid].created_at
                    )

                    tasks_to_remove = sorted_tasks[:-max_completed]

                    for task_id in tasks_to_remove:
                        del self.tasks[task_id]
                        logger.debug(f"清理已完成任务: {task_id}")

                    logger.info(f"清理了 {len(tasks_to_remove)} 个已完成任务")

                self.stats['last_cleanup_time'] = datetime.now()

        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")

    def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        try:
            with self.task_lock:
                # 计算成功率
                success_rate = 0
                if self.stats['total_executions'] > 0:
                    success_rate = (self.stats['successful_executions'] / self.stats['total_executions']) * 100

                # 计算运行时间
                uptime_seconds = 0
                if self.stats['scheduler_start_time']:
                    uptime_seconds = (datetime.now() - self.stats['scheduler_start_time']).total_seconds()

                # 统计各状态任务数量
                status_counts = {}
                for status in TaskStatus:
                    status_counts[status.value] = sum(
                        1 for task in self.tasks.values() if task.status == status
                    )

                # 统计各类型任务数量
                type_counts = {}
                for task_type in TaskType:
                    type_counts[task_type.value] = sum(
                        1 for task in self.tasks.values() if task.task_type == task_type
                    )

                return {
                    'scheduler_running': self.scheduler.running,
                    'uptime_seconds': uptime_seconds,
                    'uptime_hours': uptime_seconds / 3600,
                    'total_tasks': len(self.tasks),
                    'total_executions': self.stats['total_executions'],
                    'successful_executions': self.stats['successful_executions'],
                    'failed_executions': self.stats['failed_executions'],
                    'success_rate': round(success_rate, 2),
                    'running_tasks': self.stats['running_tasks'],
                    'scheduler_start_time': self.stats['scheduler_start_time'].isoformat() if self.stats['scheduler_start_time'] else None,
                    'last_cleanup_time': self.stats['last_cleanup_time'].isoformat() if self.stats['last_cleanup_time'] else None,
                    'status_counts': status_counts,
                    'type_counts': type_counts,
                    'config': self.config.copy()
                }

        except Exception as e:
            logger.error(f"获取调度器统计失败: {e}")
            return {}

    def get_task_summary(self) -> Dict[str, Any]:
        """获取任务汇总信息"""
        try:
            with self.task_lock:
                tasks_data = []

                for task_info in self.tasks.values():
                    task_dict = task_info.to_dict()
                    tasks_data.append(task_dict)

                # 按下次运行时间排序
                tasks_data.sort(key=lambda x: x['next_run'] or '9999-12-31T23:59:59')

                return {
                    'total_tasks': len(tasks_data),
                    'tasks': tasks_data
                }

        except Exception as e:
            logger.error(f"获取任务汇总失败: {e}")
            return {'total_tasks': 0, 'tasks': []}

    def update_config(self, new_config: Dict[str, Any]):
        """更新调度器配置"""
        try:
            valid_keys = {
                'max_workers', 'default_max_retries', 'retry_delay',
                'job_timeout', 'cleanup_interval', 'max_completed_tasks'
            }

            for key, value in new_config.items():
                if key in valid_keys:
                    if isinstance(value, (int, float)) and value > 0:
                        self.config[key] = value
                        logger.info(f"配置更新: {key} = {value}")
                    else:
                        logger.warning(f"配置项 {key} 的值无效: {value}")

            logger.info("调度器配置已更新")

        except Exception as e:
            logger.error(f"更新配置失败: {e}")

    def clear_completed_tasks(self):
        """手动清理已完成任务"""
        try:
            self._cleanup_completed_tasks()
            logger.info("手动清理已完成任务完成")

        except Exception as e:
            logger.error(f"手动清理已完成任务失败: {e}")

    def reset_stats(self):
        """重置统计信息"""
        try:
            start_time = self.stats.get('scheduler_start_time')

            self.stats = {
                'total_tasks': len(self.tasks),
                'running_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'scheduler_start_time': start_time,
                'last_cleanup_time': None
            }

            logger.info("调度器统计信息已重置")

        except Exception as e:
            logger.error(f"重置统计信息失败: {e}")


# 全局任务调度器实例
_task_scheduler_instance = None


def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器实例"""
    global _task_scheduler_instance
    if _task_scheduler_instance is None:
        _task_scheduler_instance = TaskScheduler()
    return _task_scheduler_instance
