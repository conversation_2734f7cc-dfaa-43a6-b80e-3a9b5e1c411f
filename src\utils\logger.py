# -*- coding: utf-8 -*-
"""
日志管理模块
实现统一的日志管理系统，支持分级日志、文件轮转和结构化日志记录

Author: SuperBot Team
Date: 2025-01-04
"""

import os
import sys
import json
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import threading

from src.utils.config import get_config_manager
from src.data.database import get_db_manager


class JsonFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录为JSON格式
        
        Args:
            record: 日志记录
            
        Returns:
            str: JSON格式的日志字符串
        """
        # 基础日志信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
            'thread_id': record.thread,
            'thread_name': record.threadName,
            'process_id': record.process,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_data['extra'] = record.extra_data
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def __init__(self):
        """初始化数据库日志处理器"""
        super().__init__()
        self._db = get_db_manager()
        self._lock = threading.Lock()
    
    def emit(self, record: logging.LogRecord):
        """
        发送日志记录到数据库
        
        Args:
            record: 日志记录
        """
        try:
            with self._lock:
                # 格式化消息
                message = self.format(record)
                
                # 准备详细信息
                details = None
                if record.exc_info:
                    import traceback
                    details = ''.join(traceback.format_exception(*record.exc_info))
                elif hasattr(record, 'extra_data'):
                    details = json.dumps(record.extra_data, ensure_ascii=False)
                
                # 插入数据库
                self._db.execute_insert(
                    """INSERT INTO system_logs (level, module, message, details) 
                       VALUES (?, ?, ?, ?)""",
                    (record.levelname, record.module, message, details)
                )
                
        except Exception:
            # 避免日志记录失败导致程序崩溃
            self.handleError(record)


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        """初始化日志管理器"""
        self._config = get_config_manager()
        self._loggers: Dict[str, logging.Logger] = {}
        self._initialized = False
        self._lock = threading.Lock()
        
        # 初始化日志系统
        self._initialize_logging()
    
    def _initialize_logging(self):
        """初始化日志系统"""
        with self._lock:
            if self._initialized:
                return
            
            try:
                # 创建日志目录
                log_dir = Path('data/logs')
                log_dir.mkdir(parents=True, exist_ok=True)
                
                # 获取日志配置
                log_level = self._config.get('log_level', 'INFO')
                
                # 设置根日志器
                root_logger = logging.getLogger()
                root_logger.setLevel(getattr(logging, log_level.upper()))
                
                # 清除现有处理器
                root_logger.handlers.clear()
                
                # 创建格式化器
                console_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                
                json_formatter = JsonFormatter()
                
                # 控制台处理器
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setLevel(logging.INFO)
                console_handler.setFormatter(console_formatter)
                root_logger.addHandler(console_handler)
                
                # 应用日志文件处理器（轮转）
                app_handler = logging.handlers.RotatingFileHandler(
                    log_dir / 'app.log',
                    maxBytes=10 * 1024 * 1024,  # 10MB
                    backupCount=5,
                    encoding='utf-8'
                )
                app_handler.setLevel(logging.DEBUG)
                app_handler.setFormatter(console_formatter)
                root_logger.addHandler(app_handler)
                
                # 交易日志文件处理器（JSON格式）
                trading_handler = logging.handlers.RotatingFileHandler(
                    log_dir / 'trading.log',
                    maxBytes=50 * 1024 * 1024,  # 50MB
                    backupCount=10,
                    encoding='utf-8'
                )
                trading_handler.setLevel(logging.INFO)
                trading_handler.setFormatter(json_formatter)
                
                # 错误日志文件处理器
                error_handler = logging.handlers.RotatingFileHandler(
                    log_dir / 'error.log',
                    maxBytes=20 * 1024 * 1024,  # 20MB
                    backupCount=10,
                    encoding='utf-8'
                )
                error_handler.setLevel(logging.ERROR)
                error_handler.setFormatter(json_formatter)
                root_logger.addHandler(error_handler)
                
                # 数据库日志处理器（仅记录重要日志）
                try:
                    db_handler = DatabaseLogHandler()
                    db_handler.setLevel(logging.WARNING)
                    db_handler.setFormatter(console_formatter)
                    root_logger.addHandler(db_handler)
                except Exception as e:
                    print(f"数据库日志处理器初始化失败: {e}")
                
                # 创建专用日志器
                self._create_specialized_loggers(trading_handler)
                
                self._initialized = True
                
                # 记录初始化完成
                logger = self.get_logger(__name__)
                logger.info("日志系统初始化完成")
                logger.info(f"日志级别: {log_level}")
                logger.info(f"日志目录: {log_dir.absolute()}")
                
            except Exception as e:
                print(f"日志系统初始化失败: {e}")
                raise
    
    def _create_specialized_loggers(self, trading_handler):
        """创建专用日志器"""
        # 交易日志器
        trading_logger = logging.getLogger('trading')
        trading_logger.setLevel(logging.INFO)
        trading_logger.addHandler(trading_handler)
        trading_logger.propagate = False  # 不传播到根日志器
        
        # AI分析日志器
        ai_logger = logging.getLogger('ai_analysis')
        ai_logger.setLevel(logging.INFO)
        ai_logger.addHandler(trading_handler)
        ai_logger.propagate = False
        
        # 市场数据日志器
        market_logger = logging.getLogger('market_data')
        market_logger.setLevel(logging.INFO)
        market_logger.addHandler(trading_handler)
        market_logger.propagate = False
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        if not self._initialized:
            self._initialize_logging()
        
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        
        return self._loggers[name]
    
    def log_trading_event(self, event_type: str, symbol: str, data: Dict[str, Any]):
        """
        记录交易事件
        
        Args:
            event_type: 事件类型
            symbol: 交易对
            data: 事件数据
        """
        logger = self.get_logger('trading')
        
        # 创建结构化日志数据
        log_data = {
            'event_type': event_type,
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            **data
        }
        
        # 使用extra参数传递结构化数据
        logger.info(f"交易事件: {event_type} - {symbol}", extra={'extra_data': log_data})
    
    def log_ai_analysis(self, symbol: str, analysis_type: str, result: Dict[str, Any]):
        """
        记录AI分析结果
        
        Args:
            symbol: 交易对
            analysis_type: 分析类型
            result: 分析结果
        """
        logger = self.get_logger('ai_analysis')
        
        log_data = {
            'symbol': symbol,
            'analysis_type': analysis_type,
            'timestamp': datetime.now().isoformat(),
            'result': result
        }
        
        logger.info(f"AI分析: {analysis_type} - {symbol}", extra={'extra_data': log_data})
    
    def log_market_data(self, symbol: str, data_type: str, data: Dict[str, Any]):
        """
        记录市场数据
        
        Args:
            symbol: 交易对
            data_type: 数据类型
            data: 市场数据
        """
        logger = self.get_logger('market_data')
        
        log_data = {
            'symbol': symbol,
            'data_type': data_type,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        logger.debug(f"市场数据: {data_type} - {symbol}", extra={'extra_data': log_data})
    
    def log_error(self, module: str, error: Exception, context: Dict[str, Any] = None):
        """
        记录错误信息
        
        Args:
            module: 模块名称
            error: 异常对象
            context: 上下文信息
        """
        logger = self.get_logger(module)
        
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'module': module,
            'timestamp': datetime.now().isoformat(),
            'context': context or {}
        }
        
        logger.error(f"错误: {type(error).__name__} - {str(error)}", 
                    exc_info=True, extra={'extra_data': error_data})
    
    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志文件
        
        Args:
            days: 保留天数
        """
        try:
            log_dir = Path('data/logs')
            if not log_dir.exists():
                return
            
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            for log_file in log_dir.glob('*.log*'):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    print(f"删除旧日志文件: {log_file}")
            
        except Exception as e:
            print(f"清理日志文件失败: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            Dict[str, Any]: 日志统计信息
        """
        try:
            log_dir = Path('data/logs')
            if not log_dir.exists():
                return {}
            
            stats = {}
            for log_file in log_dir.glob('*.log'):
                if log_file.is_file():
                    stat = log_file.stat()
                    stats[log_file.name] = {
                        'size_mb': round(stat.st_size / (1024 * 1024), 2),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    }
            
            return stats
            
        except Exception as e:
            print(f"获取日志统计失败: {e}")
            return {}


# 全局日志管理器实例
logger_manager = LoggerManager()


def get_logger_manager() -> LoggerManager:
    """获取日志管理器实例"""
    return logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name)


def log_trading_event(event_type: str, symbol: str, data: Dict[str, Any]):
    """记录交易事件的便捷函数"""
    logger_manager.log_trading_event(event_type, symbol, data)


def log_ai_analysis(symbol: str, analysis_type: str, result: Dict[str, Any]):
    """记录AI分析的便捷函数"""
    logger_manager.log_ai_analysis(symbol, analysis_type, result)


def log_error(module: str, error: Exception, context: Dict[str, Any] = None):
    """记录错误的便捷函数"""
    logger_manager.log_error(module, error, context)
