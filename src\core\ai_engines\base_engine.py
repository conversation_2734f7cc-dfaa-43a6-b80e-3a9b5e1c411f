# -*- coding: utf-8 -*-
"""
AI引擎基类
定义AI引擎的抽象基类和公共功能，为开仓和持仓引擎提供统一的基础功能

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import time
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from src.services.ai_service import get_ai_service
from src.core.analysis import get_technical_analysis_engine
from src.data.repositories.config_repository import get_config_repository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EngineType(Enum):
    """AI引擎类型枚举"""
    OPEN = "open"
    HOLD = "hold"


class AnalysisResult(Enum):
    """分析结果枚举"""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    NEUTRAL = "NEUTRAL"
    ERROR = "ERROR"


@dataclass
class AIAnalysisRequest:
    """AI分析请求数据结构"""
    symbol: str
    exchange: str
    engine_type: EngineType
    technical_data: Dict[str, Any]
    market_context: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'exchange': self.exchange,
            'engine_type': self.engine_type.value,
            'technical_data': self.technical_data,
            'market_context': self.market_context,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class AIAnalysisResponse:
    """AI分析响应数据结构"""
    direction: AnalysisResult
    confidence: float
    reasoning: str
    additional_data: Dict[str, Any]
    processing_time: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'direction': self.direction.value,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'additional_data': self.additional_data,
            'processing_time': self.processing_time,
            'timestamp': self.timestamp.isoformat(),
            'is_valid': self.is_valid()
        }
    
    def is_valid(self) -> bool:
        """检查分析结果是否有效"""
        return (
            self.direction != AnalysisResult.ERROR and
            0 <= self.confidence <= 100 and
            len(self.reasoning) > 0
        )


class AIEngineError(Exception):
    """AI引擎错误"""
    pass


class BaseAIEngine(ABC):
    """AI引擎抽象基类"""
    
    def __init__(self, engine_type: EngineType):
        """初始化AI引擎基类
        
        Args:
            engine_type: 引擎类型（开仓/持仓）
        """
        self.engine_type = engine_type
        self.ai_service = get_ai_service()
        self.technical_engine = get_technical_analysis_engine()
        self.config_repo = get_config_repository()
        
        # 引擎配置
        self.config = self._load_engine_config()
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_processing_time': 0.0,
            'last_request_time': None
        }
        
        logger.info(f"{self.engine_type.value.upper()}引擎初始化完成")
    
    def _load_engine_config(self) -> Dict[str, Any]:
        """加载引擎配置"""
        try:
            # 默认配置
            default_config = {
                'confidence_threshold': 70.0,
                'max_retries': 3,
                'timeout_seconds': 30,
                'cache_ttl_seconds': 300,
                'enable_cache': True,
                'enable_validation': True,
                'max_reasoning_length': 1000,
                'min_confidence': 0.0,
                'max_confidence': 100.0
            }
            
            # 从数据库加载配置
            db_config = self.config_repo.get_ai_engine_config(self.engine_type.value)
            if db_config:
                default_config.update(db_config)
            
            return default_config
            
        except Exception as e:
            logger.warning(f"加载引擎配置失败，使用默认配置: {e}")
            return {
                'confidence_threshold': 70.0,
                'max_retries': 3,
                'timeout_seconds': 30,
                'cache_ttl_seconds': 300,
                'enable_cache': True,
                'enable_validation': True,
                'max_reasoning_length': 1000,
                'min_confidence': 0.0,
                'max_confidence': 100.0
            }
    
    def _validate_request(self, request: AIAnalysisRequest) -> bool:
        """验证分析请求"""
        try:
            if not self.config.get('enable_validation', True):
                return True
            
            # 验证必需字段
            if not request.symbol or not request.exchange:
                raise AIEngineError("缺少必需的交易对或交易所信息")
            
            if not request.technical_data:
                raise AIEngineError("缺少技术分析数据")
            
            # 验证技术数据结构
            if 'timeframes' not in request.technical_data:
                raise AIEngineError("技术数据缺少时间周期信息")
            
            # 验证时间戳
            if not request.timestamp:
                raise AIEngineError("缺少时间戳信息")
            
            # 检查数据时效性（不超过10分钟）
            time_diff = datetime.now() - request.timestamp
            if time_diff.total_seconds() > 600:
                logger.warning(f"技术数据可能过时: {time_diff.total_seconds():.1f}秒")
            
            return True
            
        except Exception as e:
            logger.error(f"请求验证失败: {e}")
            raise AIEngineError(f"请求验证失败: {e}")
    
    def _preprocess_technical_data(self, technical_data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理技术分析数据"""
        try:
            processed_data = {
                'symbol': technical_data.get('symbol', 'UNKNOWN'),
                'timestamp': technical_data.get('timestamp', datetime.now().isoformat()),
                'timeframes': {},
                'summary': technical_data.get('multi_timeframe_summary', {})
            }
            
            # 处理各时间周期数据
            timeframes_data = technical_data.get('timeframes', {})
            for timeframe, tf_data in timeframes_data.items():
                if 'error' in tf_data:
                    logger.warning(f"跳过错误的时间周期数据: {timeframe}")
                    continue
                
                # 提取关键指标
                processed_tf = {
                    'timeframe': timeframe,
                    'current_price': tf_data.get('current_price'),
                    'price_change': tf_data.get('price_change'),
                    'price_change_pct': tf_data.get('price_change_pct'),
                    'indicators': self._extract_key_indicators(tf_data)
                }
                
                processed_data['timeframes'][timeframe] = processed_tf
            
            return processed_data
            
        except Exception as e:
            logger.error(f"技术数据预处理失败: {e}")
            raise AIEngineError(f"技术数据预处理失败: {e}")
    
    def _extract_key_indicators(self, timeframe_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键技术指标"""
        try:
            indicators = {}
            
            # 趋势指标
            trend_indicators = timeframe_data.get('trend_indicators', {})
            if trend_indicators:
                indicators['trend'] = {
                    'sma_20': self._get_indicator_value(trend_indicators, 'sma_20'),
                    'ema_20': self._get_indicator_value(trend_indicators, 'ema_20'),
                    'macd_line': self._get_nested_indicator_value(trend_indicators, 'macd', 'macd'),
                    'macd_signal': self._get_nested_indicator_value(trend_indicators, 'macd', 'signal'),
                    'adx': self._get_indicator_value(trend_indicators, 'adx')
                }
            
            # 震荡指标
            momentum_indicators = timeframe_data.get('momentum_indicators', {})
            if momentum_indicators:
                indicators['momentum'] = {
                    'rsi': self._get_indicator_value(momentum_indicators, 'rsi'),
                    'stoch_k': self._get_nested_indicator_value(momentum_indicators, 'stoch', 'k'),
                    'stoch_d': self._get_nested_indicator_value(momentum_indicators, 'stoch', 'd'),
                    'cci': self._get_indicator_value(momentum_indicators, 'cci'),
                    'willr': self._get_indicator_value(momentum_indicators, 'willr')
                }
            
            # 成交量指标
            volume_indicators = timeframe_data.get('volume_indicators', {})
            if volume_indicators:
                indicators['volume'] = {
                    'obv': self._get_indicator_value(volume_indicators, 'obv'),
                    'ad': self._get_indicator_value(volume_indicators, 'ad'),
                    'adosc': self._get_indicator_value(volume_indicators, 'adosc')
                }
            
            # 波动率指标
            volatility_indicators = timeframe_data.get('volatility_indicators', {})
            if volatility_indicators:
                indicators['volatility'] = {
                    'atr': self._get_indicator_value(volatility_indicators, 'atr'),
                    'natr': self._get_indicator_value(volatility_indicators, 'natr'),
                    'bb_upper': self._get_nested_indicator_value(volatility_indicators, 'bbands', 'upper'),
                    'bb_middle': self._get_nested_indicator_value(volatility_indicators, 'bbands', 'middle'),
                    'bb_lower': self._get_nested_indicator_value(volatility_indicators, 'bbands', 'lower')
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"提取关键指标失败: {e}")
            return {}
    
    def _get_indicator_value(self, indicators: Dict[str, Any], key: str) -> Optional[float]:
        """获取指标值"""
        try:
            indicator_data = indicators.get(key, {})
            if isinstance(indicator_data, dict):
                return indicator_data.get('current')
            return None
        except Exception:
            return None
    
    def _get_nested_indicator_value(self, indicators: Dict[str, Any], 
                                  parent_key: str, child_key: str) -> Optional[float]:
        """获取嵌套指标值"""
        try:
            parent_data = indicators.get(parent_key, {})
            if isinstance(parent_data, dict):
                child_data = parent_data.get(child_key, {})
                if isinstance(child_data, dict):
                    return child_data.get('current')
            return None
        except Exception:
            return None

    def _call_ai_service(self, prompt: str, request: AIAnalysisRequest) -> AIAnalysisResponse:
        """调用AI服务"""
        start_time = time.time()

        try:
            # 更新统计
            self.stats['total_requests'] += 1
            self.stats['last_request_time'] = datetime.now()

            # 调用AI服务
            ai_response = self.ai_service.analyze_market(
                prompt=prompt,
                timeout=self.config.get('timeout_seconds', 30)
            )

            # 解析AI响应
            parsed_response = self._parse_ai_response(ai_response)

            # 验证响应
            if self.config.get('enable_validation', True):
                self._validate_response(parsed_response)

            # 创建响应对象
            processing_time = time.time() - start_time
            response = AIAnalysisResponse(
                direction=parsed_response['direction'],
                confidence=parsed_response['confidence'],
                reasoning=parsed_response['reasoning'],
                additional_data=parsed_response.get('additional_data', {}),
                processing_time=processing_time,
                timestamp=datetime.now()
            )

            # 更新成功统计
            self.stats['successful_requests'] += 1
            self._update_average_processing_time(processing_time)

            logger.info(f"{self.engine_type.value}引擎AI分析完成: {response.direction.value}, 置信度: {response.confidence}")
            return response

        except Exception as e:
            # 更新失败统计
            self.stats['failed_requests'] += 1
            processing_time = time.time() - start_time

            logger.error(f"{self.engine_type.value}引擎AI分析失败: {e}")

            # 返回错误响应
            return AIAnalysisResponse(
                direction=AnalysisResult.ERROR,
                confidence=0.0,
                reasoning=f"AI分析失败: {str(e)}",
                additional_data={'error': str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )

    def _parse_ai_response(self, ai_response: str) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试解析JSON
            if isinstance(ai_response, str):
                # 提取JSON部分（可能包含其他文本）
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1

                if json_start >= 0 and json_end > json_start:
                    json_str = ai_response[json_start:json_end]
                    parsed = json.loads(json_str)
                else:
                    raise AIEngineError("AI响应中未找到有效的JSON格式")
            else:
                parsed = ai_response

            # 验证必需字段
            required_fields = ['direction', 'confidence', 'reasoning']
            for field in required_fields:
                if field not in parsed:
                    raise AIEngineError(f"AI响应缺少必需字段: {field}")

            # 标准化方向值
            direction_str = str(parsed['direction']).upper()
            if direction_str in ['LONG', 'BUY', 'BULLISH']:
                parsed['direction'] = AnalysisResult.BULLISH
            elif direction_str in ['SHORT', 'SELL', 'BEARISH']:
                parsed['direction'] = AnalysisResult.BEARISH
            elif direction_str in ['NONE', 'HOLD', 'NEUTRAL']:
                parsed['direction'] = AnalysisResult.NEUTRAL
            else:
                raise AIEngineError(f"无效的方向值: {direction_str}")

            # 标准化置信度值
            confidence = float(parsed['confidence'])
            if not (0 <= confidence <= 100):
                raise AIEngineError(f"置信度值超出范围: {confidence}")
            parsed['confidence'] = confidence

            # 验证推理文本
            reasoning = str(parsed['reasoning']).strip()
            if len(reasoning) == 0:
                raise AIEngineError("推理文本为空")

            max_length = self.config.get('max_reasoning_length', 1000)
            if len(reasoning) > max_length:
                reasoning = reasoning[:max_length] + "..."
            parsed['reasoning'] = reasoning

            return parsed

        except json.JSONDecodeError as e:
            raise AIEngineError(f"AI响应JSON解析失败: {e}")
        except Exception as e:
            raise AIEngineError(f"AI响应解析失败: {e}")

    def _validate_response(self, parsed_response: Dict[str, Any]) -> bool:
        """验证AI响应"""
        try:
            # 检查方向
            if not isinstance(parsed_response['direction'], AnalysisResult):
                raise AIEngineError("无效的分析方向")

            # 检查置信度范围
            confidence = parsed_response['confidence']
            min_conf = self.config.get('min_confidence', 0.0)
            max_conf = self.config.get('max_confidence', 100.0)

            if not (min_conf <= confidence <= max_conf):
                raise AIEngineError(f"置信度超出配置范围: {confidence}")

            # 检查推理文本长度
            reasoning = parsed_response['reasoning']
            if len(reasoning) < 10:
                raise AIEngineError("推理文本过短")

            return True

        except Exception as e:
            logger.error(f"响应验证失败: {e}")
            raise AIEngineError(f"响应验证失败: {e}")

    def _update_average_processing_time(self, processing_time: float):
        """更新平均处理时间"""
        try:
            current_avg = self.stats['average_processing_time']
            successful_count = self.stats['successful_requests']

            if successful_count == 1:
                self.stats['average_processing_time'] = processing_time
            else:
                # 计算移动平均
                self.stats['average_processing_time'] = (
                    (current_avg * (successful_count - 1) + processing_time) / successful_count
                )
        except Exception as e:
            logger.warning(f"更新平均处理时间失败: {e}")

    def _should_retry(self, attempt: int, error: Exception) -> bool:
        """判断是否应该重试"""
        max_retries = self.config.get('max_retries', 3)

        if attempt >= max_retries:
            return False

        # 某些错误不应该重试
        if isinstance(error, AIEngineError):
            error_msg = str(error).lower()
            if any(keyword in error_msg for keyword in ['invalid', '无效', 'format', '格式']):
                return False

        return True

    @abstractmethod
    def _generate_prompt(self, request: AIAnalysisRequest) -> str:
        """生成AI提示词（子类必须实现）"""
        pass

    @abstractmethod
    def _post_process_response(self, response: AIAnalysisResponse,
                             request: AIAnalysisRequest) -> AIAnalysisResponse:
        """后处理AI响应（子类可选实现）"""
        return response

    def analyze(self, symbol: str, exchange: str = "okx",
               market_context: Optional[Dict[str, Any]] = None) -> AIAnalysisResponse:
        """执行AI分析（主要接口方法）"""
        try:
            # 获取技术分析数据
            technical_data = self.technical_engine.get_ai_analysis_data(exchange, symbol, self.engine_type.value)

            # 创建分析请求
            request = AIAnalysisRequest(
                symbol=symbol,
                exchange=exchange,
                engine_type=self.engine_type,
                technical_data=technical_data['raw_analysis'],
                market_context=market_context or {},
                timestamp=datetime.now()
            )

            # 验证请求
            self._validate_request(request)

            # 预处理技术数据
            processed_data = self._preprocess_technical_data(request.technical_data)
            request.technical_data = processed_data

            # 生成提示词
            prompt = self._generate_prompt(request)

            # 执行AI分析（带重试）
            last_error = None
            for attempt in range(self.config.get('max_retries', 3) + 1):
                try:
                    response = self._call_ai_service(prompt, request)

                    if response.direction != AnalysisResult.ERROR:
                        # 后处理响应
                        response = self._post_process_response(response, request)
                        return response
                    else:
                        last_error = Exception(response.reasoning)

                except Exception as e:
                    last_error = e
                    if not self._should_retry(attempt, e):
                        break

                    # 等待后重试
                    if attempt < self.config.get('max_retries', 3):
                        wait_time = (attempt + 1) * 2  # 指数退避
                        logger.warning(f"AI分析失败，{wait_time}秒后重试 (尝试 {attempt + 1}): {e}")
                        time.sleep(wait_time)

            # 所有重试都失败
            raise AIEngineError(f"AI分析最终失败: {last_error}")

        except Exception as e:
            logger.error(f"{self.engine_type.value}引擎分析失败: {e}")
            return AIAnalysisResponse(
                direction=AnalysisResult.ERROR,
                confidence=0.0,
                reasoning=f"分析失败: {str(e)}",
                additional_data={'error': str(e)},
                processing_time=0.0,
                timestamp=datetime.now()
            )

    def get_engine_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            'engine_type': self.engine_type.value,
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': (
                self.stats['successful_requests'] / max(self.stats['total_requests'], 1) * 100
            ),
            'average_processing_time': self.stats['average_processing_time'],
            'last_request_time': (
                self.stats['last_request_time'].isoformat()
                if self.stats['last_request_time'] else None
            ),
            'config': self.config
        }

    def reset_stats(self):
        """重置引擎统计"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_processing_time': 0.0,
            'last_request_time': None
        }
        logger.info(f"{self.engine_type.value}引擎统计已重置")

    def update_config(self, new_config: Dict[str, Any]):
        """更新引擎配置"""
        try:
            # 验证配置参数
            valid_keys = {
                'confidence_threshold', 'max_retries', 'timeout_seconds',
                'cache_ttl_seconds', 'enable_cache', 'enable_validation',
                'max_reasoning_length', 'min_confidence', 'max_confidence'
            }

            for key, value in new_config.items():
                if key not in valid_keys:
                    logger.warning(f"忽略无效的配置键: {key}")
                    continue

                # 类型验证
                if key in ['confidence_threshold', 'timeout_seconds', 'cache_ttl_seconds',
                          'max_reasoning_length', 'min_confidence', 'max_confidence']:
                    if not isinstance(value, (int, float)):
                        logger.warning(f"配置 {key} 的值类型无效: {type(value)}")
                        continue

                if key in ['max_retries']:
                    if not isinstance(value, int) or value < 0:
                        logger.warning(f"配置 {key} 的值无效: {value}")
                        continue

                if key in ['enable_cache', 'enable_validation']:
                    if not isinstance(value, bool):
                        logger.warning(f"配置 {key} 的值类型无效: {type(value)}")
                        continue

                self.config[key] = value

            # 保存到数据库
            self.config_repo.save_ai_engine_config(self.engine_type.value, self.config)

            logger.info(f"{self.engine_type.value}引擎配置已更新")

        except Exception as e:
            logger.error(f"更新引擎配置失败: {e}")
            raise AIEngineError(f"更新引擎配置失败: {e}")

    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            'engine_type': self.engine_type.value,
            'status': 'ready' if self.ai_service and self.technical_engine else 'error',
            'config_loaded': bool(self.config),
            'ai_service_available': bool(self.ai_service),
            'technical_engine_available': bool(self.technical_engine),
            'last_analysis_time': (
                self.stats['last_request_time'].isoformat()
                if self.stats['last_request_time'] else None
            ),
            'total_analyses': self.stats['total_requests'],
            'success_rate': (
                self.stats['successful_requests'] / max(self.stats['total_requests'], 1) * 100
            )
        }
