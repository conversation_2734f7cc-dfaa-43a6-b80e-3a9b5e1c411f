# -*- coding: utf-8 -*-
"""
任务调度模块
包含任务调度器和定时任务定义

Author: SuperBot Team
Date: 2025-01-04
"""

from .task_scheduler import (
    TaskScheduler,
    TaskInfo,
    TaskType,
    TaskStatus,
    get_task_scheduler
)

from .jobs import (
    market_analysis_job,
    position_monitoring_job,
    risk_assessment_job,
    system_health_check_job,
    data_cleanup_job,
    performance_report_job,
    trading_execution_job,
    market_data_update_job,
    system_maintenance_job,
    TaskDependencyManager,
    setup_scheduled_jobs,
    get_job_statistics,
    SCHEDULED_JOBS
)

__all__ = [
    # 任务调度器
    'TaskScheduler',
    'get_task_scheduler',

    # 数据结构
    'TaskInfo',

    # 枚举类型
    'TaskType',
    'TaskStatus',

    # 定时任务
    'market_analysis_job',
    'position_monitoring_job',
    'risk_assessment_job',
    'system_health_check_job',
    'data_cleanup_job',
    'performance_report_job',
    'trading_execution_job',
    'market_data_update_job',
    'system_maintenance_job',

    # 任务管理
    'TaskDependencyManager',
    'setup_scheduled_jobs',
    'get_job_statistics',
    'SCHEDULED_JOBS'
]
