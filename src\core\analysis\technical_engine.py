# -*- coding: utf-8 -*-
"""
技术分析引擎
整合技术指标计算和市场数据分析

Author: SuperBot Team
Date: 2025-01-04
"""

import numpy as np
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from threading import Lock
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.core.analysis.indicators import get_technical_indicators, IndicatorError
from src.services.market_data_service import get_market_data_service
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TechnicalAnalysisError(Exception):
    """技术分析错误"""
    pass


class TechnicalAnalysisEngine:
    """技术分析引擎"""
    
    def __init__(self):
        """初始化技术分析引擎"""
        self.indicators_calculator = get_technical_indicators()
        self.market_data_service = get_market_data_service()
        
        # 线程池用于并发分析
        self._executor = ThreadPoolExecutor(max_workers=6)
        
        # 分析结果缓存
        self._analysis_cache: Dict[str, Dict] = {}
        self._cache_lock = Lock()
        self._cache_ttl = 60  # 缓存1分钟
        
        # 支持的时间周期
        self.supported_timeframes = ['1m', '5m', '15m', '1h']
        
        logger.info("技术分析引擎初始化完成")
    
    def _generate_cache_key(self, exchange: str, symbol: str, timeframe: str) -> str:
        """生成缓存键"""
        return f"{exchange}:{symbol}:{timeframe}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get('timestamp', 0)
        return datetime.now().timestamp() - cache_time < self._cache_ttl
    
    def _set_cache(self, cache_key: str, data: Dict):
        """设置缓存"""
        with self._cache_lock:
            self._analysis_cache[cache_key] = {
                'data': data,
                'timestamp': datetime.now().timestamp()
            }
    
    def _get_cache(self, cache_key: str) -> Optional[Dict]:
        """获取缓存"""
        with self._cache_lock:
            cache_entry = self._analysis_cache.get(cache_key)
            if self._is_cache_valid(cache_entry):
                return cache_entry['data']
        return None
    
    def _prepare_ohlcv_arrays(self, ohlcv_data: List[List]) -> Tuple[np.ndarray, ...]:
        """准备OHLCV数组"""
        try:
            if not ohlcv_data or len(ohlcv_data) < 20:
                raise TechnicalAnalysisError("数据点不足，无法进行技术分析")
            
            # 转换为numpy数组
            data_array = np.array(ohlcv_data)
            
            timestamps = data_array[:, 0]
            opens = data_array[:, 1].astype(float)
            highs = data_array[:, 2].astype(float)
            lows = data_array[:, 3].astype(float)
            closes = data_array[:, 4].astype(float)
            volumes = data_array[:, 5].astype(float)
            
            return timestamps, opens, highs, lows, closes, volumes
            
        except Exception as e:
            logger.error(f"准备OHLCV数组失败: {e}")
            raise TechnicalAnalysisError(f"准备OHLCV数组失败: {e}")
    
    def analyze_single_timeframe(self, exchange: str, symbol: str, timeframe: str, 
                                limit: int = 100, use_cache: bool = True) -> Dict[str, Any]:
        """分析单个时间周期"""
        try:
            cache_key = self._generate_cache_key(exchange, symbol, timeframe)
            
            # 检查缓存
            if use_cache:
                cached_result = self._get_cache(cache_key)
                if cached_result:
                    logger.debug(f"使用缓存的技术分析结果: {cache_key}")
                    return cached_result
            
            # 获取市场数据
            ohlcv_data = self.market_data_service.get_ohlcv_data(
                exchange, symbol, timeframe, limit, use_cache
            )
            
            # 准备数据数组
            timestamps, opens, highs, lows, closes, volumes = self._prepare_ohlcv_arrays(ohlcv_data)
            
            # 计算技术指标
            indicators_result = self.indicators_calculator.calculate_all_indicators(
                highs, lows, closes, volumes
            )
            
            # 添加基本信息
            indicators_result.update({
                'exchange': exchange,
                'symbol': symbol,
                'timeframe': timeframe,
                'data_start_time': datetime.fromtimestamp(timestamps[0] / 1000).isoformat(),
                'data_end_time': datetime.fromtimestamp(timestamps[-1] / 1000).isoformat(),
            })
            
            # 获取指标摘要
            summary = self.indicators_calculator.get_indicator_summary(indicators_result)
            indicators_result['summary'] = summary
            
            # 验证指标数据
            validation = self.indicators_calculator.validate_indicator_data(indicators_result)
            indicators_result['validation'] = validation
            
            # 缓存结果
            if use_cache and validation.get('is_valid', False):
                self._set_cache(cache_key, indicators_result)
            
            logger.info(f"单时间周期技术分析完成: {symbol} {timeframe}")
            return indicators_result
            
        except Exception as e:
            logger.error(f"单时间周期技术分析失败: {symbol} {timeframe}, 错误: {e}")
            raise TechnicalAnalysisError(f"单时间周期技术分析失败: {e}")
    
    def analyze_multi_timeframe(self, exchange: str, symbol: str, 
                              timeframes: List[str] = None, concurrent: bool = True) -> Dict[str, Any]:
        """多时间周期技术分析"""
        try:
            if timeframes is None:
                timeframes = self.supported_timeframes
            
            analysis_result = {
                'exchange': exchange,
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'timeframes': {},
                'multi_timeframe_summary': {}
            }
            
            if concurrent:
                # 并发分析
                future_to_timeframe = {}
                
                for timeframe in timeframes:
                    future = self._executor.submit(
                        self.analyze_single_timeframe,
                        exchange, symbol, timeframe
                    )
                    future_to_timeframe[future] = timeframe
                
                # 收集结果
                for future in as_completed(future_to_timeframe, timeout=120):
                    timeframe = future_to_timeframe[future]
                    try:
                        result = future.result()
                        analysis_result['timeframes'][timeframe] = result
                    except Exception as e:
                        logger.warning(f"时间周期 {timeframe} 分析失败: {e}")
                        analysis_result['timeframes'][timeframe] = {
                            'error': str(e),
                            'timeframe': timeframe
                        }
            else:
                # 顺序分析
                for timeframe in timeframes:
                    try:
                        result = self.analyze_single_timeframe(exchange, symbol, timeframe)
                        analysis_result['timeframes'][timeframe] = result
                    except Exception as e:
                        logger.warning(f"时间周期 {timeframe} 分析失败: {e}")
                        analysis_result['timeframes'][timeframe] = {
                            'error': str(e),
                            'timeframe': timeframe
                        }
            
            # 生成多时间周期摘要
            analysis_result['multi_timeframe_summary'] = self._generate_multi_timeframe_summary(
                analysis_result['timeframes']
            )
            
            logger.info(f"多时间周期技术分析完成: {symbol}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"多时间周期技术分析失败: {symbol}, 错误: {e}")
            raise TechnicalAnalysisError(f"多时间周期技术分析失败: {e}")
    
    def _generate_multi_timeframe_summary(self, timeframes_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成多时间周期摘要"""
        try:
            summary = {
                'trend_consensus': 'NEUTRAL',
                'momentum_consensus': 'NEUTRAL',
                'volatility_level': 'NORMAL',
                'volume_trend': 'NORMAL',
                'overall_signal': 'NEUTRAL',
                'confidence': 0,
                'timeframe_signals': {}
            }
            
            valid_timeframes = []
            trend_signals = []
            momentum_signals = []
            
            # 收集各时间周期的信号
            for timeframe, data in timeframes_data.items():
                if 'error' not in data and 'summary' in data:
                    valid_timeframes.append(timeframe)
                    tf_summary = data['summary']
                    
                    # 收集趋势信号
                    if 'trend_signals' in tf_summary:
                        for signal_name, signal_value in tf_summary['trend_signals'].items():
                            trend_signals.append(signal_value)
                    
                    # 收集动量信号
                    if 'momentum_signals' in tf_summary:
                        for signal_name, signal_value in tf_summary['momentum_signals'].items():
                            momentum_signals.append(signal_value)
                    
                    summary['timeframe_signals'][timeframe] = tf_summary.get('overall_signal', 'NEUTRAL')
            
            # 计算趋势共识
            if trend_signals:
                bullish_count = trend_signals.count('BULLISH')
                bearish_count = trend_signals.count('BEARISH')
                
                if bullish_count > bearish_count:
                    summary['trend_consensus'] = 'BULLISH'
                elif bearish_count > bullish_count:
                    summary['trend_consensus'] = 'BEARISH'
            
            # 计算动量共识
            if momentum_signals:
                overbought_count = momentum_signals.count('OVERBOUGHT')
                oversold_count = momentum_signals.count('OVERSOLD')
                
                if overbought_count > oversold_count:
                    summary['momentum_consensus'] = 'OVERBOUGHT'
                elif oversold_count > overbought_count:
                    summary['momentum_consensus'] = 'OVERSOLD'
            
            # 计算整体信号和置信度
            signal_scores = []
            for tf_signal in summary['timeframe_signals'].values():
                if tf_signal == 'BULLISH':
                    signal_scores.append(1)
                elif tf_signal == 'BEARISH':
                    signal_scores.append(-1)
                else:
                    signal_scores.append(0)
            
            if signal_scores:
                avg_score = sum(signal_scores) / len(signal_scores)
                if avg_score > 0.3:
                    summary['overall_signal'] = 'BULLISH'
                elif avg_score < -0.3:
                    summary['overall_signal'] = 'BEARISH'
                
                # 计算置信度
                summary['confidence'] = min(abs(avg_score) * 100, 100)
            
            summary['valid_timeframes'] = len(valid_timeframes)
            summary['total_timeframes'] = len(timeframes_data)
            
            return summary

        except Exception as e:
            logger.error(f"生成多时间周期摘要失败: {e}")
            return {'error': str(e)}

    def format_for_ai_engine(self, analysis_result: Dict[str, Any],
                           target_engine: str = 'open') -> str:
        """为AI引擎格式化技术分析数据"""
        try:
            if 'timeframes' not in analysis_result:
                raise TechnicalAnalysisError("分析结果缺少时间周期数据")

            formatted_data = {
                'symbol': analysis_result.get('symbol', 'UNKNOWN'),
                'timestamp': analysis_result.get('timestamp', datetime.now().isoformat()),
                'analysis_type': target_engine,
                'timeframe_analysis': {},
                'overall_summary': analysis_result.get('multi_timeframe_summary', {})
            }

            # 格式化各时间周期的指标数据
            for timeframe, tf_data in analysis_result['timeframes'].items():
                if 'error' in tf_data:
                    continue

                tf_formatted = {
                    'timeframe': timeframe,
                    'current_price': tf_data.get('current_price'),
                    'price_change': tf_data.get('price_change'),
                    'price_change_pct': tf_data.get('price_change_pct'),
                    'key_indicators': {}
                }

                # 提取关键指标
                if 'trend_indicators' in tf_data:
                    trend_data = tf_data['trend_indicators']
                    tf_formatted['key_indicators']['trend'] = {
                        'sma_20': trend_data.get('sma_20', {}).get('current'),
                        'ema_20': trend_data.get('ema_20', {}).get('current'),
                        'macd_line': trend_data.get('macd', {}).get('macd', {}).get('current'),
                        'macd_signal': trend_data.get('macd', {}).get('signal', {}).get('current'),
                        'adx': trend_data.get('adx', {}).get('current')
                    }

                if 'momentum_indicators' in tf_data:
                    momentum_data = tf_data['momentum_indicators']
                    tf_formatted['key_indicators']['momentum'] = {
                        'rsi': momentum_data.get('rsi', {}).get('current'),
                        'stoch_k': momentum_data.get('stoch', {}).get('k', {}).get('current'),
                        'stoch_d': momentum_data.get('stoch', {}).get('d', {}).get('current'),
                        'cci': momentum_data.get('cci', {}).get('current'),
                        'willr': momentum_data.get('willr', {}).get('current')
                    }

                if 'volume_indicators' in tf_data:
                    volume_data = tf_data['volume_indicators']
                    tf_formatted['key_indicators']['volume'] = {
                        'obv': volume_data.get('obv', {}).get('current'),
                        'ad': volume_data.get('ad', {}).get('current'),
                        'adosc': volume_data.get('adosc', {}).get('current')
                    }

                if 'volatility_indicators' in tf_data:
                    volatility_data = tf_data['volatility_indicators']
                    tf_formatted['key_indicators']['volatility'] = {
                        'atr': volatility_data.get('atr', {}).get('current'),
                        'natr': volatility_data.get('natr', {}).get('current'),
                        'bb_upper': volatility_data.get('bbands', {}).get('upper', {}).get('current'),
                        'bb_middle': volatility_data.get('bbands', {}).get('middle', {}).get('current'),
                        'bb_lower': volatility_data.get('bbands', {}).get('lower', {}).get('current')
                    }

                # 添加信号摘要
                if 'summary' in tf_data:
                    tf_formatted['signals'] = tf_data['summary']

                formatted_data['timeframe_analysis'][timeframe] = tf_formatted

            # 转换为JSON字符串
            import json
            return json.dumps(formatted_data, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"AI引擎数据格式化失败: {e}")
            raise TechnicalAnalysisError(f"AI引擎数据格式化失败: {e}")

    def generate_trading_signals(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易信号"""
        try:
            signals = {
                'timestamp': datetime.now().isoformat(),
                'symbol': analysis_result.get('symbol', 'UNKNOWN'),
                'overall_signal': 'NEUTRAL',
                'signal_strength': 0,
                'confidence': 0,
                'timeframe_signals': {},
                'signal_reasons': [],
                'risk_assessment': 'MEDIUM'
            }

            if 'multi_timeframe_summary' not in analysis_result:
                return signals

            summary = analysis_result['multi_timeframe_summary']

            # 基于多时间周期摘要生成信号
            trend_consensus = summary.get('trend_consensus', 'NEUTRAL')
            momentum_consensus = summary.get('momentum_consensus', 'NEUTRAL')
            overall_signal = summary.get('overall_signal', 'NEUTRAL')
            confidence = summary.get('confidence', 0)

            signals['overall_signal'] = overall_signal
            signals['confidence'] = confidence

            # 计算信号强度
            signal_strength = 0
            if trend_consensus == 'BULLISH':
                signal_strength += 30
                signals['signal_reasons'].append('多时间周期趋势看涨')
            elif trend_consensus == 'BEARISH':
                signal_strength -= 30
                signals['signal_reasons'].append('多时间周期趋势看跌')

            if momentum_consensus == 'OVERBOUGHT':
                signal_strength -= 20
                signals['signal_reasons'].append('动量指标显示超买')
            elif momentum_consensus == 'OVERSOLD':
                signal_strength += 20
                signals['signal_reasons'].append('动量指标显示超卖')

            # 分析各时间周期信号
            timeframe_signals = summary.get('timeframe_signals', {})
            bullish_count = sum(1 for signal in timeframe_signals.values() if signal == 'BULLISH')
            bearish_count = sum(1 for signal in timeframe_signals.values() if signal == 'BEARISH')

            if bullish_count > bearish_count:
                signal_strength += 10 * (bullish_count - bearish_count)
                signals['signal_reasons'].append(f'{bullish_count}个时间周期看涨')
            elif bearish_count > bullish_count:
                signal_strength -= 10 * (bearish_count - bullish_count)
                signals['signal_reasons'].append(f'{bearish_count}个时间周期看跌')

            signals['signal_strength'] = max(-100, min(100, signal_strength))
            signals['timeframe_signals'] = timeframe_signals

            # 风险评估
            if abs(signal_strength) > 60:
                signals['risk_assessment'] = 'HIGH'
            elif abs(signal_strength) > 30:
                signals['risk_assessment'] = 'MEDIUM'
            else:
                signals['risk_assessment'] = 'LOW'

            return signals

        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'symbol': analysis_result.get('symbol', 'UNKNOWN'),
                'overall_signal': 'NEUTRAL',
                'signal_strength': 0,
                'confidence': 0,
                'error': str(e)
            }

    def get_ai_analysis_data(self, exchange: str, symbol: str,
                           target_engine: str = 'open') -> Dict[str, Any]:
        """获取AI分析专用数据"""
        try:
            # 执行多时间周期分析
            analysis_result = self.analyze_multi_timeframe(exchange, symbol)

            # 格式化为AI引擎数据
            ai_formatted_data = self.format_for_ai_engine(analysis_result, target_engine)

            # 生成交易信号
            trading_signals = self.generate_trading_signals(analysis_result)

            return {
                'raw_analysis': analysis_result,
                'ai_formatted_data': ai_formatted_data,
                'trading_signals': trading_signals,
                'data_quality': {
                    'valid_timeframes': len([tf for tf, data in analysis_result['timeframes'].items() if 'error' not in data]),
                    'total_timeframes': len(analysis_result['timeframes']),
                    'analysis_timestamp': analysis_result['timestamp']
                }
            }

        except Exception as e:
            logger.error(f"获取AI分析数据失败: {e}")
            raise TechnicalAnalysisError(f"获取AI分析数据失败: {e}")

    def clear_cache(self):
        """清除分析缓存"""
        with self._cache_lock:
            self._analysis_cache.clear()
            logger.info("技术分析缓存已清除")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._cache_lock:
            return {
                'cache_size': len(self._analysis_cache),
                'cache_ttl': self._cache_ttl,
                'cache_keys': list(self._analysis_cache.keys())[:10]  # 只显示前10个
            }
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        cache_stats = self.get_cache_stats()
        
        return {
            'engine_name': 'TechnicalAnalysisEngine',
            'supported_timeframes': self.supported_timeframes,
            'executor_threads': self._executor._max_workers,
            'cache_stats': cache_stats,
            'indicators_available': True,
            'market_data_service_available': True,
            'ai_interface_available': True,
            'signal_generation_available': True,
            'status': 'ready'
        }

    def analyze_market_trend(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场趋势"""
        try:
            trend_analysis = {
                'short_term_trend': 'NEUTRAL',
                'medium_term_trend': 'NEUTRAL',
                'long_term_trend': 'NEUTRAL',
                'trend_strength': 0,
                'trend_consistency': 0,
                'support_resistance': {},
                'trend_reversal_signals': []
            }

            if 'timeframes' not in analysis_result:
                return trend_analysis

            timeframes_data = analysis_result['timeframes']

            # 短期趋势（1分、5分）
            short_term_signals = []
            for tf in ['1m', '5m']:
                if tf in timeframes_data and 'error' not in timeframes_data[tf]:
                    tf_summary = timeframes_data[tf].get('summary', {})
                    overall_signal = tf_summary.get('overall_signal', 'NEUTRAL')
                    short_term_signals.append(overall_signal)

            if short_term_signals:
                bullish_count = short_term_signals.count('BULLISH')
                bearish_count = short_term_signals.count('BEARISH')
                if bullish_count > bearish_count:
                    trend_analysis['short_term_trend'] = 'BULLISH'
                elif bearish_count > bullish_count:
                    trend_analysis['short_term_trend'] = 'BEARISH'

            # 中期趋势（15分）
            if '15m' in timeframes_data and 'error' not in timeframes_data['15m']:
                tf_summary = timeframes_data['15m'].get('summary', {})
                trend_analysis['medium_term_trend'] = tf_summary.get('overall_signal', 'NEUTRAL')

            # 长期趋势（1小时）
            if '1h' in timeframes_data and 'error' not in timeframes_data['1h']:
                tf_summary = timeframes_data['1h'].get('summary', {})
                trend_analysis['long_term_trend'] = tf_summary.get('overall_signal', 'NEUTRAL')

            # 计算趋势强度
            trend_signals = [
                trend_analysis['short_term_trend'],
                trend_analysis['medium_term_trend'],
                trend_analysis['long_term_trend']
            ]

            bullish_trends = trend_signals.count('BULLISH')
            bearish_trends = trend_signals.count('BEARISH')

            if bullish_trends > bearish_trends:
                trend_analysis['trend_strength'] = bullish_trends * 30
            elif bearish_trends > bullish_trends:
                trend_analysis['trend_strength'] = -bearish_trends * 30

            # 计算趋势一致性
            if bullish_trends == 3 or bearish_trends == 3:
                trend_analysis['trend_consistency'] = 100
            elif bullish_trends == 2 or bearish_trends == 2:
                trend_analysis['trend_consistency'] = 70
            else:
                trend_analysis['trend_consistency'] = 30

            # 检测趋势反转信号
            if (trend_analysis['short_term_trend'] == 'BEARISH' and
                trend_analysis['long_term_trend'] == 'BULLISH'):
                trend_analysis['trend_reversal_signals'].append('短期回调，长期仍看涨')
            elif (trend_analysis['short_term_trend'] == 'BULLISH' and
                  trend_analysis['long_term_trend'] == 'BEARISH'):
                trend_analysis['trend_reversal_signals'].append('短期反弹，长期仍看跌')

            return trend_analysis

        except Exception as e:
            logger.error(f"市场趋势分析失败: {e}")
            return {'error': str(e)}

    def calculate_entry_exit_levels(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算入场和出场水平"""
        try:
            levels = {
                'current_price': None,
                'support_levels': [],
                'resistance_levels': [],
                'entry_levels': {
                    'long': [],
                    'short': []
                },
                'stop_loss_levels': {
                    'long': [],
                    'short': []
                },
                'take_profit_levels': {
                    'long': [],
                    'short': []
                }
            }

            # 获取当前价格
            if 'timeframes' in analysis_result:
                for tf_data in analysis_result['timeframes'].values():
                    if 'current_price' in tf_data and tf_data['current_price']:
                        levels['current_price'] = tf_data['current_price']
                        break

            if not levels['current_price']:
                return levels

            current_price = levels['current_price']

            # 从布林带计算支撑阻力位
            for tf, tf_data in analysis_result.get('timeframes', {}).items():
                if 'error' in tf_data:
                    continue

                volatility_indicators = tf_data.get('volatility_indicators', {})
                if 'bbands' in volatility_indicators:
                    bbands = volatility_indicators['bbands']
                    upper = bbands.get('upper', {}).get('current')
                    lower = bbands.get('lower', {}).get('current')

                    if upper and lower:
                        levels['resistance_levels'].append({
                            'level': upper,
                            'timeframe': tf,
                            'type': 'bollinger_upper'
                        })
                        levels['support_levels'].append({
                            'level': lower,
                            'timeframe': tf,
                            'type': 'bollinger_lower'
                        })

            # 计算ATR用于止损
            atr_values = []
            for tf_data in analysis_result.get('timeframes', {}).values():
                if 'error' in tf_data:
                    continue

                volatility_indicators = tf_data.get('volatility_indicators', {})
                if 'atr' in volatility_indicators:
                    atr = volatility_indicators['atr'].get('current')
                    if atr:
                        atr_values.append(atr)

            if atr_values:
                avg_atr = sum(atr_values) / len(atr_values)

                # 多头入场和止损
                levels['entry_levels']['long'].append({
                    'level': current_price - avg_atr * 0.5,
                    'type': 'pullback_entry'
                })
                levels['stop_loss_levels']['long'].append({
                    'level': current_price - avg_atr * 2,
                    'type': 'atr_stop'
                })
                levels['take_profit_levels']['long'].append({
                    'level': current_price + avg_atr * 3,
                    'type': 'atr_target'
                })

                # 空头入场和止损
                levels['entry_levels']['short'].append({
                    'level': current_price + avg_atr * 0.5,
                    'type': 'pullback_entry'
                })
                levels['stop_loss_levels']['short'].append({
                    'level': current_price + avg_atr * 2,
                    'type': 'atr_stop'
                })
                levels['take_profit_levels']['short'].append({
                    'level': current_price - avg_atr * 3,
                    'type': 'atr_target'
                })

            return levels

        except Exception as e:
            logger.error(f"计算入场出场水平失败: {e}")
            return {'error': str(e)}

    def get_comprehensive_analysis(self, exchange: str, symbol: str) -> Dict[str, Any]:
        """获取综合技术分析"""
        try:
            # 基础多时间周期分析
            base_analysis = self.analyze_multi_timeframe(exchange, symbol)

            # 趋势分析
            trend_analysis = self.analyze_market_trend(base_analysis)

            # 交易信号
            trading_signals = self.generate_trading_signals(base_analysis)

            # 入场出场水平
            entry_exit_levels = self.calculate_entry_exit_levels(base_analysis)

            # AI格式化数据
            ai_data = self.format_for_ai_engine(base_analysis, 'comprehensive')

            comprehensive_analysis = {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol,
                'exchange': exchange,
                'base_analysis': base_analysis,
                'trend_analysis': trend_analysis,
                'trading_signals': trading_signals,
                'entry_exit_levels': entry_exit_levels,
                'ai_formatted_data': ai_data,
                'analysis_summary': {
                    'overall_signal': trading_signals.get('overall_signal', 'NEUTRAL'),
                    'signal_strength': trading_signals.get('signal_strength', 0),
                    'trend_direction': trend_analysis.get('long_term_trend', 'NEUTRAL'),
                    'trend_strength': trend_analysis.get('trend_strength', 0),
                    'confidence': trading_signals.get('confidence', 0),
                    'risk_level': trading_signals.get('risk_assessment', 'MEDIUM')
                }
            }

            logger.info(f"综合技术分析完成: {symbol}")
            return comprehensive_analysis

        except Exception as e:
            logger.error(f"综合技术分析失败: {symbol}, 错误: {e}")
            raise TechnicalAnalysisError(f"综合技术分析失败: {e}")


# 全局技术分析引擎实例
technical_analysis_engine = TechnicalAnalysisEngine()


def get_technical_analysis_engine() -> TechnicalAnalysisEngine:
    """获取技术分析引擎实例"""
    return technical_analysis_engine
