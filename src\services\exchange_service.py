# -*- coding: utf-8 -*-
"""
交易所服务
封装CCXT交易所操作，提供统一的交易接口

Author: SuperBot Team
Date: 2025-01-04
"""

import ccxt
import logging
import time
import threading
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from threading import Lock, Thread, Event
import asyncio
from concurrent.futures import ThreadPoolExecutor

from src.data.repositories.trading_repository import get_api_keys_repository, get_trading_pairs_repository
from src.data.models import ApiKeys, TradingPairs
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ExchangeConnectionError(Exception):
    """交易所连接错误"""
    pass


class ExchangeAuthenticationError(Exception):
    """交易所认证错误"""
    pass


class ExchangeRateLimitError(Exception):
    """交易所频率限制错误"""
    pass


class ExchangeService:
    """交易所服务类"""
    
    def __init__(self):
        """初始化交易所服务"""
        self._exchanges: Dict[str, ccxt.Exchange] = {}
        self._connection_lock = Lock()
        self._last_request_time: Dict[str, float] = {}
        self._rate_limit_delay = 1.0  # 默认请求间隔1秒

        self.api_keys_repo = get_api_keys_repository()
        self.trading_pairs_repo = get_trading_pairs_repository()

        # 定时轮询相关
        self._polling_threads: Dict[str, Thread] = {}
        self._polling_events: Dict[str, Event] = {}
        self._polling_data: Dict[str, Dict] = {}
        self._polling_lock = Lock()
        self._polling_interval = 5  # 5秒轮询间隔

        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=10)

        # 支持的时间周期
        self.supported_timeframes = ['1m', '5m', '15m', '1h']

        logger.info("交易所服务初始化完成")
    
    def _get_exchange_instance(self, exchange_name: str, force_reload: bool = False) -> ccxt.Exchange:
        """获取交易所实例"""
        with self._connection_lock:
            if exchange_name in self._exchanges and not force_reload:
                return self._exchanges[exchange_name]
            
            try:
                # 获取API密钥
                api_keys = self.api_keys_repo.find_by_exchange(exchange_name, is_active=True)
                if not api_keys:
                    raise ExchangeAuthenticationError(f"未找到 {exchange_name} 的有效API密钥")
                
                # 创建交易所实例
                exchange_class = getattr(ccxt, exchange_name.lower())

                config = {
                    'apiKey': api_keys.api_key,
                    'secret': api_keys.secret_key,
                    'sandbox': api_keys.is_sandbox,
                    'enableRateLimit': True,
                    'timeout': 30000,  # 30秒超时
                }

                # OKX需要passphrase
                if exchange_name.lower() == 'okx' and api_keys.passphrase:
                    config['password'] = api_keys.passphrase

                exchange = exchange_class(config)

                # 设置模拟盘模式
                if api_keys.is_sandbox:
                    exchange.set_sandbox_mode(True)
                
                # 加载市场数据
                exchange.load_markets()
                
                self._exchanges[exchange_name] = exchange
                logger.info(f"交易所 {exchange_name} 连接成功")
                
                return exchange
                
            except ccxt.AuthenticationError as e:
                logger.error(f"交易所认证失败: {exchange_name}, 错误: {e}")
                raise ExchangeAuthenticationError(f"交易所认证失败: {e}")
            
            except ccxt.NetworkError as e:
                logger.error(f"交易所网络错误: {exchange_name}, 错误: {e}")
                raise ExchangeConnectionError(f"交易所网络错误: {e}")
            
            except Exception as e:
                logger.error(f"交易所连接失败: {exchange_name}, 错误: {e}")
                raise ExchangeConnectionError(f"交易所连接失败: {e}")
    
    def _rate_limit_check(self, exchange_name: str):
        """检查频率限制"""
        current_time = time.time()
        last_time = self._last_request_time.get(exchange_name, 0)
        
        time_diff = current_time - last_time
        if time_diff < self._rate_limit_delay:
            sleep_time = self._rate_limit_delay - time_diff
            logger.debug(f"频率限制等待: {sleep_time:.2f}秒")
            time.sleep(sleep_time)
        
        self._last_request_time[exchange_name] = time.time()
    
    def _handle_exchange_error(self, e: Exception, operation: str):
        """处理交易所错误"""
        if isinstance(e, ccxt.RateLimitExceeded):
            logger.warning(f"频率限制超出: {operation}")
            raise ExchangeRateLimitError(f"频率限制超出: {e}")
        
        elif isinstance(e, ccxt.AuthenticationError):
            logger.error(f"认证错误: {operation}, 错误: {e}")
            raise ExchangeAuthenticationError(f"认证错误: {e}")
        
        elif isinstance(e, ccxt.NetworkError):
            logger.error(f"网络错误: {operation}, 错误: {e}")
            raise ExchangeConnectionError(f"网络错误: {e}")
        
        else:
            logger.error(f"交易所操作失败: {operation}, 错误: {e}")
            raise Exception(f"交易所操作失败: {e}")
    
    def test_connection(self, exchange_name: str) -> bool:
        """测试交易所连接"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            
            # 尝试获取账户余额来测试连接
            self._rate_limit_check(exchange_name)
            balance = exchange.fetch_balance()
            
            logger.info(f"交易所 {exchange_name} 连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"交易所 {exchange_name} 连接测试失败: {e}")
            return False
    
    def get_account_balance(self, exchange_name: str) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            balance = exchange.fetch_balance()
            
            # 格式化余额信息
            formatted_balance = {
                'total': balance.get('total', {}),
                'free': balance.get('free', {}),
                'used': balance.get('used', {}),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.debug(f"获取 {exchange_name} 账户余额成功")
            return formatted_balance
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取 {exchange_name} 账户余额")
    
    def get_ticker(self, exchange_name: str, symbol: str) -> Dict[str, Any]:
        """获取交易对行情"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            ticker = exchange.fetch_ticker(symbol)
            
            # 格式化行情数据
            formatted_ticker = {
                'symbol': ticker.get('symbol'),
                'last': ticker.get('last'),
                'bid': ticker.get('bid'),
                'ask': ticker.get('ask'),
                'high': ticker.get('high'),
                'low': ticker.get('low'),
                'volume': ticker.get('baseVolume'),
                'change': ticker.get('change'),
                'percentage': ticker.get('percentage'),
                'timestamp': ticker.get('timestamp'),
                'datetime': ticker.get('datetime')
            }
            
            logger.debug(f"获取 {exchange_name} {symbol} 行情成功")
            return formatted_ticker
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取 {exchange_name} {symbol} 行情")
    
    def get_ohlcv(self, exchange_name: str, symbol: str, timeframe: str = '1m', 
                  limit: int = 100, since: Optional[int] = None) -> List[List]:
        """获取K线数据"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit)
            
            logger.debug(f"获取 {exchange_name} {symbol} {timeframe} K线数据成功: {len(ohlcv)} 条")
            return ohlcv
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取 {exchange_name} {symbol} K线数据")
    
    def get_order_book(self, exchange_name: str, symbol: str, limit: int = 20) -> Dict[str, Any]:
        """获取订单簿"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            order_book = exchange.fetch_order_book(symbol, limit)
            
            formatted_order_book = {
                'symbol': symbol,
                'bids': order_book.get('bids', []),
                'asks': order_book.get('asks', []),
                'timestamp': order_book.get('timestamp'),
                'datetime': order_book.get('datetime')
            }
            
            logger.debug(f"获取 {exchange_name} {symbol} 订单簿成功")
            return formatted_order_book
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取 {exchange_name} {symbol} 订单簿")
    
    def create_market_order(self, exchange_name: str, symbol: str, side: str, 
                           amount: float, params: Dict = None) -> Dict[str, Any]:
        """创建市价单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            order = exchange.create_market_order(symbol, side, amount, None, params or {})
            
            logger.info(f"创建市价单成功: {exchange_name} {symbol} {side} {amount}")
            return order
            
        except Exception as e:
            self._handle_exchange_error(e, f"创建市价单: {exchange_name} {symbol} {side} {amount}")
    
    def create_limit_order(self, exchange_name: str, symbol: str, side: str, 
                          amount: float, price: float, params: Dict = None) -> Dict[str, Any]:
        """创建限价单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            order = exchange.create_limit_order(symbol, side, amount, price, params or {})
            
            logger.info(f"创建限价单成功: {exchange_name} {symbol} {side} {amount} @ {price}")
            return order
            
        except Exception as e:
            self._handle_exchange_error(e, f"创建限价单: {exchange_name} {symbol} {side} {amount} @ {price}")
    
    def cancel_order(self, exchange_name: str, order_id: str, symbol: str) -> Dict[str, Any]:
        """取消订单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            result = exchange.cancel_order(order_id, symbol)
            
            logger.info(f"取消订单成功: {exchange_name} {order_id}")
            return result
            
        except Exception as e:
            self._handle_exchange_error(e, f"取消订单: {exchange_name} {order_id}")
    
    def get_order_status(self, exchange_name: str, order_id: str, symbol: str) -> Dict[str, Any]:
        """获取订单状态"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            order = exchange.fetch_order(order_id, symbol)
            
            logger.debug(f"获取订单状态成功: {exchange_name} {order_id}")
            return order
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取订单状态: {exchange_name} {order_id}")
    
    def get_open_orders(self, exchange_name: str, symbol: str = None) -> List[Dict[str, Any]]:
        """获取未完成订单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            orders = exchange.fetch_open_orders(symbol)
            
            logger.debug(f"获取未完成订单成功: {exchange_name}, 数量: {len(orders)}")
            return orders
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取未完成订单: {exchange_name}")
    
    def get_positions(self, exchange_name: str, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            positions = exchange.fetch_positions(symbols)
            
            # 过滤掉零持仓
            active_positions = [pos for pos in positions if pos.get('contracts', 0) != 0]
            
            logger.debug(f"获取持仓信息成功: {exchange_name}, 活跃持仓: {len(active_positions)}")
            return active_positions
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取持仓信息: {exchange_name}")
    
    def get_trading_fees(self, exchange_name: str, symbol: str = None) -> Dict[str, Any]:
        """获取交易手续费"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)
            
            if symbol:
                fees = exchange.fetch_trading_fee(symbol)
            else:
                fees = exchange.fetch_trading_fees()
            
            logger.debug(f"获取交易手续费成功: {exchange_name}")
            return fees
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取交易手续费: {exchange_name}")
    
    def get_markets(self, exchange_name: str) -> Dict[str, Any]:
        """获取市场信息"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            
            markets = exchange.markets
            
            logger.debug(f"获取市场信息成功: {exchange_name}, 市场数量: {len(markets)}")
            return markets
            
        except Exception as e:
            self._handle_exchange_error(e, f"获取市场信息: {exchange_name}")
    
    def start_polling(self, exchange_name: str, symbols: List[str] = None):
        """启动定时轮询"""
        try:
            if exchange_name in self._polling_threads:
                logger.warning(f"交易所 {exchange_name} 轮询已启动")
                return

            # 创建停止事件
            stop_event = Event()
            self._polling_events[exchange_name] = stop_event

            # 初始化轮询数据
            self._polling_data[exchange_name] = {
                'account_balance': {},
                'positions': [],
                'open_orders': [],
                'last_update': None
            }

            # 启动轮询线程
            polling_thread = Thread(
                target=self._polling_worker,
                args=(exchange_name, symbols, stop_event),
                daemon=True
            )
            polling_thread.start()

            self._polling_threads[exchange_name] = polling_thread
            logger.info(f"启动交易所 {exchange_name} 定时轮询")

        except Exception as e:
            logger.error(f"启动轮询失败: {exchange_name}, 错误: {e}")

    def stop_polling(self, exchange_name: str):
        """停止定时轮询"""
        try:
            if exchange_name in self._polling_events:
                self._polling_events[exchange_name].set()

            if exchange_name in self._polling_threads:
                self._polling_threads[exchange_name].join(timeout=10)
                del self._polling_threads[exchange_name]

            if exchange_name in self._polling_events:
                del self._polling_events[exchange_name]

            if exchange_name in self._polling_data:
                del self._polling_data[exchange_name]

            logger.info(f"停止交易所 {exchange_name} 定时轮询")

        except Exception as e:
            logger.error(f"停止轮询失败: {exchange_name}, 错误: {e}")

    def _polling_worker(self, exchange_name: str, symbols: List[str], stop_event: Event):
        """轮询工作线程"""
        logger.info(f"开始轮询工作线程: {exchange_name}")

        while not stop_event.is_set():
            try:
                # 更新账户余额
                balance = self.get_account_balance(exchange_name)
                self._polling_data[exchange_name]['account_balance'] = balance

                # 更新持仓信息
                positions = self.get_positions(exchange_name)
                self._polling_data[exchange_name]['positions'] = positions

                # 更新未完成订单
                open_orders = self.get_open_orders(exchange_name)
                self._polling_data[exchange_name]['open_orders'] = open_orders

                # 更新时间戳
                self._polling_data[exchange_name]['last_update'] = datetime.now()

                logger.debug(f"轮询更新完成: {exchange_name}")

            except Exception as e:
                logger.error(f"轮询更新失败: {exchange_name}, 错误: {e}")

            # 等待下次轮询
            if stop_event.wait(self._polling_interval):
                break

        logger.info(f"轮询工作线程结束: {exchange_name}")

    def get_polling_data(self, exchange_name: str) -> Dict[str, Any]:
        """获取轮询数据"""
        return self._polling_data.get(exchange_name, {})

    def close_all_connections(self):
        """关闭所有交易所连接"""
        # 停止所有轮询
        for exchange_name in list(self._polling_threads.keys()):
            self.stop_polling(exchange_name)

        # 关闭线程池
        self._executor.shutdown(wait=True)

        # 关闭交易所连接
        with self._connection_lock:
            for exchange_name, exchange in self._exchanges.items():
                try:
                    if hasattr(exchange, 'close'):
                        exchange.close()
                    logger.info(f"关闭交易所连接: {exchange_name}")
                except Exception as e:
                    logger.error(f"关闭交易所连接失败: {exchange_name}, 错误: {e}")

            self._exchanges.clear()
            logger.info("所有交易所连接已关闭")
    
    def create_stop_loss_order(self, exchange_name: str, symbol: str, side: str,
                              amount: float, stop_price: float, params: Dict = None) -> Dict[str, Any]:
        """创建止损单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            # OKX止损单参数
            order_params = params or {}
            order_params.update({
                'stopPrice': stop_price,
                'type': 'stop_market'
            })

            order = exchange.create_order(symbol, 'market', side, amount, None, order_params)

            logger.info(f"创建止损单成功: {exchange_name} {symbol} {side} {amount} @ {stop_price}")
            return order

        except Exception as e:
            self._handle_exchange_error(e, f"创建止损单: {exchange_name} {symbol}")

    def create_take_profit_order(self, exchange_name: str, symbol: str, side: str,
                                amount: float, take_profit_price: float, params: Dict = None) -> Dict[str, Any]:
        """创建止盈单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            # OKX止盈单参数
            order_params = params or {}
            order_params.update({
                'stopPrice': take_profit_price,
                'type': 'take_profit_market'
            })

            order = exchange.create_order(symbol, 'market', side, amount, None, order_params)

            logger.info(f"创建止盈单成功: {exchange_name} {symbol} {side} {amount} @ {take_profit_price}")
            return order

        except Exception as e:
            self._handle_exchange_error(e, f"创建止盈单: {exchange_name} {symbol}")

    def modify_order(self, exchange_name: str, order_id: str, symbol: str,
                    amount: float = None, price: float = None, params: Dict = None) -> Dict[str, Any]:
        """修改订单"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            result = exchange.edit_order(order_id, symbol, None, amount, price, params or {})

            logger.info(f"修改订单成功: {exchange_name} {order_id}")
            return result

        except Exception as e:
            self._handle_exchange_error(e, f"修改订单: {exchange_name} {order_id}")

    def get_order_history(self, exchange_name: str, symbol: str = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """获取订单历史"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            orders = exchange.fetch_orders(symbol, None, limit)

            logger.debug(f"获取订单历史成功: {exchange_name}, 数量: {len(orders)}")
            return orders

        except Exception as e:
            self._handle_exchange_error(e, f"获取订单历史: {exchange_name}")

    def get_trade_history(self, exchange_name: str, symbol: str = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """获取交易历史"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            trades = exchange.fetch_my_trades(symbol, None, limit)

            logger.debug(f"获取交易历史成功: {exchange_name}, 数量: {len(trades)}")
            return trades

        except Exception as e:
            self._handle_exchange_error(e, f"获取交易历史: {exchange_name}")

    def set_leverage(self, exchange_name: str, symbol: str, leverage: int) -> Dict[str, Any]:
        """设置杠杆"""
        try:
            exchange = self._get_exchange_instance(exchange_name)
            self._rate_limit_check(exchange_name)

            result = exchange.set_leverage(leverage, symbol)

            logger.info(f"设置杠杆成功: {exchange_name} {symbol} {leverage}x")
            return result

        except Exception as e:
            self._handle_exchange_error(e, f"设置杠杆: {exchange_name} {symbol} {leverage}x")

    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        with self._polling_lock:
            polling_status = {
                exchange: {
                    'is_polling': exchange in self._polling_threads,
                    'last_update': self._polling_data.get(exchange, {}).get('last_update'),
                    'data_available': bool(self._polling_data.get(exchange))
                }
                for exchange in self._exchanges.keys()
            }

        status = {
            'connected_exchanges': list(self._exchanges.keys()),
            'total_connections': len(self._exchanges),
            'last_request_times': self._last_request_time.copy(),
            'rate_limit_delay': self._rate_limit_delay,
            'supported_timeframes': self.supported_timeframes,
            'polling_interval': self._polling_interval,
            'polling_status': polling_status
        }

        return status


# 全局交易所服务实例
exchange_service = ExchangeService()


def get_exchange_service() -> ExchangeService:
    """获取交易所服务实例"""
    return exchange_service
