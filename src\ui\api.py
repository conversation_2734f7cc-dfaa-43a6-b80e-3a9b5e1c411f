# -*- coding: utf-8 -*-
"""
前后端通信API
实现JavaScript调用Python方法的通信接口，为前端提供系统功能

Author: SuperBot Team
Date: 2025-01-05
"""

import json
import sys
import threading
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.utils.logger import get_logger
from src.utils.config import get_config_manager
from src.core.scheduler import get_task_scheduler, setup_scheduled_jobs
from src.core.trading import get_trade_executor, get_risk_manager, get_position_manager
from src.core.ai_engines import get_ai_open_engine, get_ai_hold_engine
from src.services.market_data_service import get_market_data_service
from src.services.exchange_service import get_exchange_service

logger = get_logger(__name__)


class WebviewAPI:
    """PyWebview前后端通信API"""
    
    def __init__(self):
        """初始化API"""
        self.config_manager = get_config_manager()
        self.task_scheduler = get_task_scheduler()
        self.trade_executor = get_trade_executor()
        self.risk_manager = get_risk_manager()
        self.position_manager = get_position_manager()
        self.market_data_service = get_market_data_service()
        self.exchange_service = get_exchange_service()
        
        # 系统状态
        self.system_status = {
            'status': 'initializing',
            'version': '1.0.0',
            'trading_active': False,
            'scheduler_running': False,
            'last_update': datetime.now().isoformat()
        }
        
        # API调用统计
        self.api_stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'last_call_time': None
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        logger.info("WebviewAPI初始化完成")
    
    def initialize(self):
        """初始化API服务"""
        try:
            with self.lock:
                # 更新系统状态
                self.system_status['status'] = 'ready'
                self.system_status['last_update'] = datetime.now().isoformat()
                
                logger.info("WebviewAPI服务初始化完成")
                
        except Exception as e:
            logger.error(f"初始化API服务失败: {e}")
            self.system_status['status'] = 'error'
    
    def cleanup(self):
        """清理API资源"""
        try:
            with self.lock:
                self.system_status['status'] = 'shutdown'
                logger.info("WebviewAPI资源清理完成")
                
        except Exception as e:
            logger.error(f"清理API资源失败: {e}")
    
    def _update_api_stats(self, success: bool = True):
        """更新API调用统计"""
        with self.lock:
            self.api_stats['total_calls'] += 1
            if success:
                self.api_stats['successful_calls'] += 1
            else:
                self.api_stats['failed_calls'] += 1
            self.api_stats['last_call_time'] = datetime.now().isoformat()
    
    def _format_response(self, success: bool, data: Any = None, message: str = "", error: str = "") -> Dict[str, Any]:
        """格式化API响应"""
        response = {
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'data': data,
            'message': message,
            'error': error
        }
        
        self._update_api_stats(success)
        return response
    
    # ==================== 系统状态API ====================
    
    def get_system_status(self, *args, **kwargs) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            with self.lock:
                # 更新实时状态
                scheduler_stats = self.task_scheduler.get_scheduler_stats()
                
                self.system_status.update({
                    'scheduler_running': scheduler_stats.get('scheduler_running', False),
                    'total_tasks': scheduler_stats.get('total_tasks', 0),
                    'running_tasks': scheduler_stats.get('running_tasks', 0),
                    'last_update': datetime.now().isoformat()
                })
                
                return self._format_response(True, self.system_status, "系统状态获取成功")
                
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return self._format_response(False, error=f"获取系统状态失败: {e}")
    
    def get_system_info(self, *args, **kwargs) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            # 收集系统信息
            system_info = {
                'version': '1.0.0',
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'platform': sys.platform,
                'start_time': self.system_status.get('start_time', datetime.now().isoformat()),
                'api_stats': self.api_stats.copy()
            }
            
            return self._format_response(True, system_info, "系统信息获取成功")
            
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return self._format_response(False, error=f"获取系统信息失败: {e}")
    
    # ==================== 交易操作API ====================
    
    def start_trading(self, pairs: List[str] = None, *args, **kwargs) -> Dict[str, Any]:
        """开始交易"""
        try:
            with self.lock:
                if self.system_status['trading_active']:
                    return self._format_response(False, message="交易已经在运行中")
                
                # 启动任务调度器
                if not self.task_scheduler.scheduler.running:
                    # 设置预定义任务
                    setup_success = setup_scheduled_jobs(self.task_scheduler)
                    if not setup_success:
                        return self._format_response(False, error="设置定时任务失败")
                    
                    # 启动调度器
                    self.task_scheduler.start()
                
                # 更新状态
                self.system_status['trading_active'] = True
                self.system_status['scheduler_running'] = True
                self.system_status['last_update'] = datetime.now().isoformat()
                
                logger.info("交易系统已启动")
                return self._format_response(True, message="交易系统启动成功")
                
        except Exception as e:
            logger.error(f"启动交易失败: {e}")
            return self._format_response(False, error=f"启动交易失败: {e}")
    
    def stop_trading(self, *args, **kwargs) -> Dict[str, Any]:
        """停止交易"""
        try:
            with self.lock:
                if not self.system_status['trading_active']:
                    return self._format_response(False, message="交易未在运行")
                
                # 停止任务调度器
                if self.task_scheduler.scheduler.running:
                    self.task_scheduler.stop(wait=False)
                
                # 更新状态
                self.system_status['trading_active'] = False
                self.system_status['scheduler_running'] = False
                self.system_status['last_update'] = datetime.now().isoformat()
                
                logger.info("交易系统已停止")
                return self._format_response(True, message="交易系统停止成功")
                
        except Exception as e:
            logger.error(f"停止交易失败: {e}")
            return self._format_response(False, error=f"停止交易失败: {e}")
    
    def get_trading_status(self, *args, **kwargs) -> Dict[str, Any]:
        """获取交易状态"""
        try:
            # 获取交易执行器状态
            executor_stats = self.trade_executor.get_executor_stats()
            
            # 获取风险管理器状态
            risk_stats = self.risk_manager.get_risk_manager_stats()
            
            # 获取仓位管理器状态
            position_stats = self.position_manager.get_position_manager_stats()
            
            trading_status = {
                'trading_active': self.system_status['trading_active'],
                'scheduler_running': self.system_status['scheduler_running'],
                'executor_stats': executor_stats,
                'risk_stats': risk_stats,
                'position_stats': position_stats,
                'last_update': datetime.now().isoformat()
            }
            
            return self._format_response(True, trading_status, "交易状态获取成功")
            
        except Exception as e:
            logger.error(f"获取交易状态失败: {e}")
            return self._format_response(False, error=f"获取交易状态失败: {e}")
    
    # ==================== 配置管理API ====================
    
    def get_config(self, key: str = None, *args, **kwargs) -> Dict[str, Any]:
        """获取配置"""
        try:
            if key:
                config_value = self.config_manager.get(key)
                data = {key: config_value}
            else:
                # 获取所有配置（敏感信息除外）
                data = {
                    'trading_params': self.config_manager.get('trading', {}),
                    'ui_config': self.config_manager.get('ui', {}),
                    'system_config': self.config_manager.get('system', {})
                }
            
            return self._format_response(True, data, "配置获取成功")
            
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return self._format_response(False, error=f"获取配置失败: {e}")
    
    def set_config(self, key: str, value: Any, *args, **kwargs) -> Dict[str, Any]:
        """设置配置"""
        try:
            # 验证配置键，防止设置敏感配置
            sensitive_keys = ['api_keys', 'secret', 'password', 'token']
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                return self._format_response(False, error="不允许通过API设置敏感配置")
            
            # 设置配置
            self.config_manager.set(key, value)
            
            return self._format_response(True, message=f"配置 {key} 设置成功")
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return self._format_response(False, error=f"设置配置失败: {e}")
    
    def get_trading_pairs(self, *args, **kwargs) -> Dict[str, Any]:
        """获取交易对列表"""
        try:
            # 获取支持的交易对
            trading_pairs = [
                'BTC/USDT:USDT',
                'ETH/USDT:USDT',
                'BNB/USDT:USDT',
                'ADA/USDT:USDT',
                'SOL/USDT:USDT'
            ]
            
            return self._format_response(True, trading_pairs, "交易对列表获取成功")
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}")
            return self._format_response(False, error=f"获取交易对列表失败: {e}")
    
    # ==================== 数据查询API ====================
    
    def get_positions(self, *args, **kwargs) -> Dict[str, Any]:
        """获取持仓信息"""
        try:
            # 获取所有持仓
            positions = self.position_manager.get_all_positions()
            
            # 转换为字典格式
            positions_data = []
            for position in positions:
                position_dict = {
                    'symbol': position.symbol,
                    'side': position.side.value,
                    'size': position.size,
                    'entry_price': position.entry_price,
                    'current_price': position.current_price,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'leverage': position.leverage,
                    'margin': position.margin,
                    'status': position.status.value,
                    'created_at': position.created_at.isoformat(),
                    'updated_at': position.updated_at.isoformat()
                }
                positions_data.append(position_dict)
            
            return self._format_response(True, positions_data, "持仓信息获取成功")
            
        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return self._format_response(False, error=f"获取持仓信息失败: {e}")
    
    def get_position_summary(self, *args, **kwargs) -> Dict[str, Any]:
        """获取仓位汇总"""
        try:
            # 获取仓位汇总统计
            summary = self.position_manager.calculate_position_summary()
            
            summary_data = {
                'total_positions': summary.total_positions,
                'active_positions': summary.active_positions,
                'total_unrealized_pnl': summary.total_unrealized_pnl,
                'total_realized_pnl': summary.total_realized_pnl,
                'total_margin': summary.total_margin,
                'win_rate': summary.win_rate,
                'profit_factor': summary.profit_factor,
                'sharpe_ratio': summary.sharpe_ratio,
                'max_drawdown': summary.max_drawdown,
                'average_win': summary.average_win,
                'average_loss': summary.average_loss
            }
            
            return self._format_response(True, summary_data, "仓位汇总获取成功")
            
        except Exception as e:
            logger.error(f"获取仓位汇总失败: {e}")
            return self._format_response(False, error=f"获取仓位汇总失败: {e}")
    
    def get_market_data(self, symbol: str = 'BTC/USDT:USDT', *args, **kwargs) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            # 获取市场数据
            market_data = self.market_data_service.get_market_data(symbol)
            
            if market_data.get('success', False):
                return self._format_response(True, market_data['data'], "市场数据获取成功")
            else:
                return self._format_response(False, error="市场数据获取失败")
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return self._format_response(False, error=f"获取市场数据失败: {e}")
    
    def get_task_status(self, *args, **kwargs) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            # 获取调度器统计
            scheduler_stats = self.task_scheduler.get_scheduler_stats()
            
            # 获取任务汇总
            task_summary = self.task_scheduler.get_task_summary()
            
            task_status = {
                'scheduler_stats': scheduler_stats,
                'task_summary': task_summary,
                'last_update': datetime.now().isoformat()
            }
            
            return self._format_response(True, task_status, "任务状态获取成功")
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return self._format_response(False, error=f"获取任务状态失败: {e}")
    
    # ==================== 工具方法 ====================
    
    def ping(self, *args, **kwargs) -> Dict[str, Any]:
        """API连通性测试"""
        return self._format_response(True, {'timestamp': datetime.now().isoformat()}, "API连接正常")
    
    def get_api_stats(self, *args, **kwargs) -> Dict[str, Any]:
        """获取API调用统计"""
        try:
            return self._format_response(True, self.api_stats.copy(), "API统计获取成功")

        except Exception as e:
            logger.error(f"获取API统计失败: {e}")
            return self._format_response(False, error=f"获取API统计失败: {e}")

    def get_account_balance(self, *args, **kwargs) -> Dict[str, Any]:
        """获取账户余额信息"""
        try:
            # 获取账户余额
            balance_info = self.exchange_service.get_account_balance('okx')

            if not balance_info:
                return self._format_response(False, error="获取账户余额失败")

            # 计算总资产（USDT等价）
            total_balance = 0
            available_balance = 0
            used_margin = 0

            # 处理USDT余额
            if 'total' in balance_info and 'USDT' in balance_info['total']:
                total_balance += balance_info['total']['USDT']

            if 'free' in balance_info and 'USDT' in balance_info['free']:
                available_balance += balance_info['free']['USDT']

            if 'used' in balance_info and 'USDT' in balance_info['used']:
                used_margin += balance_info['used']['USDT']

            # 处理BTC余额（转换为USDT等价）
            if 'total' in balance_info and 'BTC' in balance_info['total']:
                btc_amount = balance_info['total']['BTC']
                if btc_amount > 0:
                    # 获取BTC价格
                    try:
                        ticker = self.exchange_service.get_ticker('okx', 'BTC/USDT:USDT')
                        if ticker and 'last' in ticker:
                            btc_price = ticker['last']
                            total_balance += btc_amount * btc_price

                            # BTC的可用余额
                            if 'free' in balance_info and 'BTC' in balance_info['free']:
                                available_balance += balance_info['free']['BTC'] * btc_price
                    except Exception as e:
                        logger.warning(f"获取BTC价格失败: {e}")

            balance_data = {
                'total_balance': round(total_balance, 2),
                'available_balance': round(available_balance, 2),
                'used_margin': round(used_margin, 2),
                'timestamp': balance_info.get('timestamp', datetime.now().isoformat()),
                'raw_balance': balance_info  # 原始余额数据
            }

            return self._format_response(True, balance_data, "账户余额获取成功")

        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return self._format_response(False, error=f"获取账户余额失败: {e}")


# 全局API实例
_webview_api_instance = None


def get_webview_api() -> WebviewAPI:
    """获取WebviewAPI实例"""
    global _webview_api_instance
    if _webview_api_instance is None:
        _webview_api_instance = WebviewAPI()
    return _webview_api_instance
