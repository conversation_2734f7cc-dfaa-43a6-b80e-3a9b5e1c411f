#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek加密货币永续合约全自动量化交易系统
主程序入口文件

Author: SuperBot Team
Date: 2025-01-04
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置基础日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('superbot.log', encoding='utf-8')
        ]
    )

def main():
    """主程序入口"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("=" * 60)
        logger.info("DeepSeek加密货币永续合约全自动量化交易系统")
        logger.info("系统启动中...")
        logger.info("=" * 60)
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("Python版本过低，需要Python 3.8或更高版本")
            sys.exit(1)
        
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"项目根目录: {project_root}")
        
        # 导入和启动主要模块
        logger.info("正在启动用户界面...")

        from src.ui import SuperBotWebviewApp

        # 创建应用实例
        app = SuperBotWebviewApp()

        logger.info("用户界面创建成功，正在启动...")

        # 启动应用（这会阻塞直到应用关闭）
        app.start(debug=False)

        logger.info("系统已退出")
        
    except KeyboardInterrupt:
        logger.info("用户中断，系统退出")
    except Exception as e:
        logger.error(f"系统启动失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
