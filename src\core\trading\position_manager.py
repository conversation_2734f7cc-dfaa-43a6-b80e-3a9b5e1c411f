# -*- coding: utf-8 -*-
"""
仓位管理器
实现仓位信息获取、盈亏计算、风险评估、历史记录和仓位操作管理

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics

from src.services.exchange_service import get_exchange_service
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PositionStatus(Enum):
    """仓位状态枚举"""
    ACTIVE = "active"      # 活跃
    CLOSED = "closed"      # 已关闭
    PARTIAL = "partial"    # 部分平仓
    LIQUIDATED = "liquidated"  # 强制平仓


class PositionType(Enum):
    """仓位类型枚举"""
    LONG = "long"   # 多头
    SHORT = "short" # 空头


@dataclass
class PositionInfo:
    """仓位信息数据结构"""
    symbol: str
    position_id: str
    position_type: PositionType
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    realized_pnl: float
    leverage: int
    margin: float
    liquidation_price: Optional[float]
    open_time: datetime
    last_update: datetime
    status: PositionStatus
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'position_id': self.position_id,
            'position_type': self.position_type.value,
            'size': self.size,
            'entry_price': self.entry_price,
            'current_price': self.current_price,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'realized_pnl': self.realized_pnl,
            'leverage': self.leverage,
            'margin': self.margin,
            'liquidation_price': self.liquidation_price,
            'open_time': self.open_time.isoformat(),
            'last_update': self.last_update.isoformat(),
            'status': self.status.value,
            'is_profitable': self.unrealized_pnl > 0,
            'hold_duration_hours': (datetime.now() - self.open_time).total_seconds() / 3600
        }


@dataclass
class PositionSummary:
    """仓位汇总数据结构"""
    total_positions: int
    active_positions: int
    total_unrealized_pnl: float
    total_realized_pnl: float
    total_margin: float
    total_notional: float
    win_rate: float
    avg_hold_time: float
    max_drawdown: float
    sharpe_ratio: float
    profit_factor: float
    average_win: float
    average_loss: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_positions': self.total_positions,
            'active_positions': self.active_positions,
            'total_unrealized_pnl': self.total_unrealized_pnl,
            'total_realized_pnl': self.total_realized_pnl,
            'total_margin': self.total_margin,
            'total_notional': self.total_notional,
            'win_rate': self.win_rate,
            'avg_hold_time': self.avg_hold_time,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'profit_factor': self.profit_factor,
            'average_win': self.average_win,
            'average_loss': self.average_loss,
            'timestamp': self.timestamp.isoformat(),
            'total_pnl': self.total_unrealized_pnl + self.total_realized_pnl,
            'is_profitable': (self.total_unrealized_pnl + self.total_realized_pnl) > 0
        }


class PositionManager:
    """仓位管理器"""
    
    def __init__(self):
        """初始化仓位管理器"""
        self.exchange_service = get_exchange_service()
        
        # 仓位管理配置
        self.config = {
            'update_interval': 30,  # 更新间隔（秒）
            'max_position_history': 1000,  # 最大历史记录数
            'risk_warning_threshold': 0.8,  # 风险警告阈值
            'liquidation_warning_threshold': 0.9,  # 强平警告阈值
            'max_hold_time_hours': 24,  # 最大持仓时间（小时）
            'profit_target_ratio': 0.15,  # 目标盈利比例
            'stop_loss_ratio': 0.05,  # 止损比例
        }
        
        # 仓位数据缓存
        self.positions_cache = {}  # symbol -> PositionInfo
        self.position_history = []  # 历史仓位记录
        
        # 统计数据
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'last_update_time': None,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        logger.info("仓位管理器初始化完成")
    
    def get_position(self, symbol: str) -> Optional[PositionInfo]:
        """获取指定交易对的仓位信息"""
        try:
            logger.debug(f"获取仓位信息: {symbol}")
            
            # 先检查缓存
            if symbol in self.positions_cache:
                cached_position = self.positions_cache[symbol]
                # 检查缓存是否过期
                if (datetime.now() - cached_position.last_update).seconds < self.config['update_interval']:
                    self.stats['cache_hits'] += 1
                    return cached_position
            
            self.stats['cache_misses'] += 1
            
            # 从交易所获取最新仓位信息
            try:
                positions_list = self.exchange_service.get_positions('okx')
                if not positions_list:
                    logger.warning("未获取到仓位信息")
                    return None
            except Exception as e:
                logger.error(f"获取仓位信息失败: {e}")
                return None
            
            # 查找指定交易对的仓位
            for pos_data in positions_list:
                if pos_data.get('symbol') == symbol and pos_data.get('size', 0) > 0:
                    position_info = self._create_position_info(pos_data)
                    
                    # 更新缓存
                    self.positions_cache[symbol] = position_info
                    
                    logger.debug(f"仓位信息获取成功: {symbol}")
                    return position_info
            
            # 如果没有找到活跃仓位，从缓存中移除
            if symbol in self.positions_cache:
                del self.positions_cache[symbol]
            
            return None
            
        except Exception as e:
            logger.error(f"获取仓位信息异常: {e}")
            return None
    
    def get_all_positions(self) -> List[PositionInfo]:
        """获取所有活跃仓位"""
        try:
            logger.debug("获取所有活跃仓位")
            
            # 更新统计
            self.stats['total_updates'] += 1
            
            # 从交易所获取仓位信息
            try:
                positions_list = self.exchange_service.get_positions('okx')
                if not positions_list:
                    logger.warning("未获取到仓位信息")
                    self.stats['failed_updates'] += 1
                    return []
            except Exception as e:
                logger.error(f"获取所有仓位异常: {e}")
                self.stats['failed_updates'] += 1
                return []
            
            active_positions = []

            # 处理每个仓位
            for pos_data in positions_list:
                if pos_data.get('size', 0) > 0:  # 只处理有仓位的交易对
                    position_info = self._create_position_info(pos_data)
                    active_positions.append(position_info)
                    
                    # 更新缓存
                    symbol = position_info.symbol
                    self.positions_cache[symbol] = position_info
            
            # 清理缓存中已平仓的仓位
            active_symbols = {pos.symbol for pos in active_positions}
            cached_symbols = set(self.positions_cache.keys())
            
            for symbol in cached_symbols - active_symbols:
                del self.positions_cache[symbol]
            
            # 更新统计
            self.stats['successful_updates'] += 1
            self.stats['last_update_time'] = datetime.now()
            
            logger.info(f"获取到 {len(active_positions)} 个活跃仓位")
            return active_positions
            
        except Exception as e:
            logger.error(f"获取所有仓位异常: {e}")
            self.stats['failed_updates'] += 1
            return []
    
    def calculate_pnl(self, position: PositionInfo) -> Dict[str, float]:
        """计算仓位盈亏"""
        try:
            # 计算未实现盈亏
            if position.position_type == PositionType.LONG:
                unrealized_pnl = (position.current_price - position.entry_price) * position.size
            else:  # SHORT
                unrealized_pnl = (position.entry_price - position.current_price) * position.size
            
            # 计算盈亏比例
            unrealized_pnl_pct = (unrealized_pnl / (position.entry_price * position.size)) * 100
            
            # 计算总盈亏
            total_pnl = unrealized_pnl + position.realized_pnl
            
            # 计算收益率
            roi = (total_pnl / position.margin) * 100 if position.margin > 0 else 0
            
            return {
                'unrealized_pnl': unrealized_pnl,
                'unrealized_pnl_pct': unrealized_pnl_pct,
                'realized_pnl': position.realized_pnl,
                'total_pnl': total_pnl,
                'roi': roi,
                'margin_ratio': (position.margin / (position.entry_price * position.size)) * 100
            }
            
        except Exception as e:
            logger.error(f"计算盈亏异常: {e}")
            return {
                'unrealized_pnl': 0.0,
                'unrealized_pnl_pct': 0.0,
                'realized_pnl': 0.0,
                'total_pnl': 0.0,
                'roi': 0.0,
                'margin_ratio': 0.0
            }
    
    def assess_position_risk(self, position: PositionInfo) -> Dict[str, Any]:
        """评估仓位风险"""
        try:
            risk_factors = []
            risk_score = 0.0
            recommendations = []
            
            # 1. 强平风险评估
            if position.liquidation_price:
                if position.position_type == PositionType.LONG:
                    liquidation_distance = (position.current_price - position.liquidation_price) / position.current_price
                else:
                    liquidation_distance = (position.liquidation_price - position.current_price) / position.current_price
                
                if liquidation_distance < 0.05:  # 5%以内
                    risk_score += 40.0
                    risk_factors.append("强平风险极高")
                    recommendations.append("立即减仓或平仓")
                elif liquidation_distance < 0.1:  # 10%以内
                    risk_score += 25.0
                    risk_factors.append("强平风险较高")
                    recommendations.append("考虑减仓")
            
            # 2. 持仓时间风险
            hold_hours = (datetime.now() - position.open_time).total_seconds() / 3600
            max_hold_hours = self.config.get('max_hold_time_hours', 24)
            
            if hold_hours > max_hold_hours:
                risk_score += 15.0
                risk_factors.append(f"持仓时间过长: {hold_hours:.1f}小时")
                recommendations.append("考虑平仓")
            elif hold_hours > max_hold_hours * 0.8:
                risk_score += 8.0
                risk_factors.append(f"持仓时间较长: {hold_hours:.1f}小时")
            
            # 3. 盈亏风险评估
            if position.unrealized_pnl_pct < -10:  # 亏损超过10%
                risk_score += 20.0
                risk_factors.append(f"亏损较大: {position.unrealized_pnl_pct:.1f}%")
                recommendations.append("检查止损策略")
            elif position.unrealized_pnl_pct > 20:  # 盈利超过20%
                risk_score += 10.0
                risk_factors.append(f"盈利较大: {position.unrealized_pnl_pct:.1f}%")
                recommendations.append("考虑止盈")
            
            # 4. 杠杆风险评估
            if position.leverage > 15:
                risk_score += 15.0
                risk_factors.append(f"杠杆过高: {position.leverage}x")
                recommendations.append("降低杠杆")
            elif position.leverage > 10:
                risk_score += 8.0
                risk_factors.append(f"杠杆较高: {position.leverage}x")
            
            # 确定风险等级
            if risk_score >= 60:
                risk_level = "CRITICAL"
            elif risk_score >= 40:
                risk_level = "HIGH"
            elif risk_score >= 20:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            return {
                'risk_level': risk_level,
                'risk_score': min(risk_score, 100.0),
                'risk_factors': risk_factors,
                'recommendations': recommendations,
                'liquidation_distance': liquidation_distance if position.liquidation_price else None,
                'hold_hours': hold_hours,
                'is_high_risk': risk_score >= 40
            }
            
        except Exception as e:
            logger.error(f"评估仓位风险异常: {e}")
            return {
                'risk_level': 'UNKNOWN',
                'risk_score': 50.0,
                'risk_factors': ['风险评估异常'],
                'recommendations': ['检查系统状态'],
                'liquidation_distance': None,
                'hold_hours': 0,
                'is_high_risk': True
            }

    def record_position_history(self, position: PositionInfo, action: str = "update"):
        """记录仓位历史"""
        try:
            history_record = {
                'timestamp': datetime.now(),
                'action': action,  # open, update, close, partial_close
                'position_data': position.to_dict()
            }

            self.position_history.append(history_record)

            # 限制历史记录数量
            max_history = self.config.get('max_position_history', 1000)
            if len(self.position_history) > max_history:
                self.position_history = self.position_history[-max_history:]

            logger.debug(f"记录仓位历史: {position.symbol} {action}")

        except Exception as e:
            logger.error(f"记录仓位历史异常: {e}")

    def get_position_history(self, symbol: Optional[str] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """获取仓位历史记录"""
        try:
            history = self.position_history

            # 按交易对过滤
            if symbol:
                history = [
                    record for record in history
                    if record['position_data'].get('symbol') == symbol
                ]

            # 按时间倒序排列并限制数量
            history = sorted(history, key=lambda x: x['timestamp'], reverse=True)

            return history[:limit]

        except Exception as e:
            logger.error(f"获取仓位历史异常: {e}")
            return []

    def calculate_position_summary(self) -> PositionSummary:
        """计算仓位汇总统计"""
        try:
            logger.debug("计算仓位汇总统计")

            # 获取所有活跃仓位
            active_positions = self.get_all_positions()

            # 基础统计
            total_positions = len(self.position_history)
            active_positions_count = len(active_positions)

            # 盈亏统计
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in active_positions)
            total_realized_pnl = sum(pos.realized_pnl for pos in active_positions)
            total_margin = sum(pos.margin for pos in active_positions)
            total_notional = sum(pos.entry_price * pos.size for pos in active_positions)

            # 计算胜率
            win_rate = self._calculate_win_rate()

            # 计算平均持仓时间
            avg_hold_time = self._calculate_avg_hold_time(active_positions)

            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown()

            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio()

            # 计算盈利因子
            profit_factor = self._calculate_profit_factor()

            # 计算平均盈利和亏损
            average_win, average_loss = self._calculate_average_win_loss()

            summary = PositionSummary(
                total_positions=total_positions,
                active_positions=active_positions_count,
                total_unrealized_pnl=total_unrealized_pnl,
                total_realized_pnl=total_realized_pnl,
                total_margin=total_margin,
                total_notional=total_notional,
                win_rate=win_rate,
                avg_hold_time=avg_hold_time,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                profit_factor=profit_factor,
                average_win=average_win,
                average_loss=average_loss,
                timestamp=datetime.now()
            )

            logger.info(f"仓位汇总计算完成: {active_positions_count}个活跃仓位")
            return summary

        except Exception as e:
            logger.error(f"计算仓位汇总异常: {e}")
            return PositionSummary(
                total_positions=0,
                active_positions=0,
                total_unrealized_pnl=0.0,
                total_realized_pnl=0.0,
                total_margin=0.0,
                total_notional=0.0,
                win_rate=0.0,
                avg_hold_time=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                profit_factor=0.0,
                average_win=0.0,
                average_loss=0.0,
                timestamp=datetime.now()
            )

    def merge_positions(self, symbol: str, positions: List[Dict[str, Any]]) -> Optional[PositionInfo]:
        """合并同一交易对的多个仓位"""
        try:
            if not positions:
                return None

            logger.info(f"合并仓位: {symbol}, 数量: {len(positions)}")

            # 检查仓位方向是否一致
            position_types = set(pos.get('side', '').lower() for pos in positions)
            if len(position_types) > 1:
                logger.error("无法合并不同方向的仓位")
                return None

            position_type = PositionType.LONG if 'long' in position_types else PositionType.SHORT

            # 计算合并后的数据
            total_size = sum(pos.get('size', 0) for pos in positions)
            total_notional = sum(pos.get('size', 0) * pos.get('entry_price', 0) for pos in positions)
            weighted_entry_price = total_notional / total_size if total_size > 0 else 0

            total_margin = sum(pos.get('margin', 0) for pos in positions)
            total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in positions)
            total_realized_pnl = sum(pos.get('realized_pnl', 0) for pos in positions)

            # 使用第一个仓位的其他信息
            first_pos = positions[0]
            current_price = first_pos.get('mark_price', first_pos.get('current_price', 0))

            # 创建合并后的仓位信息
            merged_position = PositionInfo(
                symbol=symbol,
                position_id=f"merged_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                position_type=position_type,
                size=total_size,
                entry_price=weighted_entry_price,
                current_price=current_price,
                unrealized_pnl=total_unrealized_pnl,
                unrealized_pnl_pct=(total_unrealized_pnl / total_notional * 100) if total_notional > 0 else 0,
                realized_pnl=total_realized_pnl,
                leverage=first_pos.get('leverage', 1),
                margin=total_margin,
                liquidation_price=first_pos.get('liquidation_price'),
                open_time=datetime.now(),
                last_update=datetime.now(),
                status=PositionStatus.ACTIVE
            )

            # 记录合并操作
            self.record_position_history(merged_position, "merge")

            logger.info(f"仓位合并完成: {symbol}")
            return merged_position

        except Exception as e:
            logger.error(f"合并仓位异常: {e}")
            return None

    def split_position(self, position: PositionInfo, split_ratio: float) -> Tuple[Optional[PositionInfo], Optional[PositionInfo]]:
        """分割仓位"""
        try:
            if not (0 < split_ratio < 1):
                logger.error("分割比例必须在0和1之间")
                return None, None

            logger.info(f"分割仓位: {position.symbol}, 比例: {split_ratio}")

            # 计算分割后的数据
            split_size = position.size * split_ratio
            remaining_size = position.size * (1 - split_ratio)

            split_margin = position.margin * split_ratio
            remaining_margin = position.margin * (1 - split_ratio)

            split_unrealized_pnl = position.unrealized_pnl * split_ratio
            remaining_unrealized_pnl = position.unrealized_pnl * (1 - split_ratio)

            # 创建分割后的第一个仓位
            split_position = PositionInfo(
                symbol=position.symbol,
                position_id=f"{position.position_id}_split1",
                position_type=position.position_type,
                size=split_size,
                entry_price=position.entry_price,
                current_price=position.current_price,
                unrealized_pnl=split_unrealized_pnl,
                unrealized_pnl_pct=position.unrealized_pnl_pct,
                realized_pnl=position.realized_pnl * split_ratio,
                leverage=position.leverage,
                margin=split_margin,
                liquidation_price=position.liquidation_price,
                open_time=position.open_time,
                last_update=datetime.now(),
                status=PositionStatus.PARTIAL
            )

            # 创建分割后的第二个仓位
            remaining_position = PositionInfo(
                symbol=position.symbol,
                position_id=f"{position.position_id}_split2",
                position_type=position.position_type,
                size=remaining_size,
                entry_price=position.entry_price,
                current_price=position.current_price,
                unrealized_pnl=remaining_unrealized_pnl,
                unrealized_pnl_pct=position.unrealized_pnl_pct,
                realized_pnl=position.realized_pnl * (1 - split_ratio),
                leverage=position.leverage,
                margin=remaining_margin,
                liquidation_price=position.liquidation_price,
                open_time=position.open_time,
                last_update=datetime.now(),
                status=PositionStatus.ACTIVE
            )

            # 记录分割操作
            self.record_position_history(split_position, "split")
            self.record_position_history(remaining_position, "split")

            logger.info(f"仓位分割完成: {position.symbol}")
            return split_position, remaining_position

        except Exception as e:
            logger.error(f"分割仓位异常: {e}")
            return None, None

    def _create_position_info(self, pos_data: Dict[str, Any]) -> PositionInfo:
        """从交易所数据创建仓位信息对象"""
        try:
            symbol = pos_data.get('symbol', '')
            side = pos_data.get('side', '').lower()
            position_type = PositionType.LONG if side == 'long' else PositionType.SHORT

            size = float(pos_data.get('size', 0))
            entry_price = float(pos_data.get('entry_price', 0))
            current_price = float(pos_data.get('mark_price', pos_data.get('current_price', 0)))

            unrealized_pnl = float(pos_data.get('unrealized_pnl', 0))
            unrealized_pnl_pct = float(pos_data.get('unrealized_pnl_pct', 0))
            realized_pnl = float(pos_data.get('realized_pnl', 0))

            leverage = int(pos_data.get('leverage', 1))
            margin = float(pos_data.get('margin', 0))
            liquidation_price = pos_data.get('liquidation_price')
            if liquidation_price is not None:
                liquidation_price = float(liquidation_price)

            # 生成仓位ID
            position_id = pos_data.get('id', f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

            position_info = PositionInfo(
                symbol=symbol,
                position_id=position_id,
                position_type=position_type,
                size=size,
                entry_price=entry_price,
                current_price=current_price,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_pct=unrealized_pnl_pct,
                realized_pnl=realized_pnl,
                leverage=leverage,
                margin=margin,
                liquidation_price=liquidation_price,
                open_time=datetime.now(),  # 实际应该从交易所获取
                last_update=datetime.now(),
                status=PositionStatus.ACTIVE
            )

            return position_info

        except Exception as e:
            logger.error(f"创建仓位信息对象异常: {e}")
            raise

    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        try:
            if not self.position_history:
                return 0.0

            closed_positions = [
                record for record in self.position_history
                if record['action'] in ['close', 'partial_close']
            ]

            if not closed_positions:
                return 0.0

            winning_positions = [
                pos for pos in closed_positions
                if pos['position_data'].get('unrealized_pnl', 0) + pos['position_data'].get('realized_pnl', 0) > 0
            ]

            return (len(winning_positions) / len(closed_positions)) * 100

        except Exception as e:
            logger.error(f"计算胜率异常: {e}")
            return 0.0

    def _calculate_avg_hold_time(self, positions: List[PositionInfo]) -> float:
        """计算平均持仓时间（小时）"""
        try:
            if not positions:
                return 0.0

            hold_times = []
            for pos in positions:
                hold_time = (datetime.now() - pos.open_time).total_seconds() / 3600
                hold_times.append(hold_time)

            return statistics.mean(hold_times)

        except Exception as e:
            logger.error(f"计算平均持仓时间异常: {e}")
            return 0.0

    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        try:
            if not self.position_history:
                return 0.0

            # 简化的最大回撤计算
            pnl_values = []
            cumulative_pnl = 0.0

            for record in self.position_history:
                pos_data = record['position_data']
                pnl = pos_data.get('unrealized_pnl', 0) + pos_data.get('realized_pnl', 0)
                cumulative_pnl += pnl
                pnl_values.append(cumulative_pnl)

            if not pnl_values:
                return 0.0

            # 计算最大回撤
            peak = pnl_values[0]
            max_drawdown = 0.0

            for value in pnl_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / abs(peak) if peak != 0 else 0
                max_drawdown = max(max_drawdown, drawdown)

            return max_drawdown * 100  # 转换为百分比

        except Exception as e:
            logger.error(f"计算最大回撤异常: {e}")
            return 0.0

    def _calculate_sharpe_ratio(self) -> float:
        """计算夏普比率"""
        try:
            if not self.position_history:
                return 0.0

            # 简化的夏普比率计算
            returns = []

            for record in self.position_history:
                pos_data = record['position_data']
                pnl = pos_data.get('unrealized_pnl', 0) + pos_data.get('realized_pnl', 0)
                margin = pos_data.get('margin', 1)

                if margin > 0:
                    return_rate = pnl / margin
                    returns.append(return_rate)

            if len(returns) < 2:
                return 0.0

            mean_return = statistics.mean(returns)
            std_return = statistics.stdev(returns)

            if std_return == 0:
                return 0.0

            # 假设无风险利率为0
            sharpe_ratio = mean_return / std_return

            return sharpe_ratio

        except Exception as e:
            logger.error(f"计算夏普比率异常: {e}")
            return 0.0

    def _calculate_profit_factor(self) -> float:
        """计算盈利因子（总盈利/总亏损）"""
        try:
            if not self.position_history:
                return 0.0

            total_profit = 0.0
            total_loss = 0.0

            for record in self.position_history:
                pos_data = record['position_data']
                pnl = pos_data.get('unrealized_pnl', 0) + pos_data.get('realized_pnl', 0)

                if pnl > 0:
                    total_profit += pnl
                elif pnl < 0:
                    total_loss += abs(pnl)

            if total_loss == 0:
                return float('inf') if total_profit > 0 else 0.0

            profit_factor = total_profit / total_loss
            return profit_factor

        except Exception as e:
            logger.error(f"计算盈利因子异常: {e}")
            return 0.0

    def _calculate_average_win_loss(self) -> tuple[float, float]:
        """计算平均盈利和平均亏损"""
        try:
            if not self.position_history:
                return 0.0, 0.0

            wins = []
            losses = []

            for record in self.position_history:
                pos_data = record['position_data']
                pnl = pos_data.get('unrealized_pnl', 0) + pos_data.get('realized_pnl', 0)

                if pnl > 0:
                    wins.append(pnl)
                elif pnl < 0:
                    losses.append(abs(pnl))

            average_win = sum(wins) / len(wins) if wins else 0.0
            average_loss = sum(losses) / len(losses) if losses else 0.0

            return average_win, average_loss

        except Exception as e:
            logger.error(f"计算平均盈亏异常: {e}")
            return 0.0, 0.0

    def get_position_manager_stats(self) -> Dict[str, Any]:
        """获取仓位管理器统计"""
        try:
            success_rate = 0
            if self.stats['total_updates'] > 0:
                success_rate = self.stats['successful_updates'] / self.stats['total_updates'] * 100

            cache_hit_rate = 0
            total_cache_requests = self.stats['cache_hits'] + self.stats['cache_misses']
            if total_cache_requests > 0:
                cache_hit_rate = self.stats['cache_hits'] / total_cache_requests * 100

            return {
                'total_updates': self.stats['total_updates'],
                'successful_updates': self.stats['successful_updates'],
                'failed_updates': self.stats['failed_updates'],
                'update_success_rate': round(success_rate, 2),
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'cache_hit_rate': round(cache_hit_rate, 2),
                'last_update_time': self.stats['last_update_time'].isoformat() if self.stats['last_update_time'] else None,
                'cached_positions_count': len(self.positions_cache),
                'position_history_count': len(self.position_history),
                'config': self.config.copy()
            }

        except Exception as e:
            logger.error(f"获取仓位管理器统计异常: {e}")
            return {}

    def update_config(self, new_config: Dict[str, Any]):
        """更新仓位管理配置"""
        try:
            valid_keys = {
                'update_interval', 'max_position_history', 'risk_warning_threshold',
                'liquidation_warning_threshold', 'max_hold_time_hours',
                'profit_target_ratio', 'stop_loss_ratio'
            }

            for key, value in new_config.items():
                if key in valid_keys:
                    # 类型和范围验证
                    if key == 'update_interval' and isinstance(value, int) and value > 0:
                        self.config[key] = value
                    elif key == 'max_position_history' and isinstance(value, int) and value > 0:
                        self.config[key] = value
                    elif key in ['risk_warning_threshold', 'liquidation_warning_threshold',
                               'profit_target_ratio', 'stop_loss_ratio']:
                        if isinstance(value, (int, float)) and 0 <= value <= 1:
                            self.config[key] = value
                        else:
                            logger.warning(f"配置项 {key} 的值无效: {value}")
                    elif key == 'max_hold_time_hours' and isinstance(value, (int, float)) and value > 0:
                        self.config[key] = value
                    else:
                        logger.warning(f"配置项 {key} 的值无效: {value}")

            logger.info("仓位管理器配置已更新")

        except Exception as e:
            logger.error(f"更新配置异常: {e}")

    def clear_cache(self):
        """清理缓存"""
        self.positions_cache.clear()
        logger.info("仓位缓存已清理")

    def clear_history(self):
        """清理历史记录"""
        self.position_history.clear()
        logger.info("仓位历史记录已清理")

    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'last_update_time': None,
            'cache_hits': 0,
            'cache_misses': 0
        }

        logger.info("仓位管理器统计已重置")


# 全局仓位管理器实例
_position_manager_instance = None


def get_position_manager() -> PositionManager:
    """获取仓位管理器实例"""
    global _position_manager_instance
    if _position_manager_instance is None:
        _position_manager_instance = PositionManager()
    return _position_manager_instance
