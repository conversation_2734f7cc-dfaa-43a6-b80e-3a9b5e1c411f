# -*- coding: utf-8 -*-
"""
风险管理器
实现交易参数验证、仓位大小计算、杠杆控制、账户风险评估和紧急停止机制

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from src.services.exchange_service import get_exchange_service
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskType(Enum):
    """风险类型枚举"""
    POSITION_SIZE = "position_size"
    LEVERAGE = "leverage"
    ACCOUNT_BALANCE = "account_balance"
    DRAWDOWN = "drawdown"
    CONCENTRATION = "concentration"
    MARKET_VOLATILITY = "market_volatility"


@dataclass
class RiskAssessment:
    """风险评估结果数据结构"""
    risk_level: RiskLevel
    risk_score: float  # 0-100
    risk_factors: List[str]
    recommendations: List[str]
    max_position_size: float
    max_leverage: int
    stop_trading: bool
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'risk_level': self.risk_level.value,
            'risk_score': self.risk_score,
            'risk_factors': self.risk_factors,
            'recommendations': self.recommendations,
            'max_position_size': self.max_position_size,
            'max_leverage': self.max_leverage,
            'stop_trading': self.stop_trading,
            'timestamp': self.timestamp.isoformat(),
            'is_safe_to_trade': not self.stop_trading and self.risk_level != RiskLevel.CRITICAL
        }


@dataclass
class TradeValidation:
    """交易验证结果数据结构"""
    is_valid: bool
    validation_errors: List[str]
    warnings: List[str]
    adjusted_size: Optional[float]
    adjusted_leverage: Optional[int]
    risk_assessment: RiskAssessment
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'is_valid': self.is_valid,
            'validation_errors': self.validation_errors,
            'warnings': self.warnings,
            'adjusted_size': self.adjusted_size,
            'adjusted_leverage': self.adjusted_leverage,
            'risk_assessment': self.risk_assessment.to_dict(),
            'timestamp': self.timestamp.isoformat(),
            'can_proceed': self.is_valid and not self.risk_assessment.stop_trading
        }


class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        """初始化风险管理器"""
        self.exchange_service = get_exchange_service()
        
        # 风险管理配置
        self.config = {
            # 仓位风险控制
            'max_position_ratio': 0.1,  # 单笔最大仓位比例（10%）
            'max_total_position_ratio': 0.8,  # 总仓位比例上限（80%）
            'max_single_symbol_ratio': 0.3,  # 单个交易对最大比例（30%）
            
            # 杠杆控制
            'max_leverage': 20,  # 最大杠杆倍数
            'default_leverage': 10,  # 默认杠杆倍数
            'high_risk_max_leverage': 5,  # 高风险情况下最大杠杆
            
            # 账户风险控制
            'max_drawdown_ratio': 0.15,  # 最大回撤比例（15%）
            'stop_loss_ratio': 0.05,  # 单笔止损比例（5%）
            'daily_loss_limit_ratio': 0.10,  # 日亏损限制（10%）
            
            # 风险评估阈值
            'low_risk_threshold': 30,
            'medium_risk_threshold': 60,
            'high_risk_threshold': 80,
            
            # 紧急停止条件
            'emergency_stop_loss_ratio': 0.20,  # 紧急停止亏损比例（20%）
            'min_account_balance': 100.0,  # 最小账户余额
            'max_consecutive_losses': 5,  # 最大连续亏损次数
        }
        
        # 风险统计
        self.stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'risk_assessments': 0,
            'emergency_stops': 0,
            'last_assessment_time': None,
            'consecutive_losses': 0,
            'daily_pnl': 0.0,
            'max_drawdown': 0.0
        }
        
        # 风险状态
        self.risk_state = {
            'is_emergency_stopped': False,
            'last_risk_level': RiskLevel.LOW,
            'risk_factors_history': [],
            'stop_trading_until': None
        }
        
        logger.info("风险管理器初始化完成")
    
    def validate_trade(self, symbol: str, side: str, size: float, 
                      leverage: int = 1, price: Optional[float] = None) -> TradeValidation:
        """验证交易参数"""
        try:
            logger.info(f"验证交易参数: {symbol} {side} {size} 杠杆{leverage}x")
            
            validation_errors = []
            warnings = []
            adjusted_size = None
            adjusted_leverage = None
            
            # 更新统计
            self.stats['total_validations'] += 1
            
            # 1. 基础参数验证
            basic_errors = self._validate_basic_params(symbol, side, size, leverage, price)
            validation_errors.extend(basic_errors)
            
            # 2. 账户状态检查
            account_errors, account_warnings = self._check_account_status()
            validation_errors.extend(account_errors)
            warnings.extend(account_warnings)
            
            # 3. 仓位大小验证和调整
            size_result = self._validate_and_adjust_position_size(symbol, size, leverage)
            if size_result['errors']:
                validation_errors.extend(size_result['errors'])
            if size_result['warnings']:
                warnings.extend(size_result['warnings'])
            if size_result['adjusted_size'] is not None:
                adjusted_size = size_result['adjusted_size']
            
            # 4. 杠杆验证和调整
            leverage_result = self._validate_and_adjust_leverage(symbol, leverage, size)
            if leverage_result['errors']:
                validation_errors.extend(leverage_result['errors'])
            if leverage_result['warnings']:
                warnings.extend(leverage_result['warnings'])
            if leverage_result['adjusted_leverage'] is not None:
                adjusted_leverage = leverage_result['adjusted_leverage']
            
            # 5. 风险评估
            risk_assessment = self.assess_account_risk()
            
            # 6. 紧急停止检查
            if self._check_emergency_stop_conditions():
                validation_errors.append("触发紧急停止条件，禁止交易")
            
            # 7. 确定验证结果
            is_valid = len(validation_errors) == 0 and not risk_assessment.stop_trading
            
            # 更新统计
            if is_valid:
                self.stats['passed_validations'] += 1
            else:
                self.stats['failed_validations'] += 1
            
            validation_result = TradeValidation(
                is_valid=is_valid,
                validation_errors=validation_errors,
                warnings=warnings,
                adjusted_size=adjusted_size,
                adjusted_leverage=adjusted_leverage,
                risk_assessment=risk_assessment,
                timestamp=datetime.now()
            )
            
            logger.info(f"交易验证完成: {'通过' if is_valid else '失败'}, 错误数: {len(validation_errors)}")
            return validation_result
            
        except Exception as e:
            logger.error(f"交易验证异常: {e}")
            # 返回失败的验证结果
            return TradeValidation(
                is_valid=False,
                validation_errors=[f"验证异常: {e}"],
                warnings=[],
                adjusted_size=None,
                adjusted_leverage=None,
                risk_assessment=RiskAssessment(
                    risk_level=RiskLevel.CRITICAL,
                    risk_score=100.0,
                    risk_factors=["系统异常"],
                    recommendations=["停止交易，检查系统"],
                    max_position_size=0.0,
                    max_leverage=1,
                    stop_trading=True,
                    timestamp=datetime.now()
                ),
                timestamp=datetime.now()
            )
    
    def calculate_position_size(self, symbol: str, confidence: float, 
                              account_balance: float, risk_ratio: float = 0.02) -> float:
        """计算建议仓位大小"""
        try:
            logger.info(f"计算仓位大小: {symbol} 置信度{confidence} 余额{account_balance}")
            
            # 基础仓位计算（基于风险比例）
            base_position = account_balance * risk_ratio
            
            # 根据置信度调整
            confidence_multiplier = min(confidence / 100.0, 1.0)
            adjusted_position = base_position * confidence_multiplier
            
            # 应用最大仓位限制
            max_position_ratio = self.config.get('max_position_ratio', 0.1)
            max_position = account_balance * max_position_ratio
            
            # 取较小值
            final_position = min(adjusted_position, max_position)
            
            # 风险评估调整
            risk_assessment = self.assess_account_risk()
            if risk_assessment.risk_level == RiskLevel.HIGH:
                final_position *= 0.5  # 高风险时减半
            elif risk_assessment.risk_level == RiskLevel.CRITICAL:
                final_position = 0.0  # 极高风险时禁止交易
            
            logger.info(f"计算得出仓位大小: {final_position}")
            return max(final_position, 0.0)
            
        except Exception as e:
            logger.error(f"计算仓位大小异常: {e}")
            return 0.0
    
    def assess_account_risk(self) -> RiskAssessment:
        """评估账户风险"""
        try:
            logger.debug("开始评估账户风险")
            
            risk_factors = []
            recommendations = []
            risk_score = 0.0
            
            # 更新统计
            self.stats['risk_assessments'] += 1
            self.stats['last_assessment_time'] = datetime.now()
            
            # 1. 账户余额风险
            balance_risk = self._assess_balance_risk()
            risk_score += balance_risk['score']
            risk_factors.extend(balance_risk['factors'])
            recommendations.extend(balance_risk['recommendations'])
            
            # 2. 持仓集中度风险
            concentration_risk = self._assess_concentration_risk()
            risk_score += concentration_risk['score']
            risk_factors.extend(concentration_risk['factors'])
            recommendations.extend(concentration_risk['recommendations'])
            
            # 3. 杠杆风险
            leverage_risk = self._assess_leverage_risk()
            risk_score += leverage_risk['score']
            risk_factors.extend(leverage_risk['factors'])
            recommendations.extend(leverage_risk['recommendations'])
            
            # 4. 回撤风险
            drawdown_risk = self._assess_drawdown_risk()
            risk_score += drawdown_risk['score']
            risk_factors.extend(drawdown_risk['factors'])
            recommendations.extend(drawdown_risk['recommendations'])
            
            # 5. 市场波动风险
            volatility_risk = self._assess_market_volatility_risk()
            risk_score += volatility_risk['score']
            risk_factors.extend(volatility_risk['factors'])
            recommendations.extend(volatility_risk['recommendations'])
            
            # 确定风险等级
            risk_level = self._determine_risk_level(risk_score)
            
            # 计算最大仓位和杠杆
            max_position_size, max_leverage = self._calculate_risk_limits(risk_level, risk_score)
            
            # 确定是否停止交易
            stop_trading = (
                risk_level == RiskLevel.CRITICAL or
                self.risk_state['is_emergency_stopped'] or
                self._check_emergency_stop_conditions()
            )
            
            # 更新风险状态
            self.risk_state['last_risk_level'] = risk_level
            self.risk_state['risk_factors_history'].append({
                'timestamp': datetime.now(),
                'risk_level': risk_level.value,
                'risk_score': risk_score,
                'factors': risk_factors
            })
            
            # 保持历史记录在合理范围内
            if len(self.risk_state['risk_factors_history']) > 100:
                self.risk_state['risk_factors_history'] = self.risk_state['risk_factors_history'][-50:]
            
            risk_assessment = RiskAssessment(
                risk_level=risk_level,
                risk_score=min(risk_score, 100.0),
                risk_factors=risk_factors,
                recommendations=recommendations,
                max_position_size=max_position_size,
                max_leverage=max_leverage,
                stop_trading=stop_trading,
                timestamp=datetime.now()
            )
            
            logger.info(f"风险评估完成: {risk_level.value}, 分数: {risk_score:.1f}")
            return risk_assessment
            
        except Exception as e:
            logger.error(f"风险评估异常: {e}")
            return RiskAssessment(
                risk_level=RiskLevel.CRITICAL,
                risk_score=100.0,
                risk_factors=["风险评估系统异常"],
                recommendations=["立即停止交易，检查系统"],
                max_position_size=0.0,
                max_leverage=1,
                stop_trading=True,
                timestamp=datetime.now()
            )

    def _validate_basic_params(self, symbol: str, side: str, size: float,
                              leverage: int, price: Optional[float]) -> List[str]:
        """验证基础交易参数"""
        errors = []

        try:
            # 验证交易对
            if not symbol or not isinstance(symbol, str):
                errors.append("无效的交易对")

            # 验证交易方向
            if side.lower() not in ['buy', 'sell', 'long', 'short']:
                errors.append("无效的交易方向")

            # 验证仓位大小
            if not isinstance(size, (int, float)) or size <= 0:
                errors.append("仓位大小必须大于0")
            elif size > 100:  # 假设最大100个单位
                errors.append("仓位大小过大")

            # 验证杠杆
            if not isinstance(leverage, int) or leverage < 1:
                errors.append("杠杆倍数必须大于等于1")
            elif leverage > self.config.get('max_leverage', 20):
                errors.append(f"杠杆倍数不能超过{self.config.get('max_leverage', 20)}")

            # 验证价格（如果提供）
            if price is not None:
                if not isinstance(price, (int, float)) or price <= 0:
                    errors.append("价格必须大于0")

        except Exception as e:
            errors.append(f"参数验证异常: {e}")

        return errors

    def _check_account_status(self) -> Tuple[List[str], List[str]]:
        """检查账户状态"""
        errors = []
        warnings = []

        try:
            # 获取账户信息
            account_info = self.exchange_service.get_account_balance()

            if not account_info.get('success', False):
                errors.append("无法获取账户信息")
                return errors, warnings

            balance_data = account_info.get('data', {})
            available_balance = balance_data.get('available', 0)
            total_balance = balance_data.get('total', 0)

            # 检查最小余额
            min_balance = self.config.get('min_account_balance', 100.0)
            if available_balance < min_balance:
                errors.append(f"可用余额不足，当前: {available_balance}, 最小要求: {min_balance}")

            # 检查余额使用率
            if total_balance > 0:
                usage_ratio = (total_balance - available_balance) / total_balance
                if usage_ratio > 0.9:  # 90%以上资金被占用
                    warnings.append(f"资金使用率过高: {usage_ratio*100:.1f}%")

        except Exception as e:
            errors.append(f"账户状态检查异常: {e}")

        return errors, warnings

    def _validate_and_adjust_position_size(self, symbol: str, size: float,
                                         leverage: int) -> Dict[str, Any]:
        """验证和调整仓位大小"""
        result = {
            'errors': [],
            'warnings': [],
            'adjusted_size': None
        }

        try:
            # 获取账户余额
            account_info = self.exchange_service.get_account_balance()
            if not account_info.get('success', False):
                result['errors'].append("无法获取账户余额")
                return result

            available_balance = account_info.get('data', {}).get('available', 0)

            # 计算所需保证金
            required_margin = size * leverage

            # 检查保证金是否足够
            if required_margin > available_balance:
                result['errors'].append(f"保证金不足，需要: {required_margin}, 可用: {available_balance}")
                return result

            # 检查单笔最大仓位比例
            max_position_ratio = self.config.get('max_position_ratio', 0.1)
            max_position_value = available_balance * max_position_ratio

            if required_margin > max_position_value:
                # 调整仓位大小
                adjusted_size = max_position_value / leverage
                result['adjusted_size'] = round(adjusted_size, 6)
                result['warnings'].append(f"仓位大小已调整为: {result['adjusted_size']}")

            # 检查总仓位比例
            current_positions = self._get_current_positions_value()
            total_position_ratio = self.config.get('max_total_position_ratio', 0.8)
            max_total_position = available_balance * total_position_ratio

            if current_positions + required_margin > max_total_position:
                result['warnings'].append("总仓位比例接近上限")

            # 检查单个交易对集中度
            symbol_positions = self._get_symbol_positions_value(symbol)
            max_symbol_ratio = self.config.get('max_single_symbol_ratio', 0.3)
            max_symbol_position = available_balance * max_symbol_ratio

            if symbol_positions + required_margin > max_symbol_position:
                result['warnings'].append(f"交易对 {symbol} 仓位集中度过高")

        except Exception as e:
            result['errors'].append(f"仓位大小验证异常: {e}")

        return result

    def _validate_and_adjust_leverage(self, symbol: str, leverage: int,
                                    size: float) -> Dict[str, Any]:
        """验证和调整杠杆倍数"""
        result = {
            'errors': [],
            'warnings': [],
            'adjusted_leverage': None
        }

        try:
            # 获取当前风险等级
            risk_level = self.risk_state.get('last_risk_level', RiskLevel.LOW)

            # 根据风险等级确定最大杠杆
            if risk_level == RiskLevel.HIGH:
                max_leverage = self.config.get('high_risk_max_leverage', 5)
            elif risk_level == RiskLevel.CRITICAL:
                max_leverage = 1
            else:
                max_leverage = self.config.get('max_leverage', 20)

            # 检查杠杆是否超限
            if leverage > max_leverage:
                result['adjusted_leverage'] = max_leverage
                result['warnings'].append(f"杠杆已调整为: {max_leverage}x (风险等级: {risk_level.value})")

            # 检查高杠杆警告
            if leverage > 10:
                result['warnings'].append(f"使用高杠杆 {leverage}x，请注意风险")

        except Exception as e:
            result['errors'].append(f"杠杆验证异常: {e}")

        return result

    def _assess_balance_risk(self) -> Dict[str, Any]:
        """评估账户余额风险"""
        result = {
            'score': 0.0,
            'factors': [],
            'recommendations': []
        }

        try:
            account_info = self.exchange_service.get_account_balance()
            if not account_info.get('success', False):
                result['score'] = 30.0
                result['factors'].append("无法获取账户余额")
                return result

            balance_data = account_info.get('data', {})
            available_balance = balance_data.get('available', 0)
            total_balance = balance_data.get('total', 0)

            # 检查余额充足性
            min_balance = self.config.get('min_account_balance', 100.0)
            if available_balance < min_balance * 2:
                result['score'] += 20.0
                result['factors'].append("账户余额偏低")
                result['recommendations'].append("考虑增加账户资金")

            # 检查资金使用率
            if total_balance > 0:
                usage_ratio = (total_balance - available_balance) / total_balance
                if usage_ratio > 0.8:
                    result['score'] += 15.0
                    result['factors'].append("资金使用率过高")
                    result['recommendations'].append("减少持仓或增加资金")

        except Exception as e:
            result['score'] = 20.0
            result['factors'].append(f"余额风险评估异常: {e}")

        return result

    def _assess_concentration_risk(self) -> Dict[str, Any]:
        """评估持仓集中度风险"""
        result = {
            'score': 0.0,
            'factors': [],
            'recommendations': []
        }

        try:
            # 获取当前持仓
            positions = self.exchange_service.get_positions()
            if not positions.get('success', False):
                return result

            positions_data = positions.get('data', [])
            if not positions_data:
                return result

            # 计算持仓集中度
            total_value = sum(pos.get('notional', 0) for pos in positions_data)
            if total_value == 0:
                return result

            # 检查单个交易对集中度
            symbol_concentrations = {}
            for pos in positions_data:
                symbol = pos.get('symbol', '')
                value = pos.get('notional', 0)
                concentration = value / total_value
                symbol_concentrations[symbol] = concentration

                if concentration > 0.5:  # 单个交易对超过50%
                    result['score'] += 25.0
                    result['factors'].append(f"交易对 {symbol} 集中度过高: {concentration*100:.1f}%")
                    result['recommendations'].append(f"分散 {symbol} 的持仓")
                elif concentration > 0.3:  # 单个交易对超过30%
                    result['score'] += 10.0
                    result['factors'].append(f"交易对 {symbol} 集中度较高: {concentration*100:.1f}%")

            # 检查持仓数量
            if len(positions_data) == 1:
                result['score'] += 15.0
                result['factors'].append("持仓过于集中，只有一个交易对")
                result['recommendations'].append("考虑分散投资到多个交易对")

        except Exception as e:
            result['score'] = 15.0
            result['factors'].append(f"集中度风险评估异常: {e}")

        return result

    def _assess_leverage_risk(self) -> Dict[str, Any]:
        """评估杠杆风险"""
        result = {
            'score': 0.0,
            'factors': [],
            'recommendations': []
        }

        try:
            # 获取当前持仓
            positions = self.exchange_service.get_positions()
            if not positions.get('success', False):
                return result

            positions_data = positions.get('data', [])
            if not positions_data:
                return result

            # 计算平均杠杆和最大杠杆
            leverages = [pos.get('leverage', 1) for pos in positions_data if pos.get('leverage', 1) > 1]

            if leverages:
                max_leverage = max(leverages)
                avg_leverage = sum(leverages) / len(leverages)

                # 评估最大杠杆风险
                if max_leverage > 15:
                    result['score'] += 30.0
                    result['factors'].append(f"使用极高杠杆: {max_leverage}x")
                    result['recommendations'].append("降低杠杆倍数")
                elif max_leverage > 10:
                    result['score'] += 15.0
                    result['factors'].append(f"使用高杠杆: {max_leverage}x")
                    result['recommendations'].append("考虑降低杠杆")

                # 评估平均杠杆风险
                if avg_leverage > 8:
                    result['score'] += 10.0
                    result['factors'].append(f"平均杠杆较高: {avg_leverage:.1f}x")

        except Exception as e:
            result['score'] = 10.0
            result['factors'].append(f"杠杆风险评估异常: {e}")

        return result

    def _assess_drawdown_risk(self) -> Dict[str, Any]:
        """评估回撤风险"""
        result = {
            'score': 0.0,
            'factors': [],
            'recommendations': []
        }

        try:
            # 获取账户信息
            account_info = self.exchange_service.get_account_balance()
            if not account_info.get('success', False):
                return result

            balance_data = account_info.get('data', {})
            total_balance = balance_data.get('total', 0)
            unrealized_pnl = balance_data.get('unrealized_pnl', 0)

            # 计算当前回撤（简化计算）
            if total_balance > 0 and unrealized_pnl < 0:
                drawdown_ratio = abs(unrealized_pnl) / total_balance

                max_drawdown_ratio = self.config.get('max_drawdown_ratio', 0.15)

                if drawdown_ratio > max_drawdown_ratio:
                    result['score'] += 40.0
                    result['factors'].append(f"回撤过大: {drawdown_ratio*100:.1f}%")
                    result['recommendations'].append("考虑减仓或停止交易")
                elif drawdown_ratio > max_drawdown_ratio * 0.7:
                    result['score'] += 20.0
                    result['factors'].append(f"回撤较大: {drawdown_ratio*100:.1f}%")
                    result['recommendations'].append("密切关注风险")

            # 检查连续亏损
            consecutive_losses = self.stats.get('consecutive_losses', 0)
            max_consecutive = self.config.get('max_consecutive_losses', 5)

            if consecutive_losses >= max_consecutive:
                result['score'] += 30.0
                result['factors'].append(f"连续亏损次数过多: {consecutive_losses}")
                result['recommendations'].append("暂停交易，检查策略")
            elif consecutive_losses >= max_consecutive * 0.7:
                result['score'] += 15.0
                result['factors'].append(f"连续亏损较多: {consecutive_losses}")

        except Exception as e:
            result['score'] = 15.0
            result['factors'].append(f"回撤风险评估异常: {e}")

        return result

    def _assess_market_volatility_risk(self) -> Dict[str, Any]:
        """评估市场波动风险"""
        result = {
            'score': 0.0,
            'factors': [],
            'recommendations': []
        }

        try:
            # 这里可以添加市场波动率计算
            # 目前使用简化的评估方法

            # 获取当前持仓
            positions = self.exchange_service.get_positions()
            if not positions.get('success', False):
                return result

            positions_data = positions.get('data', [])

            # 检查持仓的未实现盈亏波动
            total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in positions_data)
            total_notional = sum(pos.get('notional', 0) for pos in positions_data)

            if total_notional > 0:
                pnl_ratio = abs(total_unrealized_pnl) / total_notional

                if pnl_ratio > 0.1:  # 10%以上的波动
                    result['score'] += 20.0
                    result['factors'].append(f"持仓波动较大: {pnl_ratio*100:.1f}%")
                    result['recommendations'].append("考虑降低仓位或杠杆")
                elif pnl_ratio > 0.05:  # 5%以上的波动
                    result['score'] += 10.0
                    result['factors'].append(f"持仓有一定波动: {pnl_ratio*100:.1f}%")

        except Exception as e:
            result['score'] = 10.0
            result['factors'].append(f"市场波动风险评估异常: {e}")

        return result

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """根据风险分数确定风险等级"""
        if risk_score >= self.config.get('high_risk_threshold', 80):
            return RiskLevel.CRITICAL
        elif risk_score >= self.config.get('medium_risk_threshold', 60):
            return RiskLevel.HIGH
        elif risk_score >= self.config.get('low_risk_threshold', 30):
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _calculate_risk_limits(self, risk_level: RiskLevel,
                             risk_score: float) -> Tuple[float, int]:
        """根据风险等级计算限制"""
        base_position_ratio = self.config.get('max_position_ratio', 0.1)
        base_leverage = self.config.get('max_leverage', 20)

        if risk_level == RiskLevel.CRITICAL:
            return 0.0, 1
        elif risk_level == RiskLevel.HIGH:
            return base_position_ratio * 0.3, min(base_leverage // 4, 5)
        elif risk_level == RiskLevel.MEDIUM:
            return base_position_ratio * 0.6, min(base_leverage // 2, 10)
        else:
            return base_position_ratio, base_leverage

    def _check_emergency_stop_conditions(self) -> bool:
        """检查紧急停止条件"""
        try:
            # 检查是否已经紧急停止
            if self.risk_state.get('is_emergency_stopped', False):
                return True

            # 检查停止交易时间限制
            stop_until = self.risk_state.get('stop_trading_until')
            if stop_until and datetime.now() < stop_until:
                return True

            # 检查账户余额
            account_info = self.exchange_service.get_account_balance()
            if account_info.get('success', False):
                available_balance = account_info.get('data', {}).get('available', 0)
                min_balance = self.config.get('min_account_balance', 100.0)

                if available_balance < min_balance:
                    self._trigger_emergency_stop("账户余额不足")
                    return True

            # 检查连续亏损
            consecutive_losses = self.stats.get('consecutive_losses', 0)
            max_consecutive = self.config.get('max_consecutive_losses', 5)

            if consecutive_losses >= max_consecutive:
                self._trigger_emergency_stop("连续亏损次数过多")
                return True

            return False

        except Exception as e:
            logger.error(f"检查紧急停止条件异常: {e}")
            return True  # 异常时保守处理，停止交易

    def _trigger_emergency_stop(self, reason: str):
        """触发紧急停止"""
        try:
            self.risk_state['is_emergency_stopped'] = True
            self.stats['emergency_stops'] += 1

            logger.critical(f"触发紧急停止: {reason}")

            # 设置停止交易时间（1小时后可重新评估）
            self.risk_state['stop_trading_until'] = datetime.now() + timedelta(hours=1)

        except Exception as e:
            logger.error(f"触发紧急停止异常: {e}")

    def _get_current_positions_value(self) -> float:
        """获取当前持仓总价值"""
        try:
            positions = self.exchange_service.get_positions()
            if not positions.get('success', False):
                return 0.0

            positions_data = positions.get('data', [])
            return sum(pos.get('notional', 0) for pos in positions_data)

        except Exception as e:
            logger.error(f"获取持仓总价值异常: {e}")
            return 0.0

    def _get_symbol_positions_value(self, symbol: str) -> float:
        """获取指定交易对的持仓价值"""
        try:
            positions = self.exchange_service.get_positions()
            if not positions.get('success', False):
                return 0.0

            positions_data = positions.get('data', [])
            symbol_value = 0.0

            for pos in positions_data:
                if pos.get('symbol') == symbol:
                    symbol_value += pos.get('notional', 0)

            return symbol_value

        except Exception as e:
            logger.error(f"获取交易对持仓价值异常: {e}")
            return 0.0

    def get_risk_manager_stats(self) -> Dict[str, Any]:
        """获取风险管理器统计"""
        try:
            success_rate = 0
            if self.stats['total_validations'] > 0:
                success_rate = self.stats['passed_validations'] / self.stats['total_validations'] * 100

            return {
                'total_validations': self.stats['total_validations'],
                'passed_validations': self.stats['passed_validations'],
                'failed_validations': self.stats['failed_validations'],
                'validation_success_rate': round(success_rate, 2),
                'risk_assessments': self.stats['risk_assessments'],
                'emergency_stops': self.stats['emergency_stops'],
                'consecutive_losses': self.stats['consecutive_losses'],
                'last_assessment_time': self.stats['last_assessment_time'].isoformat() if self.stats['last_assessment_time'] else None,
                'current_risk_level': self.risk_state['last_risk_level'].value,
                'is_emergency_stopped': self.risk_state['is_emergency_stopped'],
                'config': self.config.copy()
            }

        except Exception as e:
            logger.error(f"获取风险管理器统计异常: {e}")
            return {}

    def update_config(self, new_config: Dict[str, Any]):
        """更新风险管理配置"""
        try:
            valid_keys = {
                'max_position_ratio', 'max_total_position_ratio', 'max_single_symbol_ratio',
                'max_leverage', 'default_leverage', 'high_risk_max_leverage',
                'max_drawdown_ratio', 'stop_loss_ratio', 'daily_loss_limit_ratio',
                'low_risk_threshold', 'medium_risk_threshold', 'high_risk_threshold',
                'emergency_stop_loss_ratio', 'min_account_balance', 'max_consecutive_losses'
            }

            for key, value in new_config.items():
                if key in valid_keys:
                    # 类型和范围验证
                    if key.endswith('_ratio') or key.endswith('_threshold'):
                        if isinstance(value, (int, float)) and 0 <= value <= 1:
                            self.config[key] = value
                        else:
                            logger.warning(f"配置项 {key} 的值无效: {value}")
                    elif key in ['max_leverage', 'default_leverage', 'high_risk_max_leverage', 'max_consecutive_losses']:
                        if isinstance(value, int) and value > 0:
                            self.config[key] = value
                        else:
                            logger.warning(f"配置项 {key} 的值无效: {value}")
                    elif key == 'min_account_balance':
                        if isinstance(value, (int, float)) and value > 0:
                            self.config[key] = value
                        else:
                            logger.warning(f"配置项 {key} 的值无效: {value}")

            logger.info("风险管理器配置已更新")

        except Exception as e:
            logger.error(f"更新配置异常: {e}")

    def reset_emergency_stop(self):
        """重置紧急停止状态"""
        try:
            self.risk_state['is_emergency_stopped'] = False
            self.risk_state['stop_trading_until'] = None

            logger.info("紧急停止状态已重置")

        except Exception as e:
            logger.error(f"重置紧急停止状态异常: {e}")

    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'risk_assessments': 0,
            'emergency_stops': 0,
            'last_assessment_time': None,
            'consecutive_losses': 0,
            'daily_pnl': 0.0,
            'max_drawdown': 0.0
        }

        logger.info("风险管理器统计已重置")


# 全局风险管理器实例
_risk_manager_instance = None


def get_risk_manager() -> RiskManager:
    """获取风险管理器实例"""
    global _risk_manager_instance
    if _risk_manager_instance is None:
        _risk_manager_instance = RiskManager()
    return _risk_manager_instance
