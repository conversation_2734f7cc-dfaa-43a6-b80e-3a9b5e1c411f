# -*- coding: utf-8 -*-
"""
AI开仓引擎
实现市场机会分析和开仓决策功能，集成技术分析引擎和AI服务，实现智能开仓决策

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.core.ai_engines.base_engine import (
    BaseAIEngine, EngineType, AnalysisResult, 
    AIAnalysisRequest, AIAnalysisResponse, AIEngineError
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class OpenSignal:
    """开仓信号数据结构"""
    direction: AnalysisResult
    confidence: float
    entry_price: Optional[float]
    position_size: float
    leverage: int
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: str
    risk_level: str
    signal_strength: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'direction': self.direction.value,
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'position_size': self.position_size,
            'leverage': self.leverage,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'reasoning': self.reasoning,
            'risk_level': self.risk_level,
            'signal_strength': self.signal_strength,
            'timestamp': self.timestamp.isoformat(),
            'is_valid': self.is_valid()
        }
    
    def is_valid(self) -> bool:
        """检查开仓信号是否有效"""
        return (
            self.direction in [AnalysisResult.BULLISH, AnalysisResult.BEARISH] and
            0 < self.confidence <= 100 and
            self.position_size > 0 and
            1 <= self.leverage <= 100 and
            len(self.reasoning) > 0
        )


class AIOpenEngine(BaseAIEngine):
    """AI开仓引擎"""
    
    def __init__(self):
        """初始化AI开仓引擎"""
        super().__init__(EngineType.OPEN)
        
        # 开仓引擎特定配置
        self.open_config = {
            'min_confidence_threshold': 70.0,
            'max_position_size_ratio': 0.1,  # 最大仓位比例
            'default_leverage': 10,
            'max_leverage': 20,
            'stop_loss_ratio': 0.05,  # 止损比例
            'take_profit_ratio': 0.15,  # 止盈比例
            'min_signal_strength': 60.0,
            'cooldown_minutes': 30,  # 开仓冷却时间
            'max_daily_trades': 10
        }
        
        # 更新基础配置
        self.config.update(self.open_config)
        
        # 开仓统计
        self.open_stats = {
            'total_signals': 0,
            'valid_signals': 0,
            'bullish_signals': 0,
            'bearish_signals': 0,
            'last_signal_time': None,
            'daily_signals': 0,
            'last_daily_reset': datetime.now().date()
        }
        
        logger.info("AI开仓引擎初始化完成")
    
    def _generate_prompt(self, request: AIAnalysisRequest) -> str:
        """生成开仓分析提示词"""
        try:
            # 获取技术数据
            technical_data = request.technical_data
            symbol = request.symbol
            
            # 构建多时间周期技术指标描述
            timeframes_desc = self._format_timeframes_data(technical_data.get('timeframes', {}))
            
            # 获取市场摘要
            market_summary = technical_data.get('summary', {})
            
            # 构建专业化的开仓分析提示词
            prompt = f"""
你是一位专业的加密货币量化交易分析师，专门负责开仓决策分析。请基于以下技术指标数据，分析 {symbol} 的市场机会并给出开仓建议。

=== 市场概况 ===
交易对: {symbol}
分析时间: {request.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
当前市场趋势: {market_summary.get('trend_consensus', '未知')}
动量状态: {market_summary.get('momentum_consensus', '未知')}
整体信号: {market_summary.get('overall_signal', '未知')}

=== 多时间周期技术指标分析 ===
{timeframes_desc}

=== 分析要求 ===
请从以下角度进行专业分析：

1. **趋势分析**：
   - 分析多时间周期的趋势一致性
   - 判断当前趋势的强度和可持续性
   - 识别趋势转折点和突破信号

2. **动量分析**：
   - 评估RSI、STOCH、CCI、Williams %R等动量指标的状态
   - 判断是否存在超买或超卖机会
   - 分析MACD动量背离和确认信号

3. **成交量分析**：
   - 评估OBV、AD等成交量指标的支撑作用
   - 分析量价关系的健康程度
   - 判断资金流向和市场参与度

4. **波动率分析**：
   - 评估ATR市场波动率水平
   - 分析布林带位置和挤压状态
   - 判断突破的可能性和方向

5. **风险评估**：
   - 评估当前市场环境的风险水平
   - 分析潜在的不利因素
   - 给出风险控制建议

=== 输出要求 ===
请严格按照以下JSON格式返回分析结果：

{{
    "direction": "BULLISH/BEARISH/NEUTRAL",
    "confidence": 85,
    "reasoning": "详细的分析推理过程，包括技术指标解读、趋势判断、风险评估等",
    "signal_strength": 75,
    "risk_level": "LOW/MEDIUM/HIGH",
    "entry_timing": "IMMEDIATE/WAIT/AVOID",
    "key_levels": {{
        "support": 45000,
        "resistance": 52000,
        "stop_loss": 44000,
        "take_profit": 53000
    }},
    "timeframe_consensus": {{
        "short_term": "BULLISH/BEARISH/NEUTRAL",
        "medium_term": "BULLISH/BEARISH/NEUTRAL",
        "long_term": "BULLISH/BEARISH/NEUTRAL"
    }},
    "market_conditions": {{
        "trend_strength": "STRONG/MODERATE/WEAK",
        "volatility": "HIGH/MEDIUM/LOW",
        "volume_confirmation": true,
        "momentum_alignment": true
    }}
}}

注意事项：
- confidence 范围：0-100，表示分析的置信度
- signal_strength 范围：0-100，表示信号的强度
- reasoning 必须详细说明分析逻辑和依据
- 所有价格水平必须基于技术分析给出具体数值
- 必须考虑多时间周期的一致性
"""
            
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成开仓提示词失败: {e}")
            raise AIEngineError(f"生成开仓提示词失败: {e}")
    
    def _format_timeframes_data(self, timeframes_data: Dict[str, Any]) -> str:
        """格式化多时间周期数据"""
        try:
            formatted_lines = []
            
            for timeframe, tf_data in timeframes_data.items():
                if not tf_data or 'indicators' not in tf_data:
                    continue
                
                indicators = tf_data['indicators']
                current_price = tf_data.get('current_price', 0)
                price_change = tf_data.get('price_change', 0)
                price_change_pct = tf_data.get('price_change_pct', 0)
                
                formatted_lines.append(f"\n【{timeframe.upper()} 时间周期】")
                formatted_lines.append(f"当前价格: {current_price:.2f}")
                formatted_lines.append(f"价格变化: {price_change:+.2f} ({price_change_pct:+.2f}%)")
                
                # 趋势指标
                if 'trend' in indicators:
                    trend = indicators['trend']
                    formatted_lines.append("趋势指标:")
                    if trend.get('sma_20'):
                        formatted_lines.append(f"  SMA-20: {trend['sma_20']:.2f}")
                    if trend.get('ema_20'):
                        formatted_lines.append(f"  EMA-20: {trend['ema_20']:.2f}")
                    if trend.get('macd_line') and trend.get('macd_signal'):
                        formatted_lines.append(f"  MACD: {trend['macd_line']:.4f} / 信号: {trend['macd_signal']:.4f}")
                    if trend.get('adx'):
                        formatted_lines.append(f"  ADX: {trend['adx']:.2f}")
                
                # 震荡指标
                if 'momentum' in indicators:
                    momentum = indicators['momentum']
                    formatted_lines.append("震荡指标:")
                    if momentum.get('rsi'):
                        formatted_lines.append(f"  RSI: {momentum['rsi']:.2f}")
                    if momentum.get('stoch_k') and momentum.get('stoch_d'):
                        formatted_lines.append(f"  STOCH: K={momentum['stoch_k']:.2f} D={momentum['stoch_d']:.2f}")
                    if momentum.get('cci'):
                        formatted_lines.append(f"  CCI: {momentum['cci']:.2f}")
                
                # 波动率指标
                if 'volatility' in indicators:
                    volatility = indicators['volatility']
                    formatted_lines.append("波动率指标:")
                    if volatility.get('atr'):
                        formatted_lines.append(f"  ATR: {volatility['atr']:.2f}")
                    if all(volatility.get(k) for k in ['bb_upper', 'bb_middle', 'bb_lower']):
                        formatted_lines.append(f"  布林带: 上={volatility['bb_upper']:.2f} 中={volatility['bb_middle']:.2f} 下={volatility['bb_lower']:.2f}")
            
            return '\n'.join(formatted_lines)
            
        except Exception as e:
            logger.error(f"格式化时间周期数据失败: {e}")
            return "时间周期数据格式化失败"
    
    def _post_process_response(self, response: AIAnalysisResponse, 
                             request: AIAnalysisRequest) -> AIAnalysisResponse:
        """后处理AI响应"""
        try:
            # 解析额外数据
            additional_data = response.additional_data
            
            # 提取关键信息
            if isinstance(response.reasoning, str):
                try:
                    # 尝试从reasoning中提取JSON数据
                    json_start = response.reasoning.find('{')
                    json_end = response.reasoning.rfind('}') + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_str = response.reasoning[json_start:json_end]
                        parsed_data = json.loads(json_str)
                        additional_data.update(parsed_data)
                except json.JSONDecodeError:
                    pass
            
            # 更新额外数据
            response.additional_data = additional_data
            
            # 验证置信度阈值
            min_confidence = self.config.get('min_confidence_threshold', 70.0)
            if response.confidence < min_confidence:
                logger.info(f"AI分析置信度 {response.confidence} 低于阈值 {min_confidence}")
                response.direction = AnalysisResult.NEUTRAL
                response.reasoning += f"\n注意：置信度 {response.confidence} 低于开仓阈值 {min_confidence}"
            
            return response

        except Exception as e:
            logger.error(f"后处理AI响应失败: {e}")
            return response

    def generate_open_signal(self, symbol: str, exchange: str = "okx",
                           market_context: Optional[Dict[str, Any]] = None) -> OpenSignal:
        """生成开仓信号"""
        try:
            # 检查冷却时间
            if not self._check_cooldown():
                return self._create_neutral_signal("开仓冷却时间未到")

            # 检查每日交易限制
            if not self._check_daily_limit():
                return self._create_neutral_signal("已达到每日交易限制")

            # 执行AI分析
            ai_response = self.analyze(symbol, exchange, market_context)

            # 更新统计
            self._update_signal_stats(ai_response)

            # 如果AI分析失败或为中性，返回中性信号
            if ai_response.direction in [AnalysisResult.ERROR, AnalysisResult.NEUTRAL]:
                return self._create_neutral_signal(ai_response.reasoning)

            # 生成开仓信号
            signal = self._create_open_signal(ai_response, symbol, exchange)

            # 验证信号
            if not self._validate_signal(signal):
                return self._create_neutral_signal("开仓信号验证失败")

            logger.info(f"生成开仓信号: {signal.direction.value}, 置信度: {signal.confidence}")
            return signal

        except Exception as e:
            logger.error(f"生成开仓信号失败: {e}")
            return self._create_neutral_signal(f"生成开仓信号异常: {e}")

    def _check_cooldown(self) -> bool:
        """检查开仓冷却时间"""
        try:
            if not self.open_stats['last_signal_time']:
                return True

            cooldown_minutes = self.config.get('cooldown_minutes', 30)
            time_diff = datetime.now() - self.open_stats['last_signal_time']

            return time_diff.total_seconds() >= cooldown_minutes * 60

        except Exception as e:
            logger.error(f"检查冷却时间失败: {e}")
            return False

    def _check_daily_limit(self) -> bool:
        """检查每日交易限制"""
        try:
            # 检查是否需要重置每日计数
            today = datetime.now().date()
            if today != self.open_stats['last_daily_reset']:
                self.open_stats['daily_signals'] = 0
                self.open_stats['last_daily_reset'] = today

            max_daily = self.config.get('max_daily_trades', 10)
            return self.open_stats['daily_signals'] < max_daily

        except Exception as e:
            logger.error(f"检查每日限制失败: {e}")
            return False

    def _update_signal_stats(self, ai_response: AIAnalysisResponse):
        """更新信号统计"""
        try:
            self.open_stats['total_signals'] += 1
            self.open_stats['last_signal_time'] = datetime.now()

            if ai_response.direction != AnalysisResult.ERROR:
                self.open_stats['valid_signals'] += 1

                if ai_response.direction == AnalysisResult.BULLISH:
                    self.open_stats['bullish_signals'] += 1
                elif ai_response.direction == AnalysisResult.BEARISH:
                    self.open_stats['bearish_signals'] += 1

            # 更新每日计数
            today = datetime.now().date()
            if today == self.open_stats['last_daily_reset']:
                self.open_stats['daily_signals'] += 1

        except Exception as e:
            logger.error(f"更新信号统计失败: {e}")

    def _create_open_signal(self, ai_response: AIAnalysisResponse,
                          symbol: str, exchange: str) -> OpenSignal:
        """创建开仓信号"""
        try:
            # 获取当前价格
            current_price = self._get_current_price(symbol, exchange)

            # 计算仓位大小
            position_size = self._calculate_position_size(ai_response.confidence)

            # 获取杠杆
            leverage = self._calculate_leverage(ai_response.confidence)

            # 计算止损止盈
            stop_loss, take_profit = self._calculate_stop_levels(
                current_price, ai_response.direction, ai_response.additional_data
            )

            # 计算信号强度
            signal_strength = self._calculate_signal_strength(ai_response)

            # 评估风险等级
            risk_level = self._assess_risk_level(ai_response, signal_strength)

            return OpenSignal(
                direction=ai_response.direction,
                confidence=ai_response.confidence,
                entry_price=current_price,
                position_size=position_size,
                leverage=leverage,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=ai_response.reasoning,
                risk_level=risk_level,
                signal_strength=signal_strength,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"创建开仓信号失败: {e}")
            raise AIEngineError(f"创建开仓信号失败: {e}")

    def _create_neutral_signal(self, reason: str) -> OpenSignal:
        """创建中性信号"""
        return OpenSignal(
            direction=AnalysisResult.NEUTRAL,
            confidence=0.0,
            entry_price=None,
            position_size=0.0,
            leverage=1,
            stop_loss=None,
            take_profit=None,
            reasoning=reason,
            risk_level="LOW",
            signal_strength=0.0,
            timestamp=datetime.now()
        )

    def _get_current_price(self, symbol: str, exchange: str) -> Optional[float]:
        """获取当前价格"""
        try:
            # 通过技术分析引擎获取最新价格
            analysis = self.technical_engine.analyze_single_timeframe(exchange, symbol, "1m")
            return analysis.get('current_price')

        except Exception as e:
            logger.error(f"获取当前价格失败: {e}")
            return None

    def _calculate_position_size(self, confidence: float) -> float:
        """计算仓位大小"""
        try:
            # 基础仓位比例
            base_ratio = self.config.get('max_position_size_ratio', 0.1)

            # 根据置信度调整仓位大小
            confidence_factor = min(confidence / 100.0, 1.0)
            position_ratio = base_ratio * confidence_factor

            # 确保仓位不超过最大限制
            max_ratio = self.config.get('max_position_size_ratio', 0.1)
            position_ratio = min(position_ratio, max_ratio)

            return round(position_ratio, 4)

        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.01  # 默认最小仓位

    def _calculate_leverage(self, confidence: float) -> int:
        """计算杠杆倍数"""
        try:
            default_leverage = self.config.get('default_leverage', 10)
            max_leverage = self.config.get('max_leverage', 20)

            # 根据置信度调整杠杆
            if confidence >= 90:
                leverage = max_leverage
            elif confidence >= 80:
                leverage = int(default_leverage * 1.5)
            else:
                leverage = default_leverage

            return min(leverage, max_leverage)

        except Exception as e:
            logger.error(f"计算杠杆倍数失败: {e}")
            return self.config.get('default_leverage', 10)

    def _calculate_stop_levels(self, current_price: Optional[float],
                             direction: AnalysisResult,
                             additional_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[float]]:
        """计算止损止盈水平"""
        try:
            if not current_price:
                return None, None

            # 尝试从AI响应中获取关键水平
            key_levels = additional_data.get('key_levels', {})

            if key_levels:
                stop_loss = key_levels.get('stop_loss')
                take_profit = key_levels.get('take_profit')

                if stop_loss and take_profit:
                    return float(stop_loss), float(take_profit)

            # 使用默认比例计算
            stop_loss_ratio = self.config.get('stop_loss_ratio', 0.05)
            take_profit_ratio = self.config.get('take_profit_ratio', 0.15)

            if direction == AnalysisResult.BULLISH:
                stop_loss = current_price * (1 - stop_loss_ratio)
                take_profit = current_price * (1 + take_profit_ratio)
            else:  # BEARISH
                stop_loss = current_price * (1 + stop_loss_ratio)
                take_profit = current_price * (1 - take_profit_ratio)

            return round(stop_loss, 2), round(take_profit, 2)

        except Exception as e:
            logger.error(f"计算止损止盈失败: {e}")
            return None, None

    def _calculate_signal_strength(self, ai_response: AIAnalysisResponse) -> float:
        """计算信号强度"""
        try:
            base_strength = ai_response.confidence

            # 从额外数据中获取信号强度
            additional_data = ai_response.additional_data
            ai_signal_strength = additional_data.get('signal_strength', base_strength)

            # 综合计算信号强度
            signal_strength = (base_strength + ai_signal_strength) / 2

            # 根据市场条件调整
            market_conditions = additional_data.get('market_conditions', {})
            if market_conditions:
                # 趋势强度调整
                trend_strength = market_conditions.get('trend_strength', 'MODERATE')
                if trend_strength == 'STRONG':
                    signal_strength *= 1.1
                elif trend_strength == 'WEAK':
                    signal_strength *= 0.9

                # 成交量确认调整
                volume_confirmation = market_conditions.get('volume_confirmation', False)
                if volume_confirmation:
                    signal_strength *= 1.05

                # 动量一致性调整
                momentum_alignment = market_conditions.get('momentum_alignment', False)
                if momentum_alignment:
                    signal_strength *= 1.05

            return min(signal_strength, 100.0)

        except Exception as e:
            logger.error(f"计算信号强度失败: {e}")
            return ai_response.confidence

    def _assess_risk_level(self, ai_response: AIAnalysisResponse, signal_strength: float) -> str:
        """评估风险等级"""
        try:
            # 从AI响应中获取风险等级
            risk_level = ai_response.additional_data.get('risk_level', 'MEDIUM')

            # 根据置信度和信号强度调整
            if ai_response.confidence >= 85 and signal_strength >= 80:
                return 'LOW'
            elif ai_response.confidence >= 70 and signal_strength >= 60:
                return 'MEDIUM'
            else:
                return 'HIGH'

        except Exception as e:
            logger.error(f"评估风险等级失败: {e}")
            return 'MEDIUM'

    def _validate_signal(self, signal: OpenSignal) -> bool:
        """验证开仓信号"""
        try:
            # 基础有效性检查
            if not signal.is_valid():
                logger.warning("开仓信号基础验证失败")
                return False

            # 置信度检查
            min_confidence = self.config.get('min_confidence_threshold', 70.0)
            if signal.confidence < min_confidence:
                logger.warning(f"信号置信度 {signal.confidence} 低于阈值 {min_confidence}")
                return False

            # 信号强度检查
            min_signal_strength = self.config.get('min_signal_strength', 60.0)
            if signal.signal_strength < min_signal_strength:
                logger.warning(f"信号强度 {signal.signal_strength} 低于阈值 {min_signal_strength}")
                return False

            # 仓位大小检查
            max_position_ratio = self.config.get('max_position_size_ratio', 0.1)
            if signal.position_size > max_position_ratio:
                logger.warning(f"仓位大小 {signal.position_size} 超过最大限制 {max_position_ratio}")
                return False

            # 杠杆检查
            max_leverage = self.config.get('max_leverage', 20)
            if signal.leverage > max_leverage:
                logger.warning(f"杠杆倍数 {signal.leverage} 超过最大限制 {max_leverage}")
                return False

            # 止损止盈检查
            if signal.entry_price and signal.stop_loss and signal.take_profit:
                if signal.direction == AnalysisResult.BULLISH:
                    if signal.stop_loss >= signal.entry_price or signal.take_profit <= signal.entry_price:
                        logger.warning("多头信号的止损止盈设置不合理")
                        return False
                else:  # BEARISH
                    if signal.stop_loss <= signal.entry_price or signal.take_profit >= signal.entry_price:
                        logger.warning("空头信号的止损止盈设置不合理")
                        return False

            return True

        except Exception as e:
            logger.error(f"验证开仓信号失败: {e}")
            return False

    def get_open_engine_stats(self) -> Dict[str, Any]:
        """获取开仓引擎统计"""
        base_stats = self.get_engine_stats()

        # 添加开仓引擎特定统计
        base_stats.update({
            'open_stats': self.open_stats,
            'open_config': self.open_config,
            'signal_distribution': {
                'bullish_ratio': (
                    self.open_stats['bullish_signals'] / max(self.open_stats['valid_signals'], 1) * 100
                ),
                'bearish_ratio': (
                    self.open_stats['bearish_signals'] / max(self.open_stats['valid_signals'], 1) * 100
                ),
                'valid_signal_ratio': (
                    self.open_stats['valid_signals'] / max(self.open_stats['total_signals'], 1) * 100
                )
            },
            'daily_status': {
                'daily_signals': self.open_stats['daily_signals'],
                'daily_limit': self.config.get('max_daily_trades', 10),
                'remaining_signals': max(0, self.config.get('max_daily_trades', 10) - self.open_stats['daily_signals'])
            }
        })

        return base_stats

    def reset_open_stats(self):
        """重置开仓统计"""
        self.open_stats = {
            'total_signals': 0,
            'valid_signals': 0,
            'bullish_signals': 0,
            'bearish_signals': 0,
            'last_signal_time': None,
            'daily_signals': 0,
            'last_daily_reset': datetime.now().date()
        }

        # 同时重置基础统计
        self.reset_stats()

        logger.info("开仓引擎统计已重置")

    def update_open_config(self, new_config: Dict[str, Any]):
        """更新开仓引擎配置"""
        try:
            # 验证开仓引擎特定配置
            valid_open_keys = {
                'min_confidence_threshold', 'max_position_size_ratio', 'default_leverage',
                'max_leverage', 'stop_loss_ratio', 'take_profit_ratio', 'min_signal_strength',
                'cooldown_minutes', 'max_daily_trades'
            }

            for key, value in new_config.items():
                if key in valid_open_keys:
                    # 类型和范围验证
                    if key in ['min_confidence_threshold', 'max_position_size_ratio', 'stop_loss_ratio',
                              'take_profit_ratio', 'min_signal_strength']:
                        if not isinstance(value, (int, float)) or not (0 <= value <= 100):
                            logger.warning(f"开仓配置 {key} 的值无效: {value}")
                            continue

                    if key in ['default_leverage', 'max_leverage', 'cooldown_minutes', 'max_daily_trades']:
                        if not isinstance(value, int) or value <= 0:
                            logger.warning(f"开仓配置 {key} 的值无效: {value}")
                            continue

                    self.open_config[key] = value
                    self.config[key] = value

            # 更新基础配置
            self.update_config(new_config)

            logger.info("开仓引擎配置已更新")

        except Exception as e:
            logger.error(f"更新开仓引擎配置失败: {e}")
            raise AIEngineError(f"更新开仓引擎配置失败: {e}")


# 全局开仓引擎实例
_open_engine_instance = None


def get_ai_open_engine() -> AIOpenEngine:
    """获取AI开仓引擎实例"""
    global _open_engine_instance
    if _open_engine_instance is None:
        _open_engine_instance = AIOpenEngine()
    return _open_engine_instance
