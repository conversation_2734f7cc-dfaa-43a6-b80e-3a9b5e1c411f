# -*- coding: utf-8 -*-
"""
交易执行器
实现开仓、平仓、止盈止损等交易操作，集成交易所服务，实现安全可靠的交易执行

Author: SuperBot Team
Date: 2025-01-04
"""

import time
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from src.services.exchange_service import get_exchange_service
from src.utils.logger import get_logger

logger = get_logger(__name__)


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = "market"  # 市价单
    LIMIT = "limit"    # 限价单
    STOP = "stop"      # 止损单
    TAKE_PROFIT = "take_profit"  # 止盈单


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"      # 待处理
    OPEN = "open"           # 已开启
    FILLED = "filled"       # 已成交
    CANCELED = "canceled"   # 已取消
    REJECTED = "rejected"   # 已拒绝
    EXPIRED = "expired"     # 已过期


class PositionSide(Enum):
    """持仓方向枚举"""
    LONG = "long"   # 多头
    SHORT = "short" # 空头


@dataclass
class TradeOrder:
    """交易订单数据结构"""
    order_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: float
    price: Optional[float]
    order_type: OrderType
    status: OrderStatus
    filled_amount: float
    remaining_amount: float
    average_price: Optional[float]
    fee: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side,
            'amount': self.amount,
            'price': self.price,
            'order_type': self.order_type.value,
            'status': self.status.value,
            'filled_amount': self.filled_amount,
            'remaining_amount': self.remaining_amount,
            'average_price': self.average_price,
            'fee': self.fee,
            'timestamp': self.timestamp.isoformat(),
            'is_filled': self.status == OrderStatus.FILLED
        }


@dataclass
class Position:
    """持仓数据结构"""
    symbol: str
    side: PositionSide
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    leverage: int
    margin: float
    liquidation_price: Optional[float]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'side': self.side.value,
            'size': self.size,
            'entry_price': self.entry_price,
            'mark_price': self.mark_price,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'leverage': self.leverage,
            'margin': self.margin,
            'liquidation_price': self.liquidation_price,
            'timestamp': self.timestamp.isoformat(),
            'is_profitable': self.unrealized_pnl > 0
        }


class TradeExecutor:
    """交易执行器"""
    
    def __init__(self):
        """初始化交易执行器"""
        self.exchange_service = get_exchange_service()
        
        # 交易配置
        self.config = {
            'max_retries': 3,
            'retry_delay': 1.0,
            'order_timeout': 30,
            'slippage_tolerance': 0.001,  # 0.1%滑点容忍度
            'min_order_size': 0.001,
            'max_order_size': 10.0
        }
        
        # 交易统计
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'total_volume': 0.0,
            'total_fees': 0.0,
            'last_trade_time': None
        }
        
        # 活跃订单缓存
        self.active_orders = {}
        
        logger.info("交易执行器初始化完成")
    
    def open_position(self, symbol: str, side: str, size: float, 
                     leverage: int = 1, order_type: OrderType = OrderType.MARKET,
                     price: Optional[float] = None) -> Dict[str, Any]:
        """开仓操作"""
        try:
            logger.info(f"开始开仓: {symbol} {side} {size} 杠杆{leverage}x")
            
            # 参数验证
            if not self._validate_open_params(symbol, side, size, leverage, price):
                raise ValueError("开仓参数验证失败")
            
            # 设置杠杆
            if leverage > 1:
                self._set_leverage(symbol, leverage)
            
            # 创建开仓订单
            order_side = 'buy' if side.lower() == 'long' else 'sell'
            
            order_result = self._execute_order(
                symbol=symbol,
                side=order_side,
                amount=size,
                order_type=order_type,
                price=price
            )
            
            if order_result['success']:
                # 更新统计
                self._update_stats(order_result['order'])
                
                logger.info(f"开仓成功: {order_result['order']['order_id']}")
                return {
                    'success': True,
                    'order_id': order_result['order']['order_id'],
                    'message': '开仓成功',
                    'order': order_result['order']
                }
            else:
                logger.error(f"开仓失败: {order_result['error']}")
                return {
                    'success': False,
                    'error': order_result['error'],
                    'message': '开仓失败'
                }
                
        except Exception as e:
            logger.error(f"开仓异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '开仓异常'
            }
    
    def close_position(self, symbol: str, size: Optional[float] = None,
                      order_type: OrderType = OrderType.MARKET,
                      price: Optional[float] = None) -> Dict[str, Any]:
        """平仓操作"""
        try:
            logger.info(f"开始平仓: {symbol} 数量{size or '全部'}")
            
            # 获取当前持仓
            position = self.get_position(symbol)
            if not position:
                return {
                    'success': False,
                    'error': '未找到持仓',
                    'message': '平仓失败'
                }
            
            # 确定平仓数量
            close_size = size if size is not None else position['size']
            if close_size > position['size']:
                close_size = position['size']
            
            # 确定平仓方向（与持仓方向相反）
            close_side = 'sell' if position['side'] == 'long' else 'buy'
            
            # 执行平仓订单
            order_result = self._execute_order(
                symbol=symbol,
                side=close_side,
                amount=close_size,
                order_type=order_type,
                price=price,
                reduce_only=True
            )
            
            if order_result['success']:
                # 更新统计
                self._update_stats(order_result['order'])
                
                logger.info(f"平仓成功: {order_result['order']['order_id']}")
                return {
                    'success': True,
                    'order_id': order_result['order']['order_id'],
                    'message': '平仓成功',
                    'order': order_result['order']
                }
            else:
                logger.error(f"平仓失败: {order_result['error']}")
                return {
                    'success': False,
                    'error': order_result['error'],
                    'message': '平仓失败'
                }
                
        except Exception as e:
            logger.error(f"平仓异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '平仓异常'
            }
    
    def set_stop_loss(self, symbol: str, stop_price: float,
                     size: Optional[float] = None) -> Dict[str, Any]:
        """设置止损"""
        try:
            logger.info(f"设置止损: {symbol} 价格{stop_price}")
            
            # 获取当前持仓
            position = self.get_position(symbol)
            if not position:
                return {
                    'success': False,
                    'error': '未找到持仓',
                    'message': '设置止损失败'
                }
            
            # 确定止损数量
            stop_size = size if size is not None else position['size']
            
            # 确定止损方向（与持仓方向相反）
            stop_side = 'sell' if position['side'] == 'long' else 'buy'
            
            # 创建止损订单
            order_result = self._execute_order(
                symbol=symbol,
                side=stop_side,
                amount=stop_size,
                order_type=OrderType.STOP,
                price=stop_price,
                reduce_only=True
            )
            
            if order_result['success']:
                logger.info(f"止损设置成功: {order_result['order']['order_id']}")
                return {
                    'success': True,
                    'order_id': order_result['order']['order_id'],
                    'message': '止损设置成功',
                    'order': order_result['order']
                }
            else:
                logger.error(f"止损设置失败: {order_result['error']}")
                return {
                    'success': False,
                    'error': order_result['error'],
                    'message': '止损设置失败'
                }
                
        except Exception as e:
            logger.error(f"设置止损异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '设置止损异常'
            }

    def set_take_profit(self, symbol: str, take_price: float,
                       size: Optional[float] = None) -> Dict[str, Any]:
        """设置止盈"""
        try:
            logger.info(f"设置止盈: {symbol} 价格{take_price}")

            # 获取当前持仓
            position = self.get_position(symbol)
            if not position:
                return {
                    'success': False,
                    'error': '未找到持仓',
                    'message': '设置止盈失败'
                }

            # 确定止盈数量
            take_size = size if size is not None else position['size']

            # 确定止盈方向（与持仓方向相反）
            take_side = 'sell' if position['side'] == 'long' else 'buy'

            # 创建止盈订单
            order_result = self._execute_order(
                symbol=symbol,
                side=take_side,
                amount=take_size,
                order_type=OrderType.TAKE_PROFIT,
                price=take_price,
                reduce_only=True
            )

            if order_result['success']:
                logger.info(f"止盈设置成功: {order_result['order']['order_id']}")
                return {
                    'success': True,
                    'order_id': order_result['order']['order_id'],
                    'message': '止盈设置成功',
                    'order': order_result['order']
                }
            else:
                logger.error(f"止盈设置失败: {order_result['error']}")
                return {
                    'success': False,
                    'error': order_result['error'],
                    'message': '止盈设置失败'
                }

        except Exception as e:
            logger.error(f"设置止盈异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '设置止盈异常'
            }

    def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """取消订单"""
        try:
            logger.info(f"取消订单: {order_id}")

            # 调用交易所服务取消订单
            result = self.exchange_service.cancel_order(order_id, symbol)

            if result.get('success', False):
                # 从活跃订单中移除
                if order_id in self.active_orders:
                    del self.active_orders[order_id]

                logger.info(f"订单取消成功: {order_id}")
                return {
                    'success': True,
                    'message': '订单取消成功'
                }
            else:
                logger.error(f"订单取消失败: {result.get('error', '未知错误')}")
                return {
                    'success': False,
                    'error': result.get('error', '未知错误'),
                    'message': '订单取消失败'
                }

        except Exception as e:
            logger.error(f"取消订单异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取消订单异常'
            }

    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            # 调用交易所服务获取持仓
            positions = self.exchange_service.get_positions()

            if positions.get('success', False):
                for pos in positions.get('data', []):
                    if pos.get('symbol') == symbol and pos.get('size', 0) > 0:
                        return {
                            'symbol': pos.get('symbol'),
                            'side': pos.get('side'),
                            'size': pos.get('size', 0),
                            'entry_price': pos.get('entry_price', 0),
                            'mark_price': pos.get('mark_price', 0),
                            'unrealized_pnl': pos.get('unrealized_pnl', 0),
                            'unrealized_pnl_pct': pos.get('unrealized_pnl_pct', 0),
                            'leverage': pos.get('leverage', 1),
                            'margin': pos.get('margin', 0),
                            'liquidation_price': pos.get('liquidation_price')
                        }

            return None

        except Exception as e:
            logger.error(f"获取持仓信息异常: {e}")
            return None

    def get_all_positions(self) -> List[Dict[str, Any]]:
        """获取所有持仓"""
        try:
            positions = self.exchange_service.get_positions()

            if positions.get('success', False):
                result = []
                for pos in positions.get('data', []):
                    if pos.get('size', 0) > 0:
                        result.append({
                            'symbol': pos.get('symbol'),
                            'side': pos.get('side'),
                            'size': pos.get('size', 0),
                            'entry_price': pos.get('entry_price', 0),
                            'mark_price': pos.get('mark_price', 0),
                            'unrealized_pnl': pos.get('unrealized_pnl', 0),
                            'unrealized_pnl_pct': pos.get('unrealized_pnl_pct', 0),
                            'leverage': pos.get('leverage', 1),
                            'margin': pos.get('margin', 0),
                            'liquidation_price': pos.get('liquidation_price')
                        })
                return result

            return []

        except Exception as e:
            logger.error(f"获取所有持仓异常: {e}")
            return []

    def get_order_status(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        """获取订单状态"""
        try:
            # 调用交易所服务获取订单状态
            result = self.exchange_service.get_order(order_id, symbol)

            if result.get('success', False):
                order_data = result.get('data', {})
                return {
                    'order_id': order_data.get('id'),
                    'symbol': order_data.get('symbol'),
                    'side': order_data.get('side'),
                    'amount': order_data.get('amount', 0),
                    'price': order_data.get('price'),
                    'status': order_data.get('status'),
                    'filled': order_data.get('filled', 0),
                    'remaining': order_data.get('remaining', 0),
                    'average': order_data.get('average'),
                    'fee': order_data.get('fee', 0),
                    'timestamp': order_data.get('timestamp')
                }

            return None

        except Exception as e:
            logger.error(f"获取订单状态异常: {e}")
            return None

    def _execute_order(self, symbol: str, side: str, amount: float,
                      order_type: OrderType = OrderType.MARKET,
                      price: Optional[float] = None,
                      reduce_only: bool = False) -> Dict[str, Any]:
        """执行订单"""
        try:
            # 准备订单参数
            order_params = {
                'symbol': symbol,
                'side': side,
                'amount': amount,
                'type': order_type.value
            }

            if price is not None:
                order_params['price'] = price

            if reduce_only:
                order_params['reduce_only'] = True

            # 重试机制
            max_retries = self.config.get('max_retries', 3)
            retry_delay = self.config.get('retry_delay', 1.0)

            for attempt in range(max_retries):
                try:
                    # 调用交易所服务创建订单
                    result = self.exchange_service.create_order(**order_params)

                    if result.get('success', False):
                        order_data = result.get('data', {})

                        # 创建订单对象
                        order = TradeOrder(
                            order_id=order_data.get('id', ''),
                            symbol=symbol,
                            side=side,
                            amount=amount,
                            price=price,
                            order_type=order_type,
                            status=OrderStatus.OPEN,
                            filled_amount=order_data.get('filled', 0),
                            remaining_amount=order_data.get('remaining', amount),
                            average_price=order_data.get('average'),
                            fee=order_data.get('fee', 0),
                            timestamp=datetime.now()
                        )

                        # 添加到活跃订单
                        self.active_orders[order.order_id] = order

                        # 如果是市价单，等待成交
                        if order_type == OrderType.MARKET:
                            self._wait_for_fill(order.order_id, symbol)

                        return {
                            'success': True,
                            'order': order.to_dict()
                        }
                    else:
                        error_msg = result.get('error', '未知错误')
                        if attempt < max_retries - 1:
                            logger.warning(f"订单执行失败，重试 {attempt + 1}/{max_retries}: {error_msg}")
                            time.sleep(retry_delay)
                            continue
                        else:
                            return {
                                'success': False,
                                'error': error_msg
                            }

                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"订单执行异常，重试 {attempt + 1}/{max_retries}: {e}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        return {
                            'success': False,
                            'error': str(e)
                        }

            return {
                'success': False,
                'error': '达到最大重试次数'
            }

        except Exception as e:
            logger.error(f"执行订单异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _wait_for_fill(self, order_id: str, symbol: str, timeout: int = 30):
        """等待订单成交"""
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                order_status = self.get_order_status(order_id, symbol)

                if order_status:
                    status = order_status.get('status', '').lower()

                    if status in ['filled', 'closed']:
                        # 更新活跃订单状态
                        if order_id in self.active_orders:
                            self.active_orders[order_id].status = OrderStatus.FILLED
                            self.active_orders[order_id].filled_amount = order_status.get('filled', 0)
                            self.active_orders[order_id].average_price = order_status.get('average')

                        logger.info(f"订单已成交: {order_id}")
                        return True

                    elif status in ['canceled', 'rejected', 'expired']:
                        # 更新活跃订单状态
                        if order_id in self.active_orders:
                            if status == 'canceled':
                                self.active_orders[order_id].status = OrderStatus.CANCELED
                            elif status == 'rejected':
                                self.active_orders[order_id].status = OrderStatus.REJECTED
                            else:
                                self.active_orders[order_id].status = OrderStatus.EXPIRED

                        logger.warning(f"订单未成交: {order_id}, 状态: {status}")
                        return False

                time.sleep(1)  # 等待1秒后重新检查

            logger.warning(f"订单等待超时: {order_id}")
            return False

        except Exception as e:
            logger.error(f"等待订单成交异常: {e}")
            return False

    def _validate_open_params(self, symbol: str, side: str, size: float,
                             leverage: int, price: Optional[float]) -> bool:
        """验证开仓参数"""
        try:
            # 检查交易对
            if not symbol or not isinstance(symbol, str):
                logger.error("无效的交易对")
                return False

            # 检查方向
            if side.lower() not in ['long', 'short']:
                logger.error("无效的持仓方向")
                return False

            # 检查数量
            min_size = self.config.get('min_order_size', 0.001)
            max_size = self.config.get('max_order_size', 10.0)

            if not (min_size <= size <= max_size):
                logger.error(f"订单数量超出范围: {size}, 范围: [{min_size}, {max_size}]")
                return False

            # 检查杠杆
            if not (1 <= leverage <= 100):
                logger.error(f"杠杆倍数超出范围: {leverage}")
                return False

            # 检查价格（如果提供）
            if price is not None and price <= 0:
                logger.error(f"无效的价格: {price}")
                return False

            return True

        except Exception as e:
            logger.error(f"参数验证异常: {e}")
            return False

    def _set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆"""
        try:
            result = self.exchange_service.set_leverage(symbol, leverage)

            if result.get('success', False):
                logger.info(f"杠杆设置成功: {symbol} {leverage}x")
                return True
            else:
                logger.error(f"杠杆设置失败: {result.get('error', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"设置杠杆异常: {e}")
            return False

    def _update_stats(self, order: Dict[str, Any]):
        """更新交易统计"""
        try:
            self.stats['total_orders'] += 1
            self.stats['last_trade_time'] = datetime.now()

            if order.get('status') == 'filled':
                self.stats['successful_orders'] += 1
                self.stats['total_volume'] += order.get('filled_amount', 0)
                self.stats['total_fees'] += order.get('fee', 0)
            else:
                self.stats['failed_orders'] += 1

        except Exception as e:
            logger.error(f"更新统计异常: {e}")

    def get_executor_stats(self) -> Dict[str, Any]:
        """获取交易执行器统计"""
        try:
            success_rate = 0
            if self.stats['total_orders'] > 0:
                success_rate = self.stats['successful_orders'] / self.stats['total_orders'] * 100

            return {
                'total_orders': self.stats['total_orders'],
                'successful_orders': self.stats['successful_orders'],
                'failed_orders': self.stats['failed_orders'],
                'success_rate': round(success_rate, 2),
                'total_volume': self.stats['total_volume'],
                'total_fees': self.stats['total_fees'],
                'last_trade_time': self.stats['last_trade_time'].isoformat() if self.stats['last_trade_time'] else None,
                'active_orders_count': len(self.active_orders),
                'config': self.config.copy()
            }

        except Exception as e:
            logger.error(f"获取统计异常: {e}")
            return {}

    def get_active_orders(self) -> List[Dict[str, Any]]:
        """获取活跃订单"""
        try:
            return [order.to_dict() for order in self.active_orders.values()]

        except Exception as e:
            logger.error(f"获取活跃订单异常: {e}")
            return []

    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        try:
            valid_keys = {
                'max_retries', 'retry_delay', 'order_timeout',
                'slippage_tolerance', 'min_order_size', 'max_order_size'
            }

            for key, value in new_config.items():
                if key in valid_keys:
                    # 类型和范围验证
                    if key in ['max_retries', 'order_timeout'] and isinstance(value, int) and value > 0:
                        self.config[key] = value
                    elif key in ['retry_delay', 'slippage_tolerance', 'min_order_size', 'max_order_size'] and isinstance(value, (int, float)) and value > 0:
                        self.config[key] = value
                    else:
                        logger.warning(f"配置项 {key} 的值无效: {value}")

            logger.info("交易执行器配置已更新")

        except Exception as e:
            logger.error(f"更新配置异常: {e}")

    def reset_stats(self):
        """重置统计"""
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'total_volume': 0.0,
            'total_fees': 0.0,
            'last_trade_time': None
        }

        logger.info("交易执行器统计已重置")

    def cleanup_orders(self):
        """清理已完成的订单"""
        try:
            completed_orders = []

            for order_id, order in self.active_orders.items():
                if order.status in [OrderStatus.FILLED, OrderStatus.CANCELED,
                                  OrderStatus.REJECTED, OrderStatus.EXPIRED]:
                    completed_orders.append(order_id)

            for order_id in completed_orders:
                del self.active_orders[order_id]

            logger.info(f"清理了 {len(completed_orders)} 个已完成订单")

        except Exception as e:
            logger.error(f"清理订单异常: {e}")


# 全局交易执行器实例
_trade_executor_instance = None


def get_trade_executor() -> TradeExecutor:
    """获取交易执行器实例"""
    global _trade_executor_instance
    if _trade_executor_instance is None:
        _trade_executor_instance = TradeExecutor()
    return _trade_executor_instance
