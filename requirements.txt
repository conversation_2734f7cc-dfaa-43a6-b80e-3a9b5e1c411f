# DeepSeek加密货币永续合约全自动量化交易系统
# Python依赖包列表

# =============================================================================
# 核心依赖
# =============================================================================
# 交易所API接口
ccxt>=4.2.0

# 技术分析库
TA-Lib>=0.4.28

# 桌面应用框架
pywebview>=4.4.0

# 任务调度
APScheduler>=3.10.4

# HTTP请求库
requests>=2.31.0

# 异步HTTP客户端
aiohttp>=3.9.0

# JSON处理
orjson>=3.9.0

# =============================================================================
# 数据处理
# =============================================================================
# 数值计算
numpy>=1.24.0

# 数据分析
pandas>=2.0.0

# 科学计算
scipy>=1.11.0

# =============================================================================
# 数据库
# =============================================================================
# SQLite数据库（Python内置，但列出以明确依赖）
# sqlite3 - 内置模块

# 数据库ORM（可选）
sqlalchemy>=2.0.0

# =============================================================================
# 加密和安全
# =============================================================================
# 加密库
cryptography>=41.0.0

# 密码哈希
bcrypt>=4.0.0

# =============================================================================
# 配置和环境
# =============================================================================
# 环境变量管理
python-dotenv>=1.0.0

# 配置文件解析
pyyaml>=6.0

# JSON配置
jsonschema>=4.19.0

# =============================================================================
# 日志和监控
# =============================================================================
# 结构化日志
structlog>=23.1.0

# 日志轮转
loguru>=0.7.0

# =============================================================================
# 网络和API
# =============================================================================
# WebSocket客户端（备用）
websockets>=11.0.0

# URL解析
urllib3>=2.0.0

# =============================================================================
# 开发和测试
# =============================================================================
# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 代码格式化
black>=23.7.0

# 代码检查
flake8>=6.0.0

# 类型检查
mypy>=1.5.0

# =============================================================================
# 实用工具
# =============================================================================
# 时间处理
python-dateutil>=2.8.0

# 重试机制
tenacity>=8.2.0

# 进度条
tqdm>=4.66.0

# 颜色输出
colorama>=0.4.6

# 系统信息
psutil>=5.9.0
