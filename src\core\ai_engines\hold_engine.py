# -*- coding: utf-8 -*-
"""
AI持仓引擎
实现持仓管理和平仓决策功能，监控现有持仓，结合市场数据和盈亏情况做出智能平仓决策

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.core.ai_engines.base_engine import (
    BaseAIEngine, EngineType, AnalysisResult, 
    AIAnalysisRequest, AIAnalysisResponse, AIEngineError
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PositionInfo:
    """持仓信息数据结构"""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    leverage: int
    margin: float
    liquidation_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    open_time: datetime
    hold_duration: timed<PERSON><PERSON>
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'side': self.side,
            'size': self.size,
            'entry_price': self.entry_price,
            'current_price': self.current_price,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'leverage': self.leverage,
            'margin': self.margin,
            'liquidation_price': self.liquidation_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'open_time': self.open_time.isoformat(),
            'hold_duration_hours': self.hold_duration.total_seconds() / 3600,
            'is_profitable': self.unrealized_pnl > 0
        }


@dataclass
class HoldSignal:
    """持仓信号数据结构"""
    action: str  # 'hold', 'close', 'adjust_stop', 'adjust_target'
    confidence: float
    reasoning: str
    new_stop_loss: Optional[float]
    new_take_profit: Optional[float]
    close_ratio: float  # 平仓比例 0.0-1.0
    urgency: str  # 'low', 'medium', 'high', 'emergency'
    risk_level: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'action': self.action,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'new_stop_loss': self.new_stop_loss,
            'new_take_profit': self.new_take_profit,
            'close_ratio': self.close_ratio,
            'urgency': self.urgency,
            'risk_level': self.risk_level,
            'timestamp': self.timestamp.isoformat(),
            'is_valid': self.is_valid()
        }
    
    def is_valid(self) -> bool:
        """检查持仓信号是否有效"""
        return (
            self.action in ['hold', 'close', 'adjust_stop', 'adjust_target'] and
            0 <= self.confidence <= 100 and
            0 <= self.close_ratio <= 1.0 and
            len(self.reasoning) > 0
        )


class AIHoldEngine(BaseAIEngine):
    """AI持仓引擎"""
    
    def __init__(self):
        """初始化AI持仓引擎"""
        super().__init__(EngineType.HOLD)
        
        # 持仓引擎特定配置
        self.hold_config = {
            'min_confidence_threshold': 70.0,
            'profit_target_ratio': 0.15,  # 目标盈利比例
            'stop_loss_ratio': 0.05,  # 止损比例
            'trailing_stop_ratio': 0.03,  # 追踪止损比例
            'max_hold_hours': 24,  # 最大持仓时间（小时）
            'min_hold_minutes': 30,  # 最小持仓时间（分钟）
            'emergency_loss_ratio': 0.10,  # 紧急止损比例
            'partial_close_threshold': 0.08,  # 部分平仓阈值
            'rebalance_interval_hours': 4  # 重新平衡间隔
        }
        
        # 更新基础配置
        self.config.update(self.hold_config)
        
        # 持仓统计
        self.hold_stats = {
            'total_analyses': 0,
            'hold_decisions': 0,
            'close_decisions': 0,
            'adjust_decisions': 0,
            'emergency_closes': 0,
            'profitable_closes': 0,
            'loss_closes': 0,
            'last_analysis_time': None,
            'average_hold_duration': 0.0
        }
        
        logger.info("AI持仓引擎初始化完成")
    
    def _generate_prompt(self, request: AIAnalysisRequest) -> str:
        """生成持仓管理提示词"""
        try:
            # 获取持仓信息和技术数据
            position_info = request.market_context.get('position_info', {})
            technical_data = request.technical_data
            symbol = request.symbol
            
            # 构建持仓信息描述
            position_desc = self._format_position_info(position_info)
            
            # 构建技术指标描述
            technical_desc = self._format_technical_data_for_hold(technical_data.get('timeframes', {}))
            
            # 获取市场摘要
            market_summary = technical_data.get('summary', {})
            
            # 构建专业化的持仓管理提示词
            prompt = f"""
你是一位专业的加密货币量化交易持仓管理专家，专门负责持仓决策和风险控制。请基于当前持仓信息和市场技术指标，给出专业的持仓管理建议。

=== 当前持仓信息 ===
交易对: {symbol}
分析时间: {request.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
{position_desc}

=== 市场技术状况 ===
当前市场趋势: {market_summary.get('trend_consensus', '未知')}
动量状态: {market_summary.get('momentum_consensus', '未知')}
整体信号: {market_summary.get('overall_signal', '未知')}

=== 多时间周期技术指标 ===
{technical_desc}

=== 持仓管理分析要求 ===
请从以下专业角度进行深入分析：

1. **盈亏状况评估**：
   - 分析当前盈亏比例和绝对金额
   - 评估盈亏相对于预期目标的完成度
   - 判断是否达到止盈或需要止损的条件

2. **趋势延续性分析**：
   - 评估当前趋势是否与持仓方向一致
   - 分析趋势强度和可持续性
   - 判断是否存在趋势反转的信号

3. **技术指标确认**：
   - 分析RSI、MACD等动量指标的状态
   - 评估是否出现背离或确认信号
   - 判断技术指标对持仓的支撑或压力

4. **风险控制评估**：
   - 评估当前持仓的风险水平
   - 分析市场波动对持仓的影响
   - 判断是否需要调整止损止盈位置

5. **时间因素考量**：
   - 分析持仓时间对决策的影响
   - 评估是否超过合理持仓周期
   - 考虑时间成本和机会成本

6. **市场环境分析**：
   - 评估当前市场环境的变化
   - 分析外部因素对持仓的影响
   - 判断是否需要调整持仓策略

=== 输出要求 ===
请严格按照以下JSON格式返回分析结果：

{{
    "action": "hold/close/adjust_stop/adjust_target",
    "confidence": 85,
    "reasoning": "详细的持仓管理分析，包括盈亏评估、趋势分析、风险控制等",
    "close_ratio": 0.0,
    "urgency": "low/medium/high/emergency",
    "risk_level": "LOW/MEDIUM/HIGH",
    "position_assessment": {{
        "profit_potential": "HIGH/MEDIUM/LOW",
        "risk_level": "LOW/MEDIUM/HIGH",
        "trend_alignment": true,
        "time_factor": "FAVORABLE/NEUTRAL/UNFAVORABLE"
    }},
    "adjustments": {{
        "new_stop_loss": 45000,
        "new_take_profit": 55000,
        "trailing_stop": true,
        "partial_close_suggested": false
    }},
    "market_conditions": {{
        "trend_strength": "STRONG/MODERATE/WEAK",
        "volatility_level": "HIGH/MEDIUM/LOW",
        "momentum_direction": "WITH_POSITION/AGAINST_POSITION/NEUTRAL",
        "support_resistance": "STRONG/WEAK"
    }}
}}

注意事项：
- confidence 范围：0-100，表示决策的置信度
- close_ratio 范围：0.0-1.0，0表示不平仓，1表示全部平仓
- reasoning 必须详细说明分析逻辑和决策依据
- 必须考虑持仓时间、盈亏状况和市场变化
- 风险控制是首要考虑因素
- 所有价格建议必须基于技术分析给出
"""
            
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成持仓提示词失败: {e}")
            raise AIEngineError(f"生成持仓提示词失败: {e}")
    
    def _format_position_info(self, position_info: Dict[str, Any]) -> str:
        """格式化持仓信息"""
        try:
            if not position_info:
                return "无持仓信息"
            
            lines = []
            lines.append(f"持仓方向: {position_info.get('side', '未知').upper()}")
            lines.append(f"持仓数量: {position_info.get('size', 0):.4f}")
            lines.append(f"开仓价格: {position_info.get('entry_price', 0):.2f}")
            lines.append(f"当前价格: {position_info.get('current_price', 0):.2f}")
            lines.append(f"未实现盈亏: {position_info.get('unrealized_pnl', 0):.2f} ({position_info.get('unrealized_pnl_pct', 0):+.2f}%)")
            lines.append(f"杠杆倍数: {position_info.get('leverage', 1)}x")
            lines.append(f"保证金: {position_info.get('margin', 0):.2f}")
            
            if position_info.get('liquidation_price'):
                lines.append(f"强平价格: {position_info['liquidation_price']:.2f}")
            
            if position_info.get('stop_loss'):
                lines.append(f"当前止损: {position_info['stop_loss']:.2f}")
            
            if position_info.get('take_profit'):
                lines.append(f"当前止盈: {position_info['take_profit']:.2f}")
            
            # 持仓时间
            if 'hold_duration_hours' in position_info:
                hours = position_info['hold_duration_hours']
                if hours < 1:
                    lines.append(f"持仓时间: {hours * 60:.0f} 分钟")
                else:
                    lines.append(f"持仓时间: {hours:.1f} 小时")
            
            return '\n'.join(lines)
            
        except Exception as e:
            logger.error(f"格式化持仓信息失败: {e}")
            return "持仓信息格式化失败"

    def _format_technical_data_for_hold(self, timeframes_data: Dict[str, Any]) -> str:
        """为持仓管理格式化技术数据"""
        try:
            formatted_lines = []

            for timeframe, tf_data in timeframes_data.items():
                if not tf_data or 'indicators' not in tf_data:
                    continue

                indicators = tf_data['indicators']
                current_price = tf_data.get('current_price', 0)
                price_change = tf_data.get('price_change', 0)
                price_change_pct = tf_data.get('price_change_pct', 0)

                formatted_lines.append(f"\n【{timeframe.upper()} 周期持仓分析】")
                formatted_lines.append(f"价格变化: {price_change:+.2f} ({price_change_pct:+.2f}%)")

                # 重点关注趋势延续性
                if 'trend' in indicators:
                    trend = indicators['trend']
                    formatted_lines.append("趋势指标:")
                    if trend.get('sma_20') and current_price:
                        sma_relation = "上方" if current_price > trend['sma_20'] else "下方"
                        formatted_lines.append(f"  价格位于SMA-20 {sma_relation}: {trend['sma_20']:.2f}")

                    if trend.get('macd_line') and trend.get('macd_signal'):
                        macd_trend = "多头" if trend['macd_line'] > trend['macd_signal'] else "空头"
                        formatted_lines.append(f"  MACD趋势: {macd_trend} (MACD: {trend['macd_line']:.4f})")

                # 重点关注动量背离
                if 'momentum' in indicators:
                    momentum = indicators['momentum']
                    formatted_lines.append("动量指标:")
                    if momentum.get('rsi'):
                        rsi_level = "超买" if momentum['rsi'] > 70 else "超卖" if momentum['rsi'] < 30 else "正常"
                        formatted_lines.append(f"  RSI: {momentum['rsi']:.2f} ({rsi_level})")

                    if momentum.get('stoch_k'):
                        stoch_level = "超买" if momentum['stoch_k'] > 80 else "超卖" if momentum['stoch_k'] < 20 else "正常"
                        formatted_lines.append(f"  STOCH: {momentum['stoch_k']:.2f} ({stoch_level})")

                # 重点关注支撑阻力
                if 'volatility' in indicators:
                    volatility = indicators['volatility']
                    if all(volatility.get(k) for k in ['bb_upper', 'bb_middle', 'bb_lower']):
                        bb_position = "上轨附近" if current_price > volatility['bb_upper'] * 0.98 else \
                                    "下轨附近" if current_price < volatility['bb_lower'] * 1.02 else "中轨附近"
                        formatted_lines.append(f"布林带位置: {bb_position}")

            return '\n'.join(formatted_lines)

        except Exception as e:
            logger.error(f"格式化持仓技术数据失败: {e}")
            return "技术数据格式化失败"

    def _post_process_response(self, response: AIAnalysisResponse,
                             request: AIAnalysisRequest) -> AIAnalysisResponse:
        """后处理AI响应"""
        try:
            # 解析额外数据
            additional_data = response.additional_data

            # 尝试从reasoning中提取JSON数据
            if isinstance(response.reasoning, str):
                try:
                    json_start = response.reasoning.find('{')
                    json_end = response.reasoning.rfind('}') + 1

                    if json_start >= 0 and json_end > json_start:
                        json_str = response.reasoning[json_start:json_end]
                        parsed_data = json.loads(json_str)
                        additional_data.update(parsed_data)
                except json.JSONDecodeError:
                    pass

            # 更新额外数据
            response.additional_data = additional_data

            # 验证置信度阈值
            min_confidence = self.config.get('min_confidence_threshold', 70.0)
            if response.confidence < min_confidence:
                logger.info(f"AI持仓分析置信度 {response.confidence} 低于阈值 {min_confidence}")
                # 对于持仓引擎，低置信度时保持持仓
                if response.direction != AnalysisResult.NEUTRAL:
                    response.reasoning += f"\n注意：置信度 {response.confidence} 低于阈值 {min_confidence}，建议保持当前持仓"

            return response

        except Exception as e:
            logger.error(f"后处理AI响应失败: {e}")
            return response

    def generate_hold_signal(self, symbol: str, position_info: Dict[str, Any],
                           exchange: str = "okx",
                           market_context: Optional[Dict[str, Any]] = None) -> HoldSignal:
        """生成持仓信号"""
        try:
            # 验证持仓信息
            if not position_info:
                return self._create_hold_signal("无持仓信息")

            # 检查最小持仓时间
            if not self._check_min_hold_time(position_info):
                return self._create_hold_signal("未达到最小持仓时间")

            # 检查紧急止损条件
            emergency_signal = self._check_emergency_conditions(position_info)
            if emergency_signal:
                return emergency_signal

            # 准备市场上下文
            if not market_context:
                market_context = {}
            market_context['position_info'] = position_info

            # 执行AI分析
            ai_response = self.analyze(symbol, exchange, market_context)

            # 更新统计
            self._update_hold_stats(ai_response, position_info)

            # 如果AI分析失败，保持持仓
            if ai_response.direction == AnalysisResult.ERROR:
                return self._create_hold_signal(ai_response.reasoning)

            # 生成持仓信号
            signal = self._create_hold_signal_from_ai(ai_response, position_info, symbol)

            # 验证信号
            if not self._validate_hold_signal(signal, position_info):
                return self._create_hold_signal("持仓信号验证失败")

            logger.info(f"生成持仓信号: {signal.action}, 置信度: {signal.confidence}")
            return signal

        except Exception as e:
            logger.error(f"生成持仓信号失败: {e}")
            return self._create_hold_signal(f"生成持仓信号异常: {e}")

    def _check_min_hold_time(self, position_info: Dict[str, Any]) -> bool:
        """检查最小持仓时间"""
        try:
            min_hold_minutes = self.config.get('min_hold_minutes', 30)
            hold_duration_hours = position_info.get('hold_duration_hours', 0)

            return hold_duration_hours * 60 >= min_hold_minutes

        except Exception as e:
            logger.error(f"检查最小持仓时间失败: {e}")
            return True  # 默认允许

    def _check_emergency_conditions(self, position_info: Dict[str, Any]) -> Optional[HoldSignal]:
        """检查紧急平仓条件"""
        try:
            emergency_loss_ratio = self.config.get('emergency_loss_ratio', 0.10)
            max_hold_hours = self.config.get('max_hold_hours', 24)

            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0)
            hold_duration_hours = position_info.get('hold_duration_hours', 0)

            # 检查紧急止损
            if unrealized_pnl_pct <= -emergency_loss_ratio * 100:
                return HoldSignal(
                    action='close',
                    confidence=95.0,
                    reasoning=f"触发紧急止损：亏损 {unrealized_pnl_pct:.2f}% 超过阈值 {emergency_loss_ratio*100:.1f}%",
                    new_stop_loss=None,
                    new_take_profit=None,
                    close_ratio=1.0,
                    urgency='emergency',
                    risk_level='HIGH',
                    timestamp=datetime.now()
                )

            # 检查最大持仓时间
            if hold_duration_hours >= max_hold_hours:
                return HoldSignal(
                    action='close',
                    confidence=85.0,
                    reasoning=f"超过最大持仓时间：已持仓 {hold_duration_hours:.1f} 小时，超过限制 {max_hold_hours} 小时",
                    new_stop_loss=None,
                    new_take_profit=None,
                    close_ratio=1.0,
                    urgency='high',
                    risk_level='MEDIUM',
                    timestamp=datetime.now()
                )

            return None

        except Exception as e:
            logger.error(f"检查紧急条件失败: {e}")
            return None

    def _create_hold_signal(self, reason: str) -> HoldSignal:
        """创建保持持仓信号"""
        return HoldSignal(
            action='hold',
            confidence=50.0,
            reasoning=reason,
            new_stop_loss=None,
            new_take_profit=None,
            close_ratio=0.0,
            urgency='low',
            risk_level='MEDIUM',
            timestamp=datetime.now()
        )

    def _create_hold_signal_from_ai(self, ai_response: AIAnalysisResponse,
                                  position_info: Dict[str, Any], symbol: str) -> HoldSignal:
        """从AI响应创建持仓信号"""
        try:
            additional_data = ai_response.additional_data

            # 解析AI建议的行动
            action = additional_data.get('action', 'hold')
            close_ratio = additional_data.get('close_ratio', 0.0)
            urgency = additional_data.get('urgency', 'medium')
            risk_level = additional_data.get('risk_level', 'MEDIUM')

            # 解析调整建议
            adjustments = additional_data.get('adjustments', {})
            new_stop_loss = adjustments.get('new_stop_loss')
            new_take_profit = adjustments.get('new_take_profit')

            # 如果AI没有明确建议，根据置信度和方向判断
            if action == 'hold' and ai_response.direction != AnalysisResult.NEUTRAL:
                if ai_response.direction == AnalysisResult.BEARISH and ai_response.confidence > 80:
                    # 高置信度看跌，建议平仓
                    action = 'close'
                    close_ratio = min(ai_response.confidence / 100.0, 1.0)
                elif ai_response.confidence > 85:
                    # 高置信度，可能需要调整止盈止损
                    action = 'adjust_target'

            # 计算新的止损止盈位置
            if not new_stop_loss or not new_take_profit:
                new_stop_loss, new_take_profit = self._calculate_new_stop_levels(
                    position_info, ai_response, additional_data
                )

            # 评估紧急程度
            urgency = self._assess_urgency(position_info, ai_response, additional_data)

            return HoldSignal(
                action=action,
                confidence=ai_response.confidence,
                reasoning=ai_response.reasoning,
                new_stop_loss=new_stop_loss,
                new_take_profit=new_take_profit,
                close_ratio=close_ratio,
                urgency=urgency,
                risk_level=risk_level,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"从AI响应创建持仓信号失败: {e}")
            return self._create_hold_signal(f"创建信号失败: {e}")

    def _calculate_new_stop_levels(self, position_info: Dict[str, Any],
                                 ai_response: AIAnalysisResponse,
                                 additional_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[float]]:
        """计算新的止损止盈水平"""
        try:
            current_price = position_info.get('current_price', 0)
            entry_price = position_info.get('entry_price', 0)
            side = position_info.get('side', 'long')
            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0)

            if not current_price or not entry_price:
                return None, None

            # 尝试从AI响应中获取建议价位
            adjustments = additional_data.get('adjustments', {})
            if adjustments.get('new_stop_loss') and adjustments.get('new_take_profit'):
                return float(adjustments['new_stop_loss']), float(adjustments['new_take_profit'])

            # 使用动态止损止盈策略
            stop_loss_ratio = self.config.get('stop_loss_ratio', 0.05)
            profit_target_ratio = self.config.get('profit_target_ratio', 0.15)
            trailing_stop_ratio = self.config.get('trailing_stop_ratio', 0.03)

            # 如果已经盈利，使用追踪止损
            if unrealized_pnl_pct > 5:  # 盈利超过5%
                if side == 'long':
                    new_stop_loss = current_price * (1 - trailing_stop_ratio)
                    new_take_profit = current_price * (1 + profit_target_ratio * 0.5)  # 降低目标
                else:  # short
                    new_stop_loss = current_price * (1 + trailing_stop_ratio)
                    new_take_profit = current_price * (1 - profit_target_ratio * 0.5)
            else:
                # 使用固定止损止盈
                if side == 'long':
                    new_stop_loss = entry_price * (1 - stop_loss_ratio)
                    new_take_profit = entry_price * (1 + profit_target_ratio)
                else:  # short
                    new_stop_loss = entry_price * (1 + stop_loss_ratio)
                    new_take_profit = entry_price * (1 - profit_target_ratio)

            return round(new_stop_loss, 2), round(new_take_profit, 2)

        except Exception as e:
            logger.error(f"计算新止损止盈失败: {e}")
            return None, None

    def _assess_urgency(self, position_info: Dict[str, Any],
                       ai_response: AIAnalysisResponse,
                       additional_data: Dict[str, Any]) -> str:
        """评估操作紧急程度"""
        try:
            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0)
            hold_duration_hours = position_info.get('hold_duration_hours', 0)

            # 从AI响应中获取紧急程度
            ai_urgency = additional_data.get('urgency', 'medium')

            # 基于盈亏状况调整
            if unrealized_pnl_pct <= -8:  # 亏损超过8%
                return 'high'
            elif unrealized_pnl_pct <= -5:  # 亏损超过5%
                return 'medium'
            elif hold_duration_hours >= 20:  # 持仓超过20小时
                return 'medium'
            elif ai_response.confidence >= 90:  # AI高置信度
                return 'high' if ai_response.direction == AnalysisResult.BEARISH else 'medium'

            return ai_urgency

        except Exception as e:
            logger.error(f"评估紧急程度失败: {e}")
            return 'medium'

    def _validate_hold_signal(self, signal: HoldSignal, position_info: Dict[str, Any]) -> bool:
        """验证持仓信号"""
        try:
            # 基础有效性检查
            if not signal.is_valid():
                logger.warning("持仓信号基础验证失败")
                return False

            # 置信度检查
            min_confidence = self.config.get('min_confidence_threshold', 70.0)
            if signal.action == 'close' and signal.confidence < min_confidence:
                logger.warning(f"平仓信号置信度 {signal.confidence} 低于阈值 {min_confidence}")
                return False

            # 平仓比例检查
            if signal.action == 'close' and not (0 < signal.close_ratio <= 1.0):
                logger.warning(f"平仓比例 {signal.close_ratio} 无效")
                return False

            # 止损止盈合理性检查
            current_price = position_info.get('current_price', 0)
            side = position_info.get('side', 'long')

            if signal.new_stop_loss and signal.new_take_profit and current_price:
                if side == 'long':
                    if signal.new_stop_loss >= current_price or signal.new_take_profit <= current_price:
                        logger.warning("多头持仓的止损止盈设置不合理")
                        return False
                else:  # short
                    if signal.new_stop_loss <= current_price or signal.new_take_profit >= current_price:
                        logger.warning("空头持仓的止损止盈设置不合理")
                        return False

            return True

        except Exception as e:
            logger.error(f"验证持仓信号失败: {e}")
            return False

    def _update_hold_stats(self, ai_response: AIAnalysisResponse, position_info: Dict[str, Any]):
        """更新持仓统计"""
        try:
            self.hold_stats['total_analyses'] += 1
            self.hold_stats['last_analysis_time'] = datetime.now()

            # 根据AI响应更新统计
            action = ai_response.additional_data.get('action', 'hold')

            if action == 'hold':
                self.hold_stats['hold_decisions'] += 1
            elif action == 'close':
                self.hold_stats['close_decisions'] += 1

                # 统计盈亏情况
                unrealized_pnl = position_info.get('unrealized_pnl', 0)
                if unrealized_pnl > 0:
                    self.hold_stats['profitable_closes'] += 1
                else:
                    self.hold_stats['loss_closes'] += 1
            elif action in ['adjust_stop', 'adjust_target']:
                self.hold_stats['adjust_decisions'] += 1

            # 更新平均持仓时间
            hold_duration = position_info.get('hold_duration_hours', 0)
            if hold_duration > 0:
                total_closes = self.hold_stats['close_decisions']
                if total_closes > 0:
                    current_avg = self.hold_stats['average_hold_duration']
                    self.hold_stats['average_hold_duration'] = (
                        (current_avg * (total_closes - 1) + hold_duration) / total_closes
                    )

        except Exception as e:
            logger.error(f"更新持仓统计失败: {e}")

    def get_hold_engine_stats(self) -> Dict[str, Any]:
        """获取持仓引擎统计"""
        base_stats = self.get_engine_stats()

        # 添加持仓引擎特定统计
        base_stats.update({
            'hold_stats': self.hold_stats,
            'hold_config': self.hold_config,
            'decision_distribution': {
                'hold_ratio': (
                    self.hold_stats['hold_decisions'] / max(self.hold_stats['total_analyses'], 1) * 100
                ),
                'close_ratio': (
                    self.hold_stats['close_decisions'] / max(self.hold_stats['total_analyses'], 1) * 100
                ),
                'adjust_ratio': (
                    self.hold_stats['adjust_decisions'] / max(self.hold_stats['total_analyses'], 1) * 100
                )
            },
            'performance_metrics': {
                'profitable_close_ratio': (
                    self.hold_stats['profitable_closes'] / max(self.hold_stats['close_decisions'], 1) * 100
                ),
                'average_hold_duration_hours': self.hold_stats['average_hold_duration'],
                'emergency_close_ratio': (
                    self.hold_stats['emergency_closes'] / max(self.hold_stats['close_decisions'], 1) * 100
                )
            }
        })

        return base_stats

    def reset_hold_stats(self):
        """重置持仓统计"""
        self.hold_stats = {
            'total_analyses': 0,
            'hold_decisions': 0,
            'close_decisions': 0,
            'adjust_decisions': 0,
            'emergency_closes': 0,
            'profitable_closes': 0,
            'loss_closes': 0,
            'last_analysis_time': None,
            'average_hold_duration': 0.0
        }

        # 同时重置基础统计
        self.reset_stats()

        logger.info("持仓引擎统计已重置")

    def update_hold_config(self, new_config: Dict[str, Any]):
        """更新持仓引擎配置"""
        try:
            # 验证持仓引擎特定配置
            valid_hold_keys = {
                'min_confidence_threshold', 'profit_target_ratio', 'stop_loss_ratio',
                'trailing_stop_ratio', 'max_hold_hours', 'min_hold_minutes',
                'emergency_loss_ratio', 'partial_close_threshold', 'rebalance_interval_hours'
            }

            for key, value in new_config.items():
                if key in valid_hold_keys:
                    # 类型和范围验证
                    if key in ['min_confidence_threshold', 'profit_target_ratio', 'stop_loss_ratio',
                              'trailing_stop_ratio', 'emergency_loss_ratio', 'partial_close_threshold']:
                        if not isinstance(value, (int, float)) or not (0 <= value <= 1):
                            logger.warning(f"持仓配置 {key} 的值无效: {value}")
                            continue

                    if key in ['max_hold_hours', 'min_hold_minutes', 'rebalance_interval_hours']:
                        if not isinstance(value, (int, float)) or value <= 0:
                            logger.warning(f"持仓配置 {key} 的值无效: {value}")
                            continue

                    self.hold_config[key] = value
                    self.config[key] = value

            # 更新基础配置
            self.update_config(new_config)

            logger.info("持仓引擎配置已更新")

        except Exception as e:
            logger.error(f"更新持仓引擎配置失败: {e}")
            raise AIEngineError(f"更新持仓引擎配置失败: {e}")

    def analyze_position_performance(self, position_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析持仓表现"""
        try:
            unrealized_pnl = position_info.get('unrealized_pnl', 0)
            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0)
            hold_duration_hours = position_info.get('hold_duration_hours', 0)
            entry_price = position_info.get('entry_price', 0)
            current_price = position_info.get('current_price', 0)

            # 计算表现指标
            performance = {
                'is_profitable': unrealized_pnl > 0,
                'profit_loss_ratio': abs(unrealized_pnl_pct) / max(hold_duration_hours, 0.1),  # 每小时盈亏率
                'price_movement': (current_price - entry_price) / entry_price * 100 if entry_price else 0,
                'hold_efficiency': unrealized_pnl_pct / max(hold_duration_hours, 0.1),  # 持仓效率
                'risk_level': 'LOW' if abs(unrealized_pnl_pct) < 3 else 'MEDIUM' if abs(unrealized_pnl_pct) < 7 else 'HIGH'
            }

            # 评估持仓状态
            if unrealized_pnl_pct > 10:
                performance['status'] = 'EXCELLENT'
            elif unrealized_pnl_pct > 5:
                performance['status'] = 'GOOD'
            elif unrealized_pnl_pct > -3:
                performance['status'] = 'NEUTRAL'
            elif unrealized_pnl_pct > -7:
                performance['status'] = 'POOR'
            else:
                performance['status'] = 'CRITICAL'

            # 建议
            if performance['status'] in ['EXCELLENT', 'GOOD']:
                performance['suggestion'] = 'CONSIDER_PARTIAL_PROFIT'
            elif performance['status'] == 'CRITICAL':
                performance['suggestion'] = 'IMMEDIATE_CLOSE'
            elif hold_duration_hours > self.config.get('max_hold_hours', 24) * 0.8:
                performance['suggestion'] = 'TIME_BASED_CLOSE'
            else:
                performance['suggestion'] = 'CONTINUE_HOLD'

            return performance

        except Exception as e:
            logger.error(f"分析持仓表现失败: {e}")
            return {'status': 'UNKNOWN', 'suggestion': 'CONTINUE_HOLD'}


# 全局持仓引擎实例
_hold_engine_instance = None


def get_ai_hold_engine() -> AIHoldEngine:
    """获取AI持仓引擎实例"""
    global _hold_engine_instance
    if _hold_engine_instance is None:
        _hold_engine_instance = AIHoldEngine()
    return _hold_engine_instance
