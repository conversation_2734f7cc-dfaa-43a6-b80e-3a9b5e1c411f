# -*- coding: utf-8 -*-
"""
AI提示词模板
定义开仓和持仓引擎的专业化提示词模板，包含技术分析指标解读、市场趋势判断和风险评估的专业提示词

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PromptTemplate:
    """提示词模板数据结构"""
    name: str
    description: str
    template: str
    required_params: List[str]
    optional_params: List[str]
    response_format: Dict[str, Any]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        """验证参数完整性"""
        for param in self.required_params:
            if param not in params:
                logger.error(f"缺少必需参数: {param}")
                return False
        return True
    
    def format(self, **kwargs) -> str:
        """格式化提示词模板"""
        try:
            if not self.validate_params(kwargs):
                raise ValueError("参数验证失败")
            
            # 为可选参数提供默认值
            for param in self.optional_params:
                if param not in kwargs:
                    kwargs[param] = ""
            
            return self.template.format(**kwargs)
            
        except Exception as e:
            logger.error(f"格式化提示词失败: {e}")
            raise


class AIPromptTemplates:
    """AI提示词模板管理器"""
    
    def __init__(self):
        """初始化提示词模板"""
        self.templates = {}
        self._initialize_templates()
        logger.info("AI提示词模板初始化完成")
    
    def _initialize_templates(self):
        """初始化所有提示词模板"""
        # 开仓引擎提示词模板
        self.templates['open_engine'] = self._create_open_engine_template()
        
        # 持仓引擎提示词模板
        self.templates['hold_engine'] = self._create_hold_engine_template()
        
        # 技术分析解读模板
        self.templates['technical_analysis'] = self._create_technical_analysis_template()
        
        # 风险评估模板
        self.templates['risk_assessment'] = self._create_risk_assessment_template()
    
    def _create_open_engine_template(self) -> PromptTemplate:
        """创建开仓引擎提示词模板"""
        template = """
你是一位资深的加密货币量化交易分析师，专门负责开仓决策分析。请基于以下技术指标数据，对 {symbol} 进行专业的市场分析并给出开仓建议。

=== 市场基本信息 ===
交易对: {symbol}
交易所: {exchange}
分析时间: {analysis_time}
当前价格: {current_price}
24小时涨跌: {price_change_24h}%

=== 多时间周期技术分析 ===
{technical_indicators}

=== 市场环境概况 ===
整体趋势: {market_trend}
市场情绪: {market_sentiment}
波动率水平: {volatility_level}
成交量状态: {volume_status}

=== 专业分析要求 ===

**1. 趋势分析 (Trend Analysis)**
- 分析SMA、EMA移动平均线的排列和价格位置关系
- 评估MACD指标的多空信号和背离情况
- 判断ADX趋势强度指标的市场趋势力度
- 综合多时间周期判断趋势的一致性和可持续性

**2. 动量分析 (Momentum Analysis)**
- 分析RSI相对强弱指标的超买超卖状态
- 评估STOCH随机指标的K值D值交叉信号
- 判断CCI商品通道指数的极值区域信号
- 分析Williams %R威廉指标的反转信号

**3. 成交量分析 (Volume Analysis)**
- 分析OBV能量潮指标的资金流向
- 评估A/D累积/派发线的买卖压力
- 判断ADOSC价格震荡指标的确认信号
- 分析成交量与价格的背离和确认关系

**4. 波动率分析 (Volatility Analysis)**
- 分析ATR真实波动幅度的市场活跃度
- 评估布林带的价格位置和挤压扩张状态
- 判断波动率突破的方向和强度
- 分析市场波动率对交易时机的影响

**5. 风险评估 (Risk Assessment)**
- 评估当前市场环境的整体风险水平
- 分析技术指标的一致性和分歧程度
- 判断潜在的市场风险因素和不确定性
- 提供风险控制建议和仓位管理策略

=== 输出格式要求 ===
请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{{
    "direction": "BULLISH/BEARISH/NEUTRAL",
    "confidence": 85,
    "signal_strength": 78,
    "entry_timing": "IMMEDIATE/WAIT/AVOID",
    "reasoning": "详细的分析推理过程，包括技术指标解读、趋势判断、风险评估等，至少200字",
    "technical_summary": {{
        "trend_analysis": "趋势分析结论",
        "momentum_status": "动量指标状态",
        "volume_confirmation": true,
        "volatility_assessment": "波动率评估"
    }},
    "risk_factors": {{
        "risk_level": "LOW/MEDIUM/HIGH",
        "key_risks": ["风险因素1", "风险因素2"],
        "risk_mitigation": "风险缓解建议"
    }},
    "trading_parameters": {{
        "suggested_leverage": 10,
        "position_size_ratio": 0.05,
        "stop_loss_level": 45000,
        "take_profit_level": 55000
    }},
    "market_conditions": {{
        "trend_strength": "STRONG/MODERATE/WEAK",
        "market_phase": "TRENDING/RANGING/VOLATILE",
        "sentiment_score": 75,
        "volatility_regime": "HIGH/MEDIUM/LOW"
    }}
}}

**重要提示：**
- confidence 范围：0-100，表示分析的置信度
- signal_strength 范围：0-100，表示信号的强度
- reasoning 必须详细说明分析逻辑和技术依据
- 所有价格建议必须基于技术分析给出具体数值
- 必须考虑多时间周期的一致性和风险因素
"""
        
        return PromptTemplate(
            name="open_engine",
            description="开仓引擎专业化提示词模板",
            template=template.strip(),
            required_params=[
                'symbol', 'exchange', 'analysis_time', 'current_price', 
                'price_change_24h', 'technical_indicators', 'market_trend',
                'market_sentiment', 'volatility_level', 'volume_status'
            ],
            optional_params=[],
            response_format={
                "direction": "str",
                "confidence": "int",
                "signal_strength": "int",
                "entry_timing": "str",
                "reasoning": "str",
                "technical_summary": "dict",
                "risk_factors": "dict",
                "trading_parameters": "dict",
                "market_conditions": "dict"
            }
        )
    
    def _create_hold_engine_template(self) -> PromptTemplate:
        """创建持仓引擎提示词模板"""
        template = """
你是一位专业的加密货币量化交易持仓管理专家，专门负责持仓决策和风险控制。请基于当前持仓信息和市场技术指标，给出专业的持仓管理建议。

=== 当前持仓信息 ===
交易对: {symbol}
持仓方向: {position_side}
持仓数量: {position_size}
开仓价格: {entry_price}
当前价格: {current_price}
未实现盈亏: {unrealized_pnl} ({unrealized_pnl_pct}%)
杠杆倍数: {leverage}x
保证金: {margin}
强平价格: {liquidation_price}
当前止损: {current_stop_loss}
当前止盈: {current_take_profit}
持仓时间: {hold_duration}

=== 市场技术状况 ===
{technical_indicators}

=== 市场环境变化 ===
趋势变化: {trend_change}
动量变化: {momentum_change}
成交量变化: {volume_change}
波动率变化: {volatility_change}

=== 专业持仓管理分析 ===

**1. 盈亏状况评估 (P&L Assessment)**
- 分析当前盈亏比例相对于预期目标的完成度
- 评估盈亏绝对金额对账户的影响程度
- 判断是否达到预设的止盈或止损条件
- 分析盈亏趋势和未来发展预期

**2. 趋势延续性分析 (Trend Continuation Analysis)**
- 评估当前市场趋势是否与持仓方向保持一致
- 分析趋势强度的变化和可持续性
- 判断是否出现趋势反转的早期信号
- 评估趋势变化对持仓的潜在影响

**3. 技术指标确认 (Technical Confirmation)**
- 分析RSI、MACD等动量指标的最新状态
- 评估是否出现技术指标背离或确认信号
- 判断技术指标对当前持仓的支撑或压力
- 分析多时间周期指标的一致性

**4. 风险控制评估 (Risk Control Assessment)**
- 评估当前持仓面临的主要风险因素
- 分析市场波动对持仓的潜在冲击
- 判断是否需要调整止损止盈位置
- 评估持仓规模是否适合当前市场环境

**5. 时间因素考量 (Time Factor Consideration)**
- 分析持仓时间对决策的影响
- 评估是否超过合理的持仓周期
- 考虑时间成本和机会成本
- 判断是否需要基于时间因素调整策略

**6. 市场环境适应 (Market Environment Adaptation)**
- 评估当前市场环境相对于开仓时的变化
- 分析外部因素对持仓的潜在影响
- 判断是否需要根据市场变化调整策略
- 评估持仓策略的适应性和灵活性

=== 输出格式要求 ===
请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{{
    "action": "HOLD/CLOSE/ADJUST_STOP/ADJUST_TARGET/PARTIAL_CLOSE",
    "confidence": 82,
    "urgency": "LOW/MEDIUM/HIGH/EMERGENCY",
    "reasoning": "详细的持仓管理分析，包括盈亏评估、趋势分析、风险控制等，至少200字",
    "position_assessment": {{
        "profit_potential": "HIGH/MEDIUM/LOW",
        "risk_level": "LOW/MEDIUM/HIGH",
        "trend_alignment": true,
        "time_factor": "FAVORABLE/NEUTRAL/UNFAVORABLE"
    }},
    "recommended_adjustments": {{
        "new_stop_loss": 47000,
        "new_take_profit": 55000,
        "position_adjustment": "NONE/REDUCE/INCREASE",
        "close_ratio": 0.0
    }},
    "risk_management": {{
        "current_risk_score": 65,
        "risk_factors": ["市场波动加剧", "趋势信号减弱"],
        "mitigation_actions": ["调整止损", "减少仓位"]
    }},
    "market_outlook": {{
        "short_term_outlook": "BULLISH/BEARISH/NEUTRAL",
        "medium_term_outlook": "BULLISH/BEARISH/NEUTRAL",
        "key_levels": {{
            "support": 48000,
            "resistance": 52000
        }}
    }},
    "timing_considerations": {{
        "hold_duration_assessment": "OPTIMAL/ACCEPTABLE/EXCESSIVE",
        "exit_timing": "IMMEDIATE/SOON/LATER/HOLD",
        "market_timing_score": 70
    }}
}}

**重要提示：**
- confidence 范围：0-100，表示决策的置信度
- close_ratio 范围：0.0-1.0，0表示不平仓，1表示全部平仓
- reasoning 必须详细说明持仓管理的分析逻辑
- 所有价格建议必须基于技术分析和风险评估
- 风险控制是持仓管理的首要考虑因素
- 必须考虑持仓时间、盈亏状况和市场变化
"""
        
        return PromptTemplate(
            name="hold_engine",
            description="持仓引擎专业化提示词模板",
            template=template.strip(),
            required_params=[
                'symbol', 'position_side', 'position_size', 'entry_price',
                'current_price', 'unrealized_pnl', 'unrealized_pnl_pct',
                'leverage', 'margin', 'liquidation_price', 'current_stop_loss',
                'current_take_profit', 'hold_duration', 'technical_indicators',
                'trend_change', 'momentum_change', 'volume_change', 'volatility_change'
            ],
            optional_params=[],
            response_format={
                "action": "str",
                "confidence": "int",
                "urgency": "str",
                "reasoning": "str",
                "position_assessment": "dict",
                "recommended_adjustments": "dict",
                "risk_management": "dict",
                "market_outlook": "dict",
                "timing_considerations": "dict"
            }
        )

    def _create_technical_analysis_template(self) -> PromptTemplate:
        """创建技术分析解读模板"""
        template = """
作为专业的技术分析师，请对以下技术指标进行深入解读和分析：

=== 技术指标数据 ===
{technical_data}

=== 分析框架 ===

**趋势指标解读：**
- SMA/EMA: 分析移动平均线的多空排列和价格位置
- MACD: 解读MACD线、信号线和柱状图的变化
- ADX: 评估趋势强度和方向性运动

**动量指标解读：**
- RSI: 分析相对强弱和超买超卖状态
- STOCH: 解读随机指标的K值D值交叉
- CCI: 评估商品通道指数的极值信号

**成交量指标解读：**
- OBV: 分析能量潮的资金流向
- A/D Line: 解读累积/派发线的买卖压力
- Volume: 评估成交量与价格的关系

**波动率指标解读：**
- ATR: 分析真实波动幅度
- Bollinger Bands: 解读布林带的位置和形态
- Volatility: 评估市场波动率水平

请提供专业的技术分析解读和交易建议。
"""

        return PromptTemplate(
            name="technical_analysis",
            description="技术分析解读模板",
            template=template.strip(),
            required_params=['technical_data'],
            optional_params=[],
            response_format={
                "trend_analysis": "str",
                "momentum_analysis": "str",
                "volume_analysis": "str",
                "volatility_analysis": "str",
                "overall_assessment": "str"
            }
        )

    def _create_risk_assessment_template(self) -> PromptTemplate:
        """创建风险评估模板"""
        template = """
作为专业的风险管理专家，请对当前交易进行全面的风险评估：

=== 风险评估要素 ===
市场数据: {market_data}
持仓信息: {position_info}
技术指标: {technical_indicators}

=== 风险评估框架 ===

**市场风险评估：**
- 分析市场波动率和流动性风险
- 评估价格跳空和极端行情风险
- 判断市场情绪和系统性风险

**技术风险评估：**
- 分析技术指标的可靠性和一致性
- 评估技术信号的强度和持续性
- 判断技术分析的有效性和局限性

**仓位风险评估：**
- 分析当前仓位的风险暴露
- 评估杠杆倍数的风险放大效应
- 判断仓位规模的合理性

**时间风险评估：**
- 分析持仓时间的风险累积
- 评估市场时机的不确定性
- 判断时间成本和机会成本

请提供专业的风险评估和控制建议。
"""

        return PromptTemplate(
            name="risk_assessment",
            description="风险评估模板",
            template=template.strip(),
            required_params=['market_data', 'position_info', 'technical_indicators'],
            optional_params=[],
            response_format={
                "market_risk": "str",
                "technical_risk": "str",
                "position_risk": "str",
                "time_risk": "str",
                "overall_risk_score": "int",
                "risk_mitigation": "str"
            }
        )

    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """获取指定的提示词模板"""
        return self.templates.get(template_name)

    def format_prompt(self, template_name: str, **kwargs) -> str:
        """格式化指定的提示词模板"""
        try:
            template = self.get_template(template_name)
            if not template:
                raise ValueError(f"未找到模板: {template_name}")

            return template.format(**kwargs)

        except Exception as e:
            logger.error(f"格式化提示词失败: {template_name}, 错误: {e}")
            raise

    def list_templates(self) -> List[str]:
        """列出所有可用的模板名称"""
        return list(self.templates.keys())

    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """获取模板信息"""
        template = self.get_template(template_name)
        if not template:
            return None

        return {
            'name': template.name,
            'description': template.description,
            'required_params': template.required_params,
            'optional_params': template.optional_params,
            'response_format': template.response_format
        }

    def validate_template_params(self, template_name: str, params: Dict[str, Any]) -> bool:
        """验证模板参数"""
        template = self.get_template(template_name)
        if not template:
            return False

        return template.validate_params(params)


class TechnicalIndicatorFormatter:
    """技术指标格式化器"""

    @staticmethod
    def format_indicators_for_prompt(indicators_data: Dict[str, Any]) -> str:
        """将技术指标数据格式化为提示词格式"""
        try:
            formatted_lines = []

            for timeframe, tf_data in indicators_data.items():
                if not tf_data or 'indicators' not in tf_data:
                    continue

                indicators = tf_data['indicators']
                current_price = tf_data.get('current_price', 0)
                price_change = tf_data.get('price_change', 0)
                price_change_pct = tf_data.get('price_change_pct', 0)

                formatted_lines.append(f"\n**{timeframe.upper()} 时间周期分析**")
                formatted_lines.append(f"当前价格: {current_price:.2f}")
                formatted_lines.append(f"价格变化: {price_change:+.2f} ({price_change_pct:+.2f}%)")

                # 趋势指标
                if 'trend' in indicators:
                    trend = indicators['trend']
                    formatted_lines.append("\n趋势指标:")

                    if trend.get('sma_20'):
                        sma_relation = "上方" if current_price > trend['sma_20'] else "下方"
                        formatted_lines.append(f"  • SMA-20: {trend['sma_20']:.2f} (价格在{sma_relation})")

                    if trend.get('ema_20'):
                        ema_relation = "上方" if current_price > trend['ema_20'] else "下方"
                        formatted_lines.append(f"  • EMA-20: {trend['ema_20']:.2f} (价格在{ema_relation})")

                    if trend.get('macd_line') and trend.get('macd_signal'):
                        if trend['macd_line'] > trend['macd_signal']:
                            macd_signal = "多头金叉"
                        else:
                            macd_signal = "空头死叉"
                        formatted_lines.append(f"  • MACD: {trend['macd_line']:.4f} / 信号: {trend['macd_signal']:.4f} ({macd_signal})")

                    if trend.get('adx'):
                        if trend['adx'] > 25:
                            adx_strength = "强势趋势"
                        elif trend['adx'] > 20:
                            adx_strength = "正常趋势"
                        else:
                            adx_strength = "弱势趋势"
                        formatted_lines.append(f"  • ADX: {trend['adx']:.2f} ({adx_strength})")

                # 动量指标
                if 'momentum' in indicators:
                    momentum = indicators['momentum']
                    formatted_lines.append("\n动量指标:")

                    if momentum.get('rsi'):
                        if momentum['rsi'] > 70:
                            rsi_level = "超买区域"
                        elif momentum['rsi'] < 30:
                            rsi_level = "超卖区域"
                        else:
                            rsi_level = "正常区间"
                        formatted_lines.append(f"  • RSI: {momentum['rsi']:.2f} ({rsi_level})")

                    if momentum.get('stoch_k') and momentum.get('stoch_d'):
                        stoch_signal = "金叉看涨" if momentum['stoch_k'] > momentum['stoch_d'] else "死叉看跌"
                        stoch_level = "超买区域" if momentum['stoch_k'] > 80 else "超卖区域" if momentum['stoch_k'] < 20 else "正常区间"
                        formatted_lines.append(f"  • STOCH: K={momentum['stoch_k']:.2f} D={momentum['stoch_d']:.2f} ({stoch_signal}, {stoch_level})")

                    if momentum.get('cci'):
                        cci_level = "强势超买" if momentum['cci'] > 100 else "弱势超卖" if momentum['cci'] < -100 else "震荡区间"
                        formatted_lines.append(f"  • CCI: {momentum['cci']:.2f} ({cci_level})")

                    if momentum.get('williams_r'):
                        wr_level = "顶部超买" if momentum['williams_r'] > -20 else "底部超卖" if momentum['williams_r'] < -80 else "中性区域"
                        formatted_lines.append(f"  • Williams %R: {momentum['williams_r']:.2f} ({wr_level})")

                # 成交量指标
                if 'volume' in indicators:
                    volume = indicators['volume']
                    formatted_lines.append("\n成交量指标:")

                    if volume.get('obv'):
                        formatted_lines.append(f"  • OBV: {volume['obv']:.0f}")

                    if volume.get('ad'):
                        formatted_lines.append(f"  • A/D Line: {volume['ad']:.0f}")

                    if volume.get('adosc'):
                        formatted_lines.append(f"  • ADOSC: {volume['adosc']:.0f}")

                # 波动率指标
                if 'volatility' in indicators:
                    volatility = indicators['volatility']
                    formatted_lines.append("\n波动率指标:")

                    if volatility.get('atr'):
                        formatted_lines.append(f"  • ATR: {volatility['atr']:.2f}")

                    if all(volatility.get(k) for k in ['bb_upper', 'bb_middle', 'bb_lower']):
                        bb_position = "上轨" if current_price > volatility['bb_upper'] * 0.98 else \
                                    "下轨" if current_price < volatility['bb_lower'] * 1.02 else "中轨"
                        bb_width = (volatility['bb_upper'] - volatility['bb_lower']) / volatility['bb_middle'] * 100
                        formatted_lines.append(f"  • 布林带: 上={volatility['bb_upper']:.2f} 中={volatility['bb_middle']:.2f} 下={volatility['bb_lower']:.2f}")
                        formatted_lines.append(f"    价格位置: {bb_position}附近, 带宽: {bb_width:.2f}%")

            return '\n'.join(formatted_lines)

        except Exception as e:
            logger.error(f"格式化技术指标失败: {e}")
            return "技术指标格式化失败"

    @staticmethod
    def format_market_summary(summary_data: Dict[str, Any]) -> Dict[str, str]:
        """格式化市场摘要数据"""
        try:
            return {
                'market_trend': summary_data.get('trend_consensus', '未知'),
                'market_sentiment': summary_data.get('sentiment', '中性'),
                'volatility_level': summary_data.get('volatility', '中等'),
                'volume_status': summary_data.get('volume_status', '正常')
            }

        except Exception as e:
            logger.error(f"格式化市场摘要失败: {e}")
            return {
                'market_trend': '未知',
                'market_sentiment': '中性',
                'volatility_level': '中等',
                'volume_status': '正常'
            }


# 全局提示词模板管理器实例
_prompt_templates_instance = None


def get_prompt_templates() -> AIPromptTemplates:
    """获取提示词模板管理器实例"""
    global _prompt_templates_instance
    if _prompt_templates_instance is None:
        _prompt_templates_instance = AIPromptTemplates()
    return _prompt_templates_instance


def format_technical_indicators(indicators_data: Dict[str, Any]) -> str:
    """格式化技术指标数据（便捷函数）"""
    return TechnicalIndicatorFormatter.format_indicators_for_prompt(indicators_data)


def format_market_summary(summary_data: Dict[str, Any]) -> Dict[str, str]:
    """格式化市场摘要数据（便捷函数）"""
    return TechnicalIndicatorFormatter.format_market_summary(summary_data)
