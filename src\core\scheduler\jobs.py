# -*- coding: utf-8 -*-
"""
定时任务定义
定义系统中所有的定时任务和作业

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

from src.core.ai_engines import get_ai_open_engine, get_ai_hold_engine
from src.core.trading import get_trade_executor, get_risk_manager, get_position_manager
from src.services.market_data_service import get_market_data_service
from src.utils.logger import get_logger

logger = get_logger(__name__)


def market_analysis_job():
    """市场分析任务 - 每5分钟执行一次"""
    try:
        logger.info("开始执行市场分析任务")
        
        # 获取AI开仓引擎
        open_engine = get_ai_open_engine()
        
        # 获取市场数据服务
        market_data_service = get_market_data_service()
        
        # 获取主要交易对的市场数据
        symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
        
        for symbol in symbols:
            try:
                # 获取市场数据
                market_data = market_data_service.get_market_data(symbol)
                
                if market_data.get('success', False):
                    # 进行AI分析
                    analysis_result = open_engine.analyze_market(symbol, market_data['data'])
                    
                    logger.info(f"市场分析完成: {symbol}, 信号强度: {analysis_result.get('confidence', 0)}")
                else:
                    logger.warning(f"获取市场数据失败: {symbol}")
                    
            except Exception as e:
                logger.error(f"分析交易对 {symbol} 失败: {e}")
        
        logger.info("市场分析任务执行完成")
        
    except Exception as e:
        logger.error(f"市场分析任务执行失败: {e}")
        raise


def position_monitoring_job():
    """仓位监控任务 - 每1分钟执行一次"""
    try:
        logger.info("开始执行仓位监控任务")
        
        # 获取仓位管理器和AI持仓引擎
        position_manager = get_position_manager()
        hold_engine = get_ai_hold_engine()
        
        # 获取所有活跃仓位
        positions = position_manager.get_all_positions()
        
        if not positions:
            logger.debug("当前无活跃仓位")
            return
        
        for position in positions:
            try:
                # 评估仓位风险
                risk_assessment = position_manager.assess_position_risk(position)
                
                # 如果风险过高，进行AI分析
                if risk_assessment.get('is_high_risk', False):
                    logger.warning(f"检测到高风险仓位: {position.symbol}")
                    
                    # AI持仓分析
                    hold_analysis = hold_engine.analyze_position(position)
                    
                    # 根据AI建议执行操作
                    if hold_analysis.get('action') == 'close':
                        logger.info(f"AI建议平仓: {position.symbol}")
                        # 这里可以添加自动平仓逻辑
                
                # 更新仓位信息
                position_manager.record_position_history(position, "monitor")
                
            except Exception as e:
                logger.error(f"监控仓位 {position.symbol} 失败: {e}")
        
        logger.info(f"仓位监控任务执行完成，监控了 {len(positions)} 个仓位")
        
    except Exception as e:
        logger.error(f"仓位监控任务执行失败: {e}")
        raise


def risk_assessment_job():
    """风险评估任务 - 每10分钟执行一次"""
    try:
        logger.info("开始执行风险评估任务")
        
        # 获取风险管理器
        risk_manager = get_risk_manager()
        
        # 进行账户风险评估
        risk_assessment = risk_manager.assess_account_risk()
        
        logger.info(f"风险评估完成: 等级={risk_assessment.risk_level.value}, 分数={risk_assessment.risk_score}")
        
        # 如果风险过高，记录警告
        if risk_assessment.risk_level.value in ['HIGH', 'CRITICAL']:
            logger.warning(f"检测到高风险状态: {risk_assessment.risk_level.value}")
            logger.warning(f"风险因素: {', '.join(risk_assessment.risk_factors)}")
            
            # 如果是极高风险，可以考虑紧急措施
            if risk_assessment.risk_level.value == 'CRITICAL':
                logger.critical("检测到极高风险，建议立即停止交易")
        
        logger.info("风险评估任务执行完成")
        
    except Exception as e:
        logger.error(f"风险评估任务执行失败: {e}")
        raise


def system_health_check_job():
    """系统健康检查任务 - 每30分钟执行一次"""
    try:
        logger.info("开始执行系统健康检查任务")
        
        # 检查各个组件状态
        components_status = {}
        
        # 检查交易执行器
        try:
            executor = get_trade_executor()
            executor_stats = executor.get_executor_stats()
            components_status['trade_executor'] = {
                'status': 'healthy',
                'total_orders': executor_stats.get('total_orders', 0),
                'success_rate': executor_stats.get('success_rate', 0)
            }
        except Exception as e:
            components_status['trade_executor'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # 检查风险管理器
        try:
            risk_manager = get_risk_manager()
            risk_stats = risk_manager.get_risk_manager_stats()
            components_status['risk_manager'] = {
                'status': 'healthy',
                'total_validations': risk_stats.get('total_validations', 0),
                'validation_success_rate': risk_stats.get('validation_success_rate', 0)
            }
        except Exception as e:
            components_status['risk_manager'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # 检查仓位管理器
        try:
            position_manager = get_position_manager()
            position_stats = position_manager.get_position_manager_stats()
            components_status['position_manager'] = {
                'status': 'healthy',
                'total_updates': position_stats.get('total_updates', 0),
                'cache_hit_rate': position_stats.get('cache_hit_rate', 0)
            }
        except Exception as e:
            components_status['position_manager'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # 统计健康状态
        healthy_components = sum(1 for status in components_status.values() if status['status'] == 'healthy')
        total_components = len(components_status)
        
        logger.info(f"系统健康检查完成: {healthy_components}/{total_components} 组件正常")
        
        # 如果有组件异常，记录详细信息
        for component, status in components_status.items():
            if status['status'] != 'healthy':
                logger.error(f"组件异常: {component}, 错误: {status.get('error', '未知错误')}")
        
        return components_status
        
    except Exception as e:
        logger.error(f"系统健康检查任务执行失败: {e}")
        raise


def data_cleanup_job():
    """数据清理任务 - 每天凌晨2点执行一次"""
    try:
        logger.info("开始执行数据清理任务")
        
        # 清理仓位管理器历史记录
        try:
            position_manager = get_position_manager()
            
            # 获取清理前的记录数
            before_count = position_manager.get_position_manager_stats().get('position_history_count', 0)
            
            # 清理历史记录
            position_manager.clear_history()
            
            # 获取清理后的记录数
            after_count = position_manager.get_position_manager_stats().get('position_history_count', 0)
            
            logger.info(f"仓位历史记录清理完成: {before_count} -> {after_count}")
            
        except Exception as e:
            logger.error(f"清理仓位历史记录失败: {e}")
        
        # 清理缓存
        try:
            position_manager = get_position_manager()
            position_manager.clear_cache()
            logger.info("仓位缓存清理完成")
            
        except Exception as e:
            logger.error(f"清理仓位缓存失败: {e}")
        
        logger.info("数据清理任务执行完成")
        
    except Exception as e:
        logger.error(f"数据清理任务执行失败: {e}")
        raise


def performance_report_job():
    """性能报告任务 - 每小时执行一次"""
    try:
        logger.info("开始生成性能报告")
        
        # 收集各组件性能数据
        performance_data = {}
        
        # 交易执行器性能
        try:
            executor = get_trade_executor()
            executor_stats = executor.get_executor_stats()
            performance_data['trade_executor'] = {
                'total_orders': executor_stats.get('total_orders', 0),
                'success_rate': executor_stats.get('success_rate', 0),
                'total_volume': executor_stats.get('total_volume', 0),
                'total_fees': executor_stats.get('total_fees', 0)
            }
        except Exception as e:
            logger.error(f"获取交易执行器性能数据失败: {e}")
        
        # 风险管理器性能
        try:
            risk_manager = get_risk_manager()
            risk_stats = risk_manager.get_risk_manager_stats()
            performance_data['risk_manager'] = {
                'total_validations': risk_stats.get('total_validations', 0),
                'validation_success_rate': risk_stats.get('validation_success_rate', 0),
                'emergency_stops': risk_stats.get('emergency_stops', 0)
            }
        except Exception as e:
            logger.error(f"获取风险管理器性能数据失败: {e}")
        
        # 仓位管理器性能
        try:
            position_manager = get_position_manager()
            position_stats = position_manager.get_position_manager_stats()
            summary = position_manager.calculate_position_summary()
            
            performance_data['position_manager'] = {
                'total_updates': position_stats.get('total_updates', 0),
                'cache_hit_rate': position_stats.get('cache_hit_rate', 0),
                'total_positions': summary.total_positions,
                'win_rate': summary.win_rate,
                'total_pnl': summary.total_unrealized_pnl + summary.total_realized_pnl
            }
        except Exception as e:
            logger.error(f"获取仓位管理器性能数据失败: {e}")
        
        # 生成性能报告
        logger.info("=== 系统性能报告 ===")
        logger.info(f"报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        for component, data in performance_data.items():
            logger.info(f"{component}: {data}")
        
        logger.info("性能报告生成完成")
        
        return performance_data
        
    except Exception as e:
        logger.error(f"性能报告任务执行失败: {e}")
        raise


def trading_execution_job():
    """交易执行任务 - 每3分钟执行一次"""
    try:
        logger.info("开始执行交易执行任务")

        # 获取AI开仓引擎和交易执行器
        open_engine = get_ai_open_engine()
        trade_executor = get_trade_executor()
        risk_manager = get_risk_manager()
        position_manager = get_position_manager()

        # 获取当前持仓状态
        current_positions = position_manager.get_all_positions()

        # 如果没有持仓，考虑开仓
        if not current_positions:
            logger.info("当前无持仓，分析开仓机会")

            # 获取主要交易对
            symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']

            for symbol in symbols:
                try:
                    # 进行市场分析
                    analysis_result = open_engine.analyze_market(symbol)

                    if analysis_result.get('should_open', False):
                        confidence = analysis_result.get('confidence', 0)
                        direction = analysis_result.get('direction', 'none')

                        if confidence >= 70 and direction in ['long', 'short']:
                            logger.info(f"发现开仓机会: {symbol} {direction} 置信度: {confidence}%")

                            # 验证交易参数
                            validation = risk_manager.validate_trade(symbol, direction, 0.1, 10)

                            if validation.is_valid:
                                logger.info(f"交易参数验证通过，准备开仓: {symbol}")
                                # 这里可以添加实际的开仓逻辑
                                # result = trade_executor.open_position(symbol, direction, 0.1, 10)
                            else:
                                logger.warning(f"交易参数验证失败: {', '.join(validation.validation_errors)}")

                except Exception as e:
                    logger.error(f"分析交易对 {symbol} 开仓机会失败: {e}")
        else:
            logger.info(f"当前有 {len(current_positions)} 个持仓，跳过开仓分析")

        logger.info("交易执行任务执行完成")

    except Exception as e:
        logger.error(f"交易执行任务执行失败: {e}")
        raise


def market_data_update_job():
    """市场数据更新任务 - 每30秒执行一次"""
    try:
        logger.debug("开始执行市场数据更新任务")

        # 获取市场数据服务
        market_data_service = get_market_data_service()

        # 更新主要交易对的市场数据
        symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT', 'ADA/USDT:USDT']

        updated_count = 0
        for symbol in symbols:
            try:
                # 更新市场数据
                result = market_data_service.update_market_data(symbol)

                if result.get('success', False):
                    updated_count += 1
                    logger.debug(f"市场数据更新成功: {symbol}")
                else:
                    logger.warning(f"市场数据更新失败: {symbol}")

            except Exception as e:
                logger.error(f"更新交易对 {symbol} 市场数据失败: {e}")

        logger.debug(f"市场数据更新任务执行完成，更新了 {updated_count}/{len(symbols)} 个交易对")

    except Exception as e:
        logger.error(f"市场数据更新任务执行失败: {e}")
        raise


def system_maintenance_job():
    """系统维护任务 - 每6小时执行一次"""
    try:
        logger.info("开始执行系统维护任务")

        # 1. 重置统计数据（如果需要）
        try:
            # 可以选择性地重置某些统计数据
            logger.info("检查统计数据重置需求")

        except Exception as e:
            logger.error(f"重置统计数据失败: {e}")

        # 2. 检查系统资源使用
        try:
            import psutil

            # 获取内存使用情况
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)

            logger.info(f"系统资源使用情况:")
            logger.info(f"  内存使用率: {memory.percent}%")
            logger.info(f"  CPU使用率: {cpu_percent}%")

            # 如果资源使用过高，记录警告
            if memory.percent > 80:
                logger.warning(f"内存使用率过高: {memory.percent}%")

            if cpu_percent > 80:
                logger.warning(f"CPU使用率过高: {cpu_percent}%")

        except ImportError:
            logger.warning("psutil库未安装，跳过系统资源检查")
        except Exception as e:
            logger.error(f"检查系统资源失败: {e}")

        # 3. 清理临时文件和缓存
        try:
            # 清理任务调度器的已完成任务
            from src.core.scheduler import get_task_scheduler
            scheduler = get_task_scheduler()
            scheduler.clear_completed_tasks()

            logger.info("任务调度器清理完成")

        except Exception as e:
            logger.error(f"清理任务调度器失败: {e}")

        logger.info("系统维护任务执行完成")

    except Exception as e:
        logger.error(f"系统维护任务执行失败: {e}")
        raise


# 任务配置字典
SCHEDULED_JOBS = {
    # 基础数据更新任务 - 最高优先级
    'market_data_update': {
        'func': market_data_update_job,
        'trigger': 'interval',
        'seconds': 30,
        'name': '市场数据更新任务',
        'description': '每30秒更新市场数据，为其他任务提供数据基础',
        'priority': 1,
        'dependencies': []
    },

    # 监控类任务 - 高优先级
    'position_monitoring': {
        'func': position_monitoring_job,
        'trigger': 'interval',
        'minutes': 1,
        'name': '仓位监控任务',
        'description': '每1分钟监控活跃仓位，评估风险状态',
        'priority': 2,
        'dependencies': ['market_data_update']
    },

    # 分析类任务 - 中等优先级
    'market_analysis': {
        'func': market_analysis_job,
        'trigger': 'interval',
        'minutes': 5,
        'name': '市场分析任务',
        'description': '每5分钟进行一次市场分析，识别交易机会',
        'priority': 3,
        'dependencies': ['market_data_update']
    },

    'trading_execution': {
        'func': trading_execution_job,
        'trigger': 'interval',
        'minutes': 3,
        'name': '交易执行任务',
        'description': '每3分钟执行交易决策，包括开仓和平仓',
        'priority': 3,
        'dependencies': ['market_analysis', 'position_monitoring']
    },

    'risk_assessment': {
        'func': risk_assessment_job,
        'trigger': 'interval',
        'minutes': 10,
        'name': '风险评估任务',
        'description': '每10分钟进行账户风险评估',
        'priority': 3,
        'dependencies': ['position_monitoring']
    },

    # 系统维护任务 - 低优先级
    'system_health_check': {
        'func': system_health_check_job,
        'trigger': 'interval',
        'minutes': 30,
        'name': '系统健康检查任务',
        'description': '每30分钟检查系统各组件健康状态',
        'priority': 4,
        'dependencies': []
    },

    'performance_report': {
        'func': performance_report_job,
        'trigger': 'interval',
        'hours': 1,
        'name': '性能报告任务',
        'description': '每小时生成系统性能报告',
        'priority': 4,
        'dependencies': []
    },

    'system_maintenance': {
        'func': system_maintenance_job,
        'trigger': 'interval',
        'hours': 6,
        'name': '系统维护任务',
        'description': '每6小时执行系统维护，清理资源和检查状态',
        'priority': 5,
        'dependencies': []
    },

    'data_cleanup': {
        'func': data_cleanup_job,
        'trigger': 'cron',
        'hour': '2',
        'minute': '0',
        'name': '数据清理任务',
        'description': '每天凌晨2点清理历史数据和缓存',
        'priority': 5,
        'dependencies': []
    }
}


# 任务依赖关系管理
class TaskDependencyManager:
    """任务依赖关系管理器"""

    def __init__(self):
        self.dependency_graph = {}
        self.build_dependency_graph()

    def build_dependency_graph(self):
        """构建任务依赖图"""
        for job_id, job_config in SCHEDULED_JOBS.items():
            dependencies = job_config.get('dependencies', [])
            self.dependency_graph[job_id] = dependencies

    def get_execution_order(self) -> List[str]:
        """获取任务执行顺序（拓扑排序）"""
        # 简化的拓扑排序实现
        visited = set()
        temp_visited = set()
        result = []

        def dfs(job_id):
            if job_id in temp_visited:
                raise ValueError(f"检测到循环依赖: {job_id}")

            if job_id in visited:
                return

            temp_visited.add(job_id)

            for dependency in self.dependency_graph.get(job_id, []):
                dfs(dependency)

            temp_visited.remove(job_id)
            visited.add(job_id)
            result.append(job_id)

        for job_id in SCHEDULED_JOBS.keys():
            if job_id not in visited:
                dfs(job_id)

        return result

    def check_dependencies_ready(self, job_id: str, completed_jobs: set) -> bool:
        """检查任务依赖是否已完成"""
        dependencies = self.dependency_graph.get(job_id, [])
        return all(dep in completed_jobs for dep in dependencies)

    def get_priority_order(self) -> List[str]:
        """按优先级获取任务顺序"""
        jobs_with_priority = [
            (job_id, job_config.get('priority', 999))
            for job_id, job_config in SCHEDULED_JOBS.items()
        ]

        # 按优先级排序（数字越小优先级越高）
        jobs_with_priority.sort(key=lambda x: x[1])

        return [job_id for job_id, _ in jobs_with_priority]


def setup_scheduled_jobs(scheduler):
    """设置所有预定义的定时任务"""
    try:
        logger.info("开始设置预定义定时任务")

        # 创建依赖管理器
        dependency_manager = TaskDependencyManager()

        # 按优先级顺序添加任务
        priority_order = dependency_manager.get_priority_order()

        added_count = 0
        for job_id in priority_order:
            job_config = SCHEDULED_JOBS[job_id]

            try:
                # 根据触发器类型添加任务
                if job_config['trigger'] == 'interval':
                    # 间隔任务
                    kwargs = {}
                    if 'seconds' in job_config:
                        kwargs['seconds'] = job_config['seconds']
                    if 'minutes' in job_config:
                        kwargs['minutes'] = job_config['minutes']
                    if 'hours' in job_config:
                        kwargs['hours'] = job_config['hours']
                    if 'days' in job_config:
                        kwargs['days'] = job_config['days']

                    success = scheduler.add_interval_task(
                        task_id=job_id,
                        name=job_config['name'],
                        func=job_config['func'],
                        description=job_config['description'],
                        **kwargs
                    )

                elif job_config['trigger'] == 'cron':
                    # 定时任务
                    success = scheduler.add_cron_task(
                        task_id=job_id,
                        name=job_config['name'],
                        func=job_config['func'],
                        minute=job_config.get('minute', '*'),
                        hour=job_config.get('hour', '*'),
                        day=job_config.get('day', '*'),
                        month=job_config.get('month', '*'),
                        day_of_week=job_config.get('day_of_week', '*'),
                        description=job_config['description']
                    )

                else:
                    logger.error(f"不支持的触发器类型: {job_config['trigger']}")
                    continue

                if success:
                    added_count += 1
                    logger.info(f"任务添加成功: {job_id} - {job_config['name']}")
                else:
                    logger.error(f"任务添加失败: {job_id}")

            except Exception as e:
                logger.error(f"添加任务 {job_id} 失败: {e}")

        logger.info(f"预定义定时任务设置完成: {added_count}/{len(SCHEDULED_JOBS)} 个任务添加成功")

        # 输出任务依赖关系
        logger.info("任务依赖关系:")
        for job_id, dependencies in dependency_manager.dependency_graph.items():
            if dependencies:
                logger.info(f"  {job_id} 依赖于: {', '.join(dependencies)}")
            else:
                logger.info(f"  {job_id} 无依赖")

        return added_count == len(SCHEDULED_JOBS)

    except Exception as e:
        logger.error(f"设置预定义定时任务失败: {e}")
        return False


def get_job_statistics() -> Dict[str, Any]:
    """获取任务统计信息"""
    try:
        stats = {
            'total_jobs': len(SCHEDULED_JOBS),
            'jobs_by_trigger': {},
            'jobs_by_priority': {},
            'dependency_count': 0
        }

        # 按触发器类型统计
        for job_config in SCHEDULED_JOBS.values():
            trigger = job_config['trigger']
            stats['jobs_by_trigger'][trigger] = stats['jobs_by_trigger'].get(trigger, 0) + 1

        # 按优先级统计
        for job_config in SCHEDULED_JOBS.values():
            priority = job_config.get('priority', 999)
            stats['jobs_by_priority'][priority] = stats['jobs_by_priority'].get(priority, 0) + 1

        # 统计依赖关系
        for job_config in SCHEDULED_JOBS.values():
            dependencies = job_config.get('dependencies', [])
            stats['dependency_count'] += len(dependencies)

        return stats

    except Exception as e:
        logger.error(f"获取任务统计信息失败: {e}")
        return {}
