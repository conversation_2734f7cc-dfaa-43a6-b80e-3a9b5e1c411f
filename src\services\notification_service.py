# -*- coding: utf-8 -*-
"""
通知服务
处理系统通知和告警

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
import json
import time
import threading
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from threading import Lock, Thread, Event
from concurrent.futures import ThreadPoolExecutor
import queue

from src.data.repositories.log_repository import get_log_repository
from src.data.repositories.config_repository import get_config_repository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class NotificationLevel(Enum):
    """通知级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    SUCCESS = "success"


class NotificationType(Enum):
    """通知类型"""
    SYSTEM = "system"
    TRADING = "trading"
    AI_ANALYSIS = "ai_analysis"
    MARKET_DATA = "market_data"
    ERROR = "error"


class NotificationChannel(Enum):
    """通知渠道"""
    LOG = "log"
    UI = "ui"
    EMAIL = "email"  # 预留
    WEBHOOK = "webhook"  # 预留


class Notification:
    """通知对象"""
    
    def __init__(self, title: str, message: str, level: NotificationLevel,
                 notification_type: NotificationType, details: Dict = None):
        """
        初始化通知
        
        Args:
            title: 通知标题
            message: 通知消息
            level: 通知级别
            notification_type: 通知类型
            details: 详细信息
        """
        self.id = self._generate_id()
        self.title = title
        self.message = message
        self.level = level
        self.notification_type = notification_type
        self.details = details or {}
        self.timestamp = datetime.now()
        self.read = False
    
    def _generate_id(self) -> str:
        """生成通知ID"""
        import uuid
        return str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'level': self.level.value,
            'type': self.notification_type.value,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'read': self.read
        }
    
    def to_json(self) -> str:
        """转换为JSON"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class NotificationFilter:
    """通知过滤器"""

    def __init__(self, level: NotificationLevel = None,
                 notification_type: NotificationType = None,
                 keywords: List[str] = None):
        self.level = level
        self.notification_type = notification_type
        self.keywords = keywords or []

    def matches(self, notification: 'Notification') -> bool:
        """检查通知是否匹配过滤条件"""
        if self.level and notification.level != self.level:
            return False

        if self.notification_type and notification.notification_type != self.notification_type:
            return False

        if self.keywords:
            message_text = f"{notification.title} {notification.message}".lower()
            if not any(keyword.lower() in message_text for keyword in self.keywords):
                return False

        return True


class NotificationService:
    """通知服务类"""

    def __init__(self):
        """初始化通知服务"""
        self.log_repo = get_log_repository()
        self.config_repo = get_config_repository()

        # 通知存储
        self._notifications: List[Notification] = []
        self._notifications_lock = Lock()
        self._max_notifications = 1000  # 最大通知数量

        # 通知订阅者
        self._subscribers: Dict[str, List[Callable]] = {
            channel.value: [] for channel in NotificationChannel
        }

        # 通知过滤器
        self._filters: Dict[str, NotificationFilter] = {}
        self._filters_lock = Lock()

        # 实时推送
        self._push_queue = queue.Queue()
        self._push_thread = None
        self._push_stop_event = Event()
        self._executor = ThreadPoolExecutor(max_workers=5)

        # 通知统计
        self._stats = {
            'total_sent': 0,
            'sent_by_level': {level.value: 0 for level in NotificationLevel},
            'sent_by_type': {ntype.value: 0 for ntype in NotificationType},
            'sent_by_channel': {channel.value: 0 for channel in NotificationChannel}
        }
        self._stats_lock = Lock()

        # 配置
        self._load_config()

        # 启动推送线程
        self._start_push_thread()

        logger.info("通知服务初始化完成")
    
    def _load_config(self):
        """加载配置"""
        try:
            # 加载通知配置
            self.enabled_channels = self.config_repo.get_config_value(
                'notification_enabled_channels', 
                ['log', 'ui']
            )
            
            self.log_level_mapping = {
                NotificationLevel.INFO: 'INFO',
                NotificationLevel.WARNING: 'WARNING',
                NotificationLevel.ERROR: 'ERROR',
                NotificationLevel.CRITICAL: 'CRITICAL',
                NotificationLevel.SUCCESS: 'INFO'
            }
            
        except Exception as e:
            logger.warning(f"加载通知配置失败: {e}")
            self.enabled_channels = ['log', 'ui']

    def _start_push_thread(self):
        """启动推送线程"""
        self._push_thread = Thread(target=self._push_worker, daemon=True)
        self._push_thread.start()
        logger.debug("通知推送线程已启动")

    def _push_worker(self):
        """推送工作线程"""
        while not self._push_stop_event.is_set():
            try:
                # 从队列获取通知
                notification, channels = self._push_queue.get(timeout=1.0)

                # 发送到各个渠道
                for channel in channels:
                    if channel.value in self.enabled_channels:
                        self._send_to_channel(notification, channel)

                # 更新统计
                self._update_stats(notification, channels)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"推送线程异常: {e}")

    def _update_stats(self, notification: Notification, channels: List[NotificationChannel]):
        """更新统计信息"""
        with self._stats_lock:
            self._stats['total_sent'] += 1
            self._stats['sent_by_level'][notification.level.value] += 1
            self._stats['sent_by_type'][notification.notification_type.value] += 1

            for channel in channels:
                if channel.value in self.enabled_channels:
                    self._stats['sent_by_channel'][channel.value] += 1

    def add_filter(self, filter_name: str, notification_filter: NotificationFilter):
        """添加通知过滤器"""
        with self._filters_lock:
            self._filters[filter_name] = notification_filter
            logger.debug(f"添加通知过滤器: {filter_name}")

    def remove_filter(self, filter_name: str):
        """移除通知过滤器"""
        with self._filters_lock:
            if filter_name in self._filters:
                del self._filters[filter_name]
                logger.debug(f"移除通知过滤器: {filter_name}")

    def _apply_filters(self, notification: Notification) -> bool:
        """应用过滤器"""
        with self._filters_lock:
            for filter_name, notification_filter in self._filters.items():
                if not notification_filter.matches(notification):
                    logger.debug(f"通知被过滤器 {filter_name} 过滤")
                    return False
        return True

    def send_notification(self, title: str, message: str, level: NotificationLevel,
                         notification_type: NotificationType, details: Dict = None,
                         channels: List[NotificationChannel] = None) -> str:
        """
        发送通知
        
        Args:
            title: 通知标题
            message: 通知消息
            level: 通知级别
            notification_type: 通知类型
            details: 详细信息
            channels: 指定通知渠道，None则使用默认渠道
            
        Returns:
            通知ID
        """
        try:
            # 创建通知对象
            notification = Notification(title, message, level, notification_type, details)

            # 应用过滤器
            if not self._apply_filters(notification):
                logger.debug(f"通知被过滤器过滤: {notification.id}")
                return ""

            # 存储通知
            self._store_notification(notification)

            # 确定发送渠道
            if channels is None:
                channels = [NotificationChannel.LOG, NotificationChannel.UI]

            # 加入推送队列
            self._push_queue.put((notification, channels))

            logger.debug(f"通知发送成功: {notification.id}")
            return notification.id
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            return ""
    
    def _store_notification(self, notification: Notification):
        """存储通知"""
        with self._notifications_lock:
            self._notifications.append(notification)
            
            # 限制通知数量
            if len(self._notifications) > self._max_notifications:
                self._notifications = self._notifications[-self._max_notifications:]
    
    def _send_to_channel(self, notification: Notification, channel: NotificationChannel):
        """发送到指定渠道"""
        try:
            if channel == NotificationChannel.LOG:
                self._send_to_log(notification)
            elif channel == NotificationChannel.UI:
                self._send_to_ui(notification)
            elif channel == NotificationChannel.EMAIL:
                self._send_to_email(notification)
            elif channel == NotificationChannel.WEBHOOK:
                self._send_to_webhook(notification)
            
            # 通知订阅者
            self._notify_subscribers(channel, notification)
            
        except Exception as e:
            logger.error(f"发送通知到 {channel.value} 失败: {e}")
    
    def _send_to_log(self, notification: Notification):
        """发送到日志"""
        try:
            log_level = self.log_level_mapping.get(notification.level, 'INFO')
            
            # 记录到数据库日志
            self.log_repo.add_log(
                level=log_level,
                module="notification",
                message=f"[{notification.title}] {notification.message}",
                details=json.dumps(notification.details, ensure_ascii=False) if notification.details else None
            )
            
            # 记录到应用日志
            log_message = f"通知: {notification.title} - {notification.message}"
            if notification.level == NotificationLevel.INFO:
                logger.info(log_message)
            elif notification.level == NotificationLevel.WARNING:
                logger.warning(log_message)
            elif notification.level == NotificationLevel.ERROR:
                logger.error(log_message)
            elif notification.level == NotificationLevel.CRITICAL:
                logger.critical(log_message)
            elif notification.level == NotificationLevel.SUCCESS:
                logger.info(f"✅ {log_message}")
            
        except Exception as e:
            logger.error(f"发送通知到日志失败: {e}")
    
    def _send_to_ui(self, notification: Notification):
        """发送到UI"""
        # UI通知通过存储的通知列表实现，前端定期轮询获取
        pass
    
    def _send_to_email(self, notification: Notification):
        """发送到邮箱（预留）"""
        # TODO: 实现邮件通知
        logger.debug(f"邮件通知功能暂未实现: {notification.title}")
    
    def _send_to_webhook(self, notification: Notification):
        """发送到Webhook（预留）"""
        # TODO: 实现Webhook通知
        logger.debug(f"Webhook通知功能暂未实现: {notification.title}")
    
    def _notify_subscribers(self, channel: NotificationChannel, notification: Notification):
        """通知订阅者"""
        try:
            subscribers = self._subscribers.get(channel.value, [])
            for callback in subscribers:
                try:
                    callback(notification)
                except Exception as e:
                    logger.error(f"通知订阅者失败: {e}")
        except Exception as e:
            logger.error(f"通知订阅者处理失败: {e}")
    
    def subscribe(self, channel: NotificationChannel, callback: callable) -> bool:
        """订阅通知"""
        try:
            if channel.value not in self._subscribers:
                self._subscribers[channel.value] = []
            
            self._subscribers[channel.value].append(callback)
            logger.debug(f"订阅通知成功: {channel.value}")
            return True
            
        except Exception as e:
            logger.error(f"订阅通知失败: {e}")
            return False
    
    def unsubscribe(self, channel: NotificationChannel, callback: callable) -> bool:
        """取消订阅通知"""
        try:
            if channel.value in self._subscribers:
                if callback in self._subscribers[channel.value]:
                    self._subscribers[channel.value].remove(callback)
                    logger.debug(f"取消订阅通知成功: {channel.value}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"取消订阅通知失败: {e}")
            return False
    
    def get_notifications(self, limit: int = 50, unread_only: bool = False,
                         notification_type: NotificationType = None) -> List[Dict[str, Any]]:
        """获取通知列表"""
        try:
            with self._notifications_lock:
                notifications = self._notifications.copy()
            
            # 过滤条件
            if unread_only:
                notifications = [n for n in notifications if not n.read]
            
            if notification_type:
                notifications = [n for n in notifications if n.notification_type == notification_type]
            
            # 按时间倒序排列
            notifications.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 限制数量
            notifications = notifications[:limit]
            
            return [n.to_dict() for n in notifications]
            
        except Exception as e:
            logger.error(f"获取通知列表失败: {e}")
            return []
    
    def mark_as_read(self, notification_id: str) -> bool:
        """标记通知为已读"""
        try:
            with self._notifications_lock:
                for notification in self._notifications:
                    if notification.id == notification_id:
                        notification.read = True
                        logger.debug(f"通知标记为已读: {notification_id}")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"标记通知为已读失败: {e}")
            return False
    
    def mark_all_as_read(self, notification_type: NotificationType = None) -> int:
        """标记所有通知为已读"""
        try:
            count = 0
            with self._notifications_lock:
                for notification in self._notifications:
                    if not notification.read:
                        if notification_type is None or notification.notification_type == notification_type:
                            notification.read = True
                            count += 1
            
            logger.info(f"标记 {count} 个通知为已读")
            return count
            
        except Exception as e:
            logger.error(f"标记所有通知为已读失败: {e}")
            return 0
    
    def clear_notifications(self, notification_type: NotificationType = None) -> int:
        """清除通知"""
        try:
            count = 0
            with self._notifications_lock:
                if notification_type is None:
                    count = len(self._notifications)
                    self._notifications.clear()
                else:
                    original_count = len(self._notifications)
                    self._notifications = [n for n in self._notifications 
                                         if n.notification_type != notification_type]
                    count = original_count - len(self._notifications)
            
            logger.info(f"清除 {count} 个通知")
            return count
            
        except Exception as e:
            logger.error(f"清除通知失败: {e}")
            return 0
    
    def get_unread_count(self, notification_type: NotificationType = None) -> int:
        """获取未读通知数量"""
        try:
            with self._notifications_lock:
                notifications = self._notifications
                
                if notification_type:
                    notifications = [n for n in notifications if n.notification_type == notification_type]
                
                return sum(1 for n in notifications if not n.read)
                
        except Exception as e:
            logger.error(f"获取未读通知数量失败: {e}")
            return 0
    
    def send_system_notification(self, message: str, level: NotificationLevel = NotificationLevel.INFO,
                               details: Dict = None) -> str:
        """发送系统通知"""
        return self.send_notification(
            title="系统通知",
            message=message,
            level=level,
            notification_type=NotificationType.SYSTEM,
            details=details
        )
    
    def send_trading_notification(self, message: str, level: NotificationLevel = NotificationLevel.INFO,
                                details: Dict = None) -> str:
        """发送交易通知"""
        return self.send_notification(
            title="交易通知",
            message=message,
            level=level,
            notification_type=NotificationType.TRADING,
            details=details
        )
    
    def send_ai_notification(self, message: str, level: NotificationLevel = NotificationLevel.INFO,
                           details: Dict = None) -> str:
        """发送AI分析通知"""
        return self.send_notification(
            title="AI分析",
            message=message,
            level=level,
            notification_type=NotificationType.AI_ANALYSIS,
            details=details
        )
    
    def send_error_notification(self, message: str, error: Exception = None,
                              details: Dict = None) -> str:
        """发送错误通知"""
        error_details = details or {}
        if error:
            error_details.update({
                'error_type': type(error).__name__,
                'error_message': str(error)
            })
        
        return self.send_notification(
            title="系统错误",
            message=message,
            level=NotificationLevel.ERROR,
            notification_type=NotificationType.ERROR,
            details=error_details
        )

    def send_batch_notifications(self, notifications_data: List[Dict[str, Any]]) -> List[str]:
        """批量发送通知"""
        notification_ids = []

        for data in notifications_data:
            try:
                notification_id = self.send_notification(
                    title=data.get('title', ''),
                    message=data.get('message', ''),
                    level=data.get('level', NotificationLevel.INFO),
                    notification_type=data.get('type', NotificationType.SYSTEM),
                    details=data.get('details'),
                    channels=data.get('channels')
                )
                notification_ids.append(notification_id)

            except Exception as e:
                logger.error(f"批量通知发送失败: {e}")
                notification_ids.append("")

        logger.info(f"批量发送通知完成: {len(notification_ids)} 个")
        return notification_ids

    def get_notification_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取通知统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            with self._notifications_lock:
                recent_notifications = [
                    n for n in self._notifications
                    if start_time <= n.timestamp <= end_time
                ]

            # 按级别统计
            level_stats = {}
            for level in NotificationLevel:
                level_stats[level.value] = sum(
                    1 for n in recent_notifications if n.level == level
                )

            # 按类型统计
            type_stats = {}
            for ntype in NotificationType:
                type_stats[ntype.value] = sum(
                    1 for n in recent_notifications if n.notification_type == ntype
                )

            with self._stats_lock:
                global_stats = self._stats.copy()

            return {
                'time_range': f"最近 {hours} 小时",
                'total_recent': len(recent_notifications),
                'level_distribution': level_stats,
                'type_distribution': type_stats,
                'global_stats': global_stats
            }

        except Exception as e:
            logger.error(f"获取通知统计失败: {e}")
            return {}

    def cleanup_old_notifications(self, days: int = 7) -> int:
        """清理旧通知"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)

            with self._notifications_lock:
                original_count = len(self._notifications)
                self._notifications = [
                    n for n in self._notifications
                    if n.timestamp > cutoff_time
                ]
                cleaned_count = original_count - len(self._notifications)

            logger.info(f"清理旧通知: {cleaned_count} 个，保留 {len(self._notifications)} 个")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理旧通知失败: {e}")
            return 0

    def stop_service(self):
        """停止服务"""
        try:
            # 停止推送线程
            self._push_stop_event.set()
            if self._push_thread and self._push_thread.is_alive():
                self._push_thread.join(timeout=5)

            # 关闭线程池
            self._executor.shutdown(wait=True)

            logger.info("通知服务已停止")

        except Exception as e:
            logger.error(f"停止通知服务失败: {e}")

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        with self._notifications_lock:
            total_notifications = len(self._notifications)
            unread_notifications = sum(1 for n in self._notifications if not n.read)
        
        with self._stats_lock:
            global_stats = self._stats.copy()

        with self._filters_lock:
            filter_count = len(self._filters)

        return {
            'service_name': 'NotificationService',
            'enabled_channels': self.enabled_channels,
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
            'max_notifications': self._max_notifications,
            'subscribers': {channel: len(callbacks) for channel, callbacks in self._subscribers.items()},
            'filters_count': filter_count,
            'push_queue_size': self._push_queue.qsize(),
            'push_thread_alive': self._push_thread.is_alive() if self._push_thread else False,
            'executor_threads': self._executor._max_workers,
            'global_stats': global_stats,
            'status': 'ready'
        }


# 全局通知服务实例
notification_service = NotificationService()


def get_notification_service() -> NotificationService:
    """获取通知服务实例"""
    return notification_service
