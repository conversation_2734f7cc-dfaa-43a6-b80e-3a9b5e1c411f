# -*- coding: utf-8 -*-
"""
数据库连接和管理模块
实现SQLite数据库连接池、事务管理和基础CRUD操作

Author: SuperBot Team
Date: 2025-01-04
"""

import sqlite3
import threading
import logging
import os
from pathlib import Path
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Union
import time
from enum import Enum

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """查询类型枚举"""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"


class QueryBuilder:
    """SQL查询构建器"""

    def __init__(self, table_name: str):
        """
        初始化查询构建器

        Args:
            table_name: 表名
        """
        self.table_name = table_name
        self.query_type = None
        self.select_fields = []
        self.where_conditions = []
        self.order_by_fields = []
        self.group_by_fields = []
        self.having_conditions = []
        self.limit_count = None
        self.offset_count = None
        self.join_clauses = []
        self.insert_data = {}
        self.update_data = {}
        self.params = []

    def select(self, *fields) -> 'QueryBuilder':
        """选择字段"""
        self.query_type = QueryType.SELECT
        self.select_fields = list(fields) if fields else ['*']
        return self

    def where(self, condition: str, *params) -> 'QueryBuilder':
        """添加WHERE条件"""
        self.where_conditions.append(condition)
        self.params.extend(params)
        return self

    def order_by(self, field: str, direction: str = 'ASC') -> 'QueryBuilder':
        """添加ORDER BY"""
        self.order_by_fields.append(f"{field} {direction}")
        return self

    def limit(self, count: int, offset: int = 0) -> 'QueryBuilder':
        """添加LIMIT"""
        self.limit_count = count
        self.offset_count = offset
        return self

    def insert(self, data: Dict[str, Any]) -> 'QueryBuilder':
        """插入数据"""
        self.query_type = QueryType.INSERT
        self.insert_data = data
        return self

    def update(self, data: Dict[str, Any]) -> 'QueryBuilder':
        """更新数据"""
        self.query_type = QueryType.UPDATE
        self.update_data = data
        return self

    def delete(self) -> 'QueryBuilder':
        """删除数据"""
        self.query_type = QueryType.DELETE
        return self

    def build(self) -> Tuple[str, List[Any]]:
        """构建SQL查询"""
        if self.query_type == QueryType.SELECT:
            return self._build_select()
        elif self.query_type == QueryType.INSERT:
            return self._build_insert()
        elif self.query_type == QueryType.UPDATE:
            return self._build_update()
        elif self.query_type == QueryType.DELETE:
            return self._build_delete()
        else:
            raise ValueError("未指定查询类型")

    def _build_select(self) -> Tuple[str, List[Any]]:
        """构建SELECT查询"""
        fields = ', '.join(self.select_fields)
        query = f"SELECT {fields} FROM {self.table_name}"

        if self.where_conditions:
            query += " WHERE " + " AND ".join(self.where_conditions)

        if self.order_by_fields:
            query += " ORDER BY " + ", ".join(self.order_by_fields)

        if self.limit_count:
            query += f" LIMIT {self.limit_count}"
            if self.offset_count:
                query += f" OFFSET {self.offset_count}"

        return query, self.params

    def _build_insert(self) -> Tuple[str, List[Any]]:
        """构建INSERT查询"""
        fields = list(self.insert_data.keys())
        placeholders = ['?' for _ in fields]
        values = [self.insert_data[field] for field in fields]

        query = f"INSERT INTO {self.table_name} ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
        return query, values

    def _build_update(self) -> Tuple[str, List[Any]]:
        """构建UPDATE查询"""
        set_clauses = [f"{field} = ?" for field in self.update_data.keys()]
        values = list(self.update_data.values())

        query = f"UPDATE {self.table_name} SET {', '.join(set_clauses)}"

        if self.where_conditions:
            query += " WHERE " + " AND ".join(self.where_conditions)
            values.extend(self.params)

        return query, values

    def _build_delete(self) -> Tuple[str, List[Any]]:
        """构建DELETE查询"""
        query = f"DELETE FROM {self.table_name}"

        if self.where_conditions:
            query += " WHERE " + " AND ".join(self.where_conditions)

        return query, self.params


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/database/superbot.db", pool_size: int = 10):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
            pool_size: 连接池大小
        """
        self.db_path = Path(db_path)
        self.pool_size = pool_size
        self._connections = []
        self._lock = threading.Lock()
        self._local = threading.local()
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._initialize_database()
        
        logger.info(f"数据库管理器初始化完成: {self.db_path}")
    
    def _initialize_database(self):
        """初始化数据库结构"""
        try:
            # 读取初始化SQL脚本
            init_sql_path = Path(__file__).parent / "migrations" / "init_db.sql"
            
            if not init_sql_path.exists():
                raise FileNotFoundError(f"初始化SQL脚本不存在: {init_sql_path}")
            
            with open(init_sql_path, 'r', encoding='utf-8') as f:
                init_sql = f.read()
            
            # 执行初始化脚本
            with self.get_connection() as conn:
                conn.executescript(init_sql)
                conn.commit()
                
            logger.info("数据库结构初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        # 检查线程本地存储中是否已有连接
        if hasattr(self._local, 'connection') and self._local.connection:
            return self._local.connection
        
        # 创建新连接
        conn = sqlite3.connect(
            str(self.db_path),
            check_same_thread=False,
            timeout=30.0
        )
        
        # 配置连接
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        
        # 存储到线程本地存储
        self._local.connection = conn
        
        return conn
    
    def close_connection(self):
        """关闭当前线程的数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None
    
    @contextmanager
    def transaction(self):
        """
        事务上下文管理器
        
        Usage:
            with db_manager.transaction():
                # 数据库操作
                pass
        """
        conn = self.get_connection()
        try:
            yield conn
            conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"事务回滚: {e}")
            raise
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[sqlite3.Row]: 查询结果
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.fetchall()
            
        except Exception as e:
            logger.error(f"查询执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
        finally:
            cursor.close()
    
    def execute_update(self, query: str, params: Optional[Tuple] = None) -> int:
        """
        执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
            
        Returns:
            int: 受影响的行数
        """
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                return cursor.rowcount
                
            except Exception as e:
                logger.error(f"更新执行失败: {query}, 参数: {params}, 错误: {e}")
                raise
            finally:
                cursor.close()
    
    def execute_insert(self, query: str, params: Optional[Tuple] = None) -> int:
        """
        执行插入语句
        
        Args:
            query: SQL插入语句
            params: 插入参数
            
        Returns:
            int: 新插入记录的ID
        """
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                return cursor.lastrowid
                
            except Exception as e:
                logger.error(f"插入执行失败: {query}, 参数: {params}, 错误: {e}")
                raise
            finally:
                cursor.close()
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 备份是否成功
        """
        try:
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建备份连接
            source_conn = self.get_connection()
            backup_conn = sqlite3.connect(str(backup_path))
            
            # 执行备份
            source_conn.backup(backup_conn)
            
            backup_conn.close()
            
            logger.info(f"数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """
        获取表结构信息

        Args:
            table_name: 表名

        Returns:
            List[sqlite3.Row]: 表结构信息
        """
        # PRAGMA语句不支持参数绑定，需要直接拼接
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def get_database_size(self) -> int:
        """
        获取数据库文件大小（字节）
        
        Returns:
            int: 数据库文件大小
        """
        if self.db_path.exists():
            return self.db_path.stat().st_size
        return 0
    
    def vacuum_database(self):
        """清理数据库，回收空间"""
        try:
            conn = self.get_connection()
            conn.execute("VACUUM")
            conn.commit()
            logger.info("数据库清理完成")
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")
            raise

    def query_builder(self, table_name: str) -> QueryBuilder:
        """创建查询构建器"""
        return QueryBuilder(table_name)

    def execute_builder_query(self, builder: QueryBuilder) -> List[sqlite3.Row]:
        """执行查询构建器生成的查询"""
        query, params = builder.build()

        if builder.query_type == QueryType.SELECT:
            return self.execute_query(query, tuple(params) if params else None)
        else:
            raise ValueError("此方法仅支持SELECT查询")

    def execute_builder_update(self, builder: QueryBuilder) -> int:
        """执行查询构建器生成的更新语句"""
        query, params = builder.build()

        if builder.query_type in [QueryType.UPDATE, QueryType.DELETE]:
            return self.execute_update(query, tuple(params) if params else None)
        else:
            raise ValueError("此方法仅支持UPDATE和DELETE查询")

    def execute_builder_insert(self, builder: QueryBuilder) -> int:
        """执行查询构建器生成的插入语句"""
        query, params = builder.build()

        if builder.query_type == QueryType.INSERT:
            return self.execute_insert(query, tuple(params) if params else None)
        else:
            raise ValueError("此方法仅支持INSERT查询")

    def batch_insert(self, table_name: str, data_list: List[Dict[str, Any]]) -> List[int]:
        """批量插入数据"""
        if not data_list:
            return []

        # 获取字段名（假设所有记录有相同字段）
        fields = list(data_list[0].keys())
        placeholders = ['?' for _ in fields]

        query = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"

        inserted_ids = []

        with self.transaction() as conn:
            cursor = conn.cursor()

            try:
                for data in data_list:
                    values = [data[field] for field in fields]
                    cursor.execute(query, values)
                    inserted_ids.append(cursor.lastrowid)

                logger.info(f"批量插入 {len(data_list)} 条记录到 {table_name}")
                return inserted_ids

            except Exception as e:
                logger.error(f"批量插入失败: {e}")
                raise
            finally:
                cursor.close()

    def batch_update(self, table_name: str, updates: List[Dict[str, Any]],
                    where_field: str) -> int:
        """批量更新数据"""
        if not updates:
            return 0

        total_updated = 0

        with self.transaction() as conn:
            cursor = conn.cursor()

            try:
                for update_data in updates:
                    # 分离WHERE条件和更新数据
                    where_value = update_data.pop(where_field)

                    if not update_data:
                        continue

                    # 构建UPDATE语句
                    set_clauses = [f"{field} = ?" for field in update_data.keys()]
                    values = list(update_data.values()) + [where_value]

                    query = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {where_field} = ?"

                    cursor.execute(query, values)
                    total_updated += cursor.rowcount

                logger.info(f"批量更新 {total_updated} 条记录在 {table_name}")
                return total_updated

            except Exception as e:
                logger.error(f"批量更新失败: {e}")
                raise
            finally:
                cursor.close()

    def execute_raw_query(self, query: str, params: Optional[Tuple] = None) -> List[sqlite3.Row]:
        """执行原始SQL查询（仅用于复杂查询）"""
        logger.warning(f"执行原始SQL查询: {query[:100]}...")
        return self.execute_query(query, params)

    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构信息"""
        try:
            # 获取表信息
            table_info = self.get_table_info(table_name)

            # 获取索引信息
            indexes = self.execute_query(f"PRAGMA index_list({table_name})")

            # 获取外键信息
            foreign_keys = self.execute_query(f"PRAGMA foreign_key_list({table_name})")

            return {
                'columns': [dict(col) for col in table_info],
                'indexes': [dict(idx) for idx in indexes],
                'foreign_keys': [dict(fk) for fk in foreign_keys]
            }

        except Exception as e:
            logger.error(f"获取表结构失败: {table_name}, 错误: {e}")
            return {}

    def analyze_query_performance(self, query: str, params: Optional[Tuple] = None) -> Dict[str, Any]:
        """分析查询性能"""
        try:
            # 使用EXPLAIN QUERY PLAN分析查询
            explain_query = f"EXPLAIN QUERY PLAN {query}"
            plan = self.execute_query(explain_query, params)

            # 测量执行时间
            start_time = time.time()
            result = self.execute_query(query, params)
            execution_time = time.time() - start_time

            return {
                'execution_time': execution_time,
                'result_count': len(result),
                'query_plan': [dict(row) for row in plan]
            }

        except Exception as e:
            logger.error(f"查询性能分析失败: {e}")
            return {'error': str(e)}

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            'pool_size': self.pool_size,
            'active_connections': len(self._connections),
            'database_path': str(self.db_path),
            'database_size': self.get_database_size(),
            'has_local_connection': hasattr(self._local, 'connection') and self._local.connection is not None
        }


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager
