# -*- coding: utf-8 -*-
"""
AI引擎模块
包含AI引擎基类和具体实现

Author: SuperBot Team
Date: 2025-01-04
"""

from .base_engine import (
    BaseAIEngine,
    EngineType,
    AnalysisResult,
    AIAnalysisRequest,
    AIAnalysisResponse,
    AIEngineError
)

from .open_engine import (
    AIOpenEngine,
    OpenSignal,
    get_ai_open_engine
)

from .hold_engine import (
    AIHoldEngine,
    PositionInfo,
    HoldSignal,
    get_ai_hold_engine
)

from .prompts import (
    PromptTemplate,
    AIPromptTemplates,
    TechnicalIndicatorFormatter,
    get_prompt_templates,
    format_technical_indicators,
    format_market_summary
)

__all__ = [
    # 基类和枚举
    'BaseAIEngine',
    'EngineType',
    'AnalysisResult',

    # 数据结构
    'AIAnalysisRequest',
    'AIAnalysisResponse',

    # 开仓引擎
    'AIOpenEngine',
    'OpenSignal',
    'get_ai_open_engine',

    # 持仓引擎
    'AIHoldEngine',
    'PositionInfo',
    'HoldSignal',
    'get_ai_hold_engine',

    # 提示词模板
    'PromptTemplate',
    'AIPromptTemplates',
    'TechnicalIndicatorFormatter',
    'get_prompt_templates',
    'format_technical_indicators',
    'format_market_summary',

    # 异常
    'AIEngineError'
]
"""
AI引擎模块
包含AI开仓引擎、AI持仓引擎和提示词模板

Author: SuperBot Team
Date: 2025-01-04
"""
