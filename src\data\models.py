# -*- coding: utf-8 -*-
"""
数据模型定义模块
定义所有数据库表对应的数据模型类

Author: SuperBot Team
Date: 2025-01-04
"""

import json
import sqlite3
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field, asdict
from abc import ABC, abstractmethod


class BaseModel(ABC):
    """数据模型基类"""
    
    def __init__(self, **kwargs):
        """初始化模型"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        if hasattr(self, '__dataclass_fields__'):
            return asdict(self)
        else:
            return {key: value for key, value in self.__dict__.items() 
                   if not key.startswith('_')}
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = self.to_dict()
        # 处理datetime对象
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建实例"""
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str):
        """从JSON字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    @classmethod
    def from_db_row(cls, row: sqlite3.Row):
        """从数据库行创建实例"""
        data = dict(row)
        return cls.from_dict(data)
    
    def validate(self) -> List[str]:
        """验证数据模型，返回错误列表"""
        errors = []
        # 子类可以重写此方法添加具体验证逻辑
        return errors
    
    def is_valid(self) -> bool:
        """检查数据模型是否有效"""
        return len(self.validate()) == 0
    
    def __repr__(self) -> str:
        """字符串表示"""
        class_name = self.__class__.__name__
        attrs = []
        for key, value in self.to_dict().items():
            if isinstance(value, str) and len(value) > 50:
                value = value[:47] + "..."
            attrs.append(f"{key}={repr(value)}")
        return f"{class_name}({', '.join(attrs)})"
    
    def __eq__(self, other) -> bool:
        """相等比较"""
        if not isinstance(other, self.__class__):
            return False
        return self.to_dict() == other.to_dict()


@dataclass
class SystemConfig(BaseModel):
    """系统配置模型"""
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def validate(self) -> List[str]:
        """验证系统配置"""
        errors = []
        
        if not self.key:
            errors.append("配置键不能为空")
        elif len(self.key) > 100:
            errors.append("配置键长度不能超过100字符")
        
        if not self.value:
            errors.append("配置值不能为空")
        elif len(self.value) > 1000:
            errors.append("配置值长度不能超过1000字符")
        
        if self.description and len(self.description) > 500:
            errors.append("配置描述长度不能超过500字符")
        
        return errors
    
    def get_typed_value(self) -> Any:
        """获取类型转换后的配置值"""
        value = self.value
        
        # 布尔值转换
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 整数转换
        try:
            return int(value)
        except ValueError:
            pass
        
        # 浮点数转换
        try:
            return float(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value


@dataclass
class TradingParams(BaseModel):
    """交易参数模型"""
    id: Optional[int] = None
    max_leverage: int = 10
    max_position_ratio: float = 0.1
    stop_loss_ratio: float = 0.05
    take_profit_ratio: float = 0.1
    confidence_threshold: float = 70.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def validate(self) -> List[str]:
        """验证交易参数"""
        errors = []
        
        if not (1 <= self.max_leverage <= 100):
            errors.append("最大杠杆必须在1-100之间")
        
        if not (0.0 < self.max_position_ratio <= 1.0):
            errors.append("最大仓位比例必须在0-1之间")
        
        if not (0.0 < self.stop_loss_ratio <= 1.0):
            errors.append("止损比例必须在0-1之间")
        
        if not (0.0 < self.take_profit_ratio <= 1.0):
            errors.append("止盈比例必须在0-1之间")
        
        if not (0.0 <= self.confidence_threshold <= 100.0):
            errors.append("置信度阈值必须在0-100之间")
        
        return errors
    
    def calculate_position_size(self, balance: float) -> float:
        """计算仓位大小"""
        return balance * self.max_position_ratio
    
    def calculate_stop_loss_price(self, entry_price: float, side: str) -> float:
        """计算止损价格"""
        if side.lower() == 'long':
            return entry_price * (1 - self.stop_loss_ratio)
        else:  # short
            return entry_price * (1 + self.stop_loss_ratio)
    
    def calculate_take_profit_price(self, entry_price: float, side: str) -> float:
        """计算止盈价格"""
        if side.lower() == 'long':
            return entry_price * (1 + self.take_profit_ratio)
        else:  # short
            return entry_price * (1 - self.take_profit_ratio)


@dataclass
class ApiKeys(BaseModel):
    """API密钥模型"""
    id: Optional[int] = None
    exchange: str = ""
    api_key: str = ""
    secret_key: str = ""
    passphrase: Optional[str] = None
    is_sandbox: bool = True
    is_active: bool = True
    created_at: Optional[datetime] = None
    
    def validate(self) -> List[str]:
        """验证API密钥"""
        errors = []
        
        if not self.exchange:
            errors.append("交易所名称不能为空")
        elif self.exchange not in ['okx', 'deepseek', 'test_exchange']:
            errors.append("不支持的交易所类型")
        
        if not self.api_key:
            errors.append("API密钥不能为空")
        elif len(self.api_key) < 10:
            errors.append("API密钥长度过短")
        
        if self.exchange == 'okx':
            if not self.secret_key:
                errors.append("OKX交易所需要Secret密钥")
            if not self.passphrase:
                errors.append("OKX交易所需要Passphrase")
        
        return errors
    
    def mask_sensitive_data(self) -> Dict[str, str]:
        """掩码敏感数据"""
        def mask_key(key: str) -> str:
            if len(key) <= 8:
                return '*' * len(key)
            return key[:4] + '*' * (len(key) - 8) + key[-4:]
        
        return {
            'exchange': self.exchange,
            'api_key': mask_key(self.api_key),
            'secret_key': mask_key(self.secret_key) if self.secret_key else '',
            'passphrase': mask_key(self.passphrase) if self.passphrase else '',
            'is_sandbox': self.is_sandbox,
            'is_active': self.is_active
        }
    
    def to_ccxt_config(self) -> Dict[str, Any]:
        """转换为CCXT配置格式"""
        config = {
            'apiKey': self.api_key,
            'secret': self.secret_key,
            'sandbox': self.is_sandbox,
            'enableRateLimit': True,
        }
        
        if self.passphrase:
            config['password'] = self.passphrase
        
        return config


@dataclass
class TradingPairs(BaseModel):
    """交易对模型"""
    id: Optional[int] = None
    symbol: str = ""
    is_active: bool = True
    min_order_size: Optional[float] = None
    tick_size: Optional[float] = None
    created_at: Optional[datetime] = None
    
    def validate(self) -> List[str]:
        """验证交易对"""
        errors = []
        
        if not self.symbol:
            errors.append("交易对符号不能为空")
        elif not self._is_valid_symbol_format():
            errors.append("交易对符号格式不正确")
        
        if self.min_order_size is not None and self.min_order_size <= 0:
            errors.append("最小订单量必须大于0")
        
        if self.tick_size is not None and self.tick_size <= 0:
            errors.append("价格精度必须大于0")
        
        return errors
    
    def _is_valid_symbol_format(self) -> bool:
        """检查交易对符号格式是否正确"""
        # 支持格式：BTC/USDT:USDT, ETH/USDT:USDT 等
        parts = self.symbol.split(':')
        if len(parts) != 2:
            return False
        
        base_quote = parts[0].split('/')
        if len(base_quote) != 2:
            return False
        
        return all(part.isalpha() and part.isupper() for part in base_quote + [parts[1]])
    
    def get_base_currency(self) -> str:
        """获取基础货币"""
        return self.symbol.split('/')[0]
    
    def get_quote_currency(self) -> str:
        """获取计价货币"""
        return self.symbol.split('/')[1].split(':')[0]
    
    def get_settle_currency(self) -> str:
        """获取结算货币"""
        return self.symbol.split(':')[1]
    
    def format_price(self, price: float) -> float:
        """格式化价格"""
        if self.tick_size:
            return round(price / self.tick_size) * self.tick_size
        return price
    
    def format_size(self, size: float) -> float:
        """格式化数量"""
        if self.min_order_size:
            # 确保数量是最小订单量的整数倍
            multiplier = round(size / self.min_order_size)
            return max(multiplier * self.min_order_size, self.min_order_size)
        return size


@dataclass
class SystemLogs(BaseModel):
    """系统日志模型"""
    id: Optional[int] = None
    level: str = "INFO"
    module: str = ""
    message: str = ""
    details: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def validate(self) -> List[str]:
        """验证系统日志"""
        errors = []
        
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.level not in valid_levels:
            errors.append(f"日志级别必须是以下之一: {', '.join(valid_levels)}")
        
        if not self.module:
            errors.append("模块名称不能为空")
        elif len(self.module) > 100:
            errors.append("模块名称长度不能超过100字符")
        
        if not self.message:
            errors.append("日志消息不能为空")
        elif len(self.message) > 1000:
            errors.append("日志消息长度不能超过1000字符")
        
        if self.details and len(self.details) > 5000:
            errors.append("日志详情长度不能超过5000字符")
        
        return errors
    
    def get_severity_level(self) -> int:
        """获取日志严重程度级别"""
        level_map = {
            'DEBUG': 10,
            'INFO': 20,
            'WARNING': 30,
            'ERROR': 40,
            'CRITICAL': 50
        }
        return level_map.get(self.level, 20)
    
    def is_error_level(self) -> bool:
        """是否为错误级别日志"""
        return self.level in ['ERROR', 'CRITICAL']
    
    def get_details_dict(self) -> Optional[Dict[str, Any]]:
        """获取详情字典"""
        if not self.details:
            return None
        
        try:
            return json.loads(self.details)
        except json.JSONDecodeError:
            return {'raw_details': self.details}


# 模型注册表
MODEL_REGISTRY = {
    'system_config': SystemConfig,
    'trading_params': TradingParams,
    'api_keys': ApiKeys,
    'trading_pairs': TradingPairs,
    'system_logs': SystemLogs,
}


def get_model_class(table_name: str) -> Optional[type]:
    """根据表名获取模型类"""
    return MODEL_REGISTRY.get(table_name)


def create_model_instance(table_name: str, data: Dict[str, Any]) -> Optional[BaseModel]:
    """创建模型实例"""
    model_class = get_model_class(table_name)
    if model_class:
        return model_class.from_dict(data)
    return None
