# DeepSeek加密货币永续合约全自动量化交易系统

## 项目概述

本系统是一个基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，通过欧易交易所进行保证金交易。系统采用Python开发，使用ccxt获取交易数据，ta-lib进行技术分析，pywebview提供人性化界面。

## 核心特性

- 🤖 **AI驱动决策**：集成DeepSeek AI模型进行智能交易决策
- 📊 **多时间周期分析**：支持1分钟、5分钟、15分钟、1小时技术分析
- 🔄 **全自动交易**：支持自动开仓、持仓管理和平仓
- 🛡️ **风险管理**：多层次风险控制和资金保护
- 💻 **桌面应用**：基于PyWebview的跨平台桌面界面
- 🔒 **安全可靠**：API密钥加密存储，数据安全保护

## 技术架构

- **开发语言**：Python 3.8+
- **交易所接口**：CCXT (欧易交易所)
- **技术分析**：TA-Lib
- **AI模型**：DeepSeek API
- **用户界面**：PyWebview + HTML/CSS/JS
- **数据库**：SQLite3
- **任务调度**：APScheduler

## 系统要求

- Python 3.8 或更高版本
- Windows/macOS/Linux 操作系统
- 网络连接（用于API调用）
- 最低 4GB RAM
- 最低 1GB 可用磁盘空间

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境
复制 `.env.example` 到 `.env` 并配置您的API密钥。

### 3. 初始化数据库
```bash
python scripts/setup_database.py
```

### 4. 启动应用
```bash
python main.py
```

## 项目结构

```
superbot/
├── src/                    # 源代码目录
│   ├── ui/                 # 用户界面层
│   ├── core/               # 核心业务逻辑
│   ├── services/           # 服务层
│   ├── data/               # 数据访问层
│   ├── utils/              # 工具模块
│   └── constants/          # 常量定义
├── tests/                  # 测试代码
├── docs/                   # 文档目录
├── config/                 # 配置文件
├── data/                   # 数据文件
└── scripts/                # 脚本目录
```

## 安全声明

⚠️ **重要提醒**：
- 本系统仅供学习和研究使用
- 请在模拟盘环境中充分测试后再考虑实盘使用
- 量化交易存在风险，请谨慎使用
- 作者不承担任何交易损失责任

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进本项目。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
