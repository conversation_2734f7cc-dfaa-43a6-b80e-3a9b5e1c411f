# -*- coding: utf-8 -*-
"""
市场数据服务
获取和处理市场数据，计算技术指标

Author: SuperBot Team
Date: 2025-01-04
"""

import numpy as np
import talib
import logging
import time
import threading
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from threading import Lock, Thread
from concurrent.futures import ThreadPoolExecutor, as_completed
import asyncio

from src.services.exchange_service import get_exchange_service
from src.data.repositories.trading_repository import get_trading_pairs_repository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class MarketDataError(Exception):
    """市场数据错误"""
    pass


class MarketDataService:
    """市场数据服务类"""
    
    def __init__(self):
        """初始化市场数据服务"""
        self.exchange_service = get_exchange_service()
        self.trading_pairs_repo = get_trading_pairs_repository()
        
        # 缓存相关
        self._data_cache: Dict[str, Dict] = {}
        self._cache_lock = Lock()
        self._cache_ttl = 30  # 缓存30秒
        
        # 支持的时间周期
        self.supported_timeframes = ['1m', '5m', '15m', '1h']

        # 并发处理
        self._executor = ThreadPoolExecutor(max_workers=8)

        # 数据验证
        self._min_data_points = 50  # 最少数据点
        self._max_data_points = 1000  # 最多数据点

        # 技术指标默认参数
        self.indicator_params = {
            'sma_periods': [5, 10, 20, 50],
            'ema_periods': [5, 10, 20, 50],
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'stoch_k': 14,
            'stoch_d': 3,
            'atr_period': 14,
            'adx_period': 14,
            'cci_period': 14,
            'willr_period': 14
        }
        
        logger.info("市场数据服务初始化完成")
    
    def _get_cache_key(self, exchange: str, symbol: str, timeframe: str) -> str:
        """生成缓存键"""
        return f"{exchange}:{symbol}:{timeframe}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get('timestamp', 0)
        return time.time() - cache_time < self._cache_ttl
    
    def _set_cache(self, cache_key: str, data: Dict):
        """设置缓存"""
        with self._cache_lock:
            self._data_cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }
    
    def _get_cache(self, cache_key: str) -> Optional[Dict]:
        """获取缓存"""
        with self._cache_lock:
            cache_entry = self._data_cache.get(cache_key)
            if self._is_cache_valid(cache_entry):
                return cache_entry['data']
        return None

    def _validate_ohlcv_data(self, ohlcv_data: List[List]) -> bool:
        """验证OHLCV数据"""
        try:
            if not ohlcv_data or len(ohlcv_data) < 2:
                return False

            for candle in ohlcv_data:
                if len(candle) != 6:  # [timestamp, open, high, low, close, volume]
                    return False

                timestamp, open_price, high, low, close, volume = candle

                # 验证价格数据
                if not all(isinstance(x, (int, float)) and x > 0 for x in [open_price, high, low, close]):
                    return False

                # 验证价格逻辑
                if not (low <= open_price <= high and low <= close <= high):
                    return False

                # 验证成交量
                if not isinstance(volume, (int, float)) or volume < 0:
                    return False

                # 验证时间戳
                if not isinstance(timestamp, (int, float)) or timestamp <= 0:
                    return False

            return True

        except Exception as e:
            logger.error(f"OHLCV数据验证失败: {e}")
            return False

    def _clean_ohlcv_data(self, ohlcv_data: List[List]) -> List[List]:
        """清理OHLCV数据"""
        try:
            cleaned_data = []

            for candle in ohlcv_data:
                if len(candle) == 6:
                    timestamp, open_price, high, low, close, volume = candle

                    # 转换数据类型
                    try:
                        cleaned_candle = [
                            int(timestamp),
                            float(open_price),
                            float(high),
                            float(low),
                            float(close),
                            float(volume)
                        ]
                        cleaned_data.append(cleaned_candle)
                    except (ValueError, TypeError):
                        logger.warning(f"跳过无效数据点: {candle}")
                        continue

            # 按时间戳排序
            cleaned_data.sort(key=lambda x: x[0])

            return cleaned_data

        except Exception as e:
            logger.error(f"OHLCV数据清理失败: {e}")
            return ohlcv_data

    def get_ohlcv_data(self, exchange: str, symbol: str, timeframe: str,
                       limit: int = 100, use_cache: bool = True) -> List[List]:
        """获取OHLCV数据"""
        try:
            cache_key = self._get_cache_key(exchange, symbol, timeframe)
            
            # 检查缓存
            if use_cache:
                cached_data = self._get_cache(cache_key)
                if cached_data:
                    logger.debug(f"使用缓存数据: {cache_key}")
                    return cached_data
            
            # 从交易所获取数据
            ohlcv_data = self.exchange_service.get_ohlcv(
                exchange, symbol, timeframe, limit
            )
            
            if not ohlcv_data:
                raise MarketDataError(f"未获取到 {symbol} 的 {timeframe} 数据")

            # 清理和验证数据
            cleaned_data = self._clean_ohlcv_data(ohlcv_data)

            if not self._validate_ohlcv_data(cleaned_data):
                raise MarketDataError(f"OHLCV数据验证失败: {symbol} {timeframe}")

            # 检查数据量
            if len(cleaned_data) < 10:
                raise MarketDataError(f"数据量不足: {symbol} {timeframe}, 仅 {len(cleaned_data)} 条")

            # 缓存数据
            if use_cache:
                self._set_cache(cache_key, cleaned_data)

            logger.debug(f"获取OHLCV数据成功: {symbol} {timeframe}, {len(cleaned_data)} 条")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"获取OHLCV数据失败: {symbol} {timeframe}, 错误: {e}")
            raise MarketDataError(f"获取OHLCV数据失败: {e}")
    
    def _prepare_price_arrays(self, ohlcv_data: List[List]) -> Tuple[np.ndarray, ...]:
        """准备价格数组"""
        try:
            if len(ohlcv_data) < 20:  # 至少需要20个数据点
                raise MarketDataError("数据点不足，无法计算技术指标")
            
            # 转换为numpy数组
            data_array = np.array(ohlcv_data)
            
            timestamps = data_array[:, 0]
            opens = data_array[:, 1].astype(float)
            highs = data_array[:, 2].astype(float)
            lows = data_array[:, 3].astype(float)
            closes = data_array[:, 4].astype(float)
            volumes = data_array[:, 5].astype(float)
            
            return timestamps, opens, highs, lows, closes, volumes
            
        except Exception as e:
            logger.error(f"准备价格数组失败: {e}")
            raise MarketDataError(f"准备价格数组失败: {e}")
    
    def calculate_trend_indicators(self, closes: np.ndarray) -> Dict[str, Any]:
        """计算趋势指标"""
        try:
            indicators = {}
            
            # 简单移动平均线
            for period in self.indicator_params['sma_periods']:
                if len(closes) >= period:
                    sma = talib.SMA(closes, timeperiod=period)
                    indicators[f'sma_{period}'] = {
                        'values': sma.tolist(),
                        'current': float(sma[-1]) if not np.isnan(sma[-1]) else None
                    }
            
            # 指数移动平均线
            for period in self.indicator_params['ema_periods']:
                if len(closes) >= period:
                    ema = talib.EMA(closes, timeperiod=period)
                    indicators[f'ema_{period}'] = {
                        'values': ema.tolist(),
                        'current': float(ema[-1]) if not np.isnan(ema[-1]) else None
                    }
            
            # MACD
            if len(closes) >= self.indicator_params['macd_slow']:
                macd, macd_signal, macd_hist = talib.MACD(
                    closes,
                    fastperiod=self.indicator_params['macd_fast'],
                    slowperiod=self.indicator_params['macd_slow'],
                    signalperiod=self.indicator_params['macd_signal']
                )
                
                indicators['macd'] = {
                    'macd': macd.tolist(),
                    'signal': macd_signal.tolist(),
                    'histogram': macd_hist.tolist(),
                    'current_macd': float(macd[-1]) if not np.isnan(macd[-1]) else None,
                    'current_signal': float(macd_signal[-1]) if not np.isnan(macd_signal[-1]) else None,
                    'current_histogram': float(macd_hist[-1]) if not np.isnan(macd_hist[-1]) else None
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算趋势指标失败: {e}")
            return {}
    
    def calculate_momentum_indicators(self, highs: np.ndarray, lows: np.ndarray, 
                                    closes: np.ndarray, volumes: np.ndarray) -> Dict[str, Any]:
        """计算动量指标"""
        try:
            indicators = {}
            
            # RSI
            if len(closes) >= self.indicator_params['rsi_period']:
                rsi = talib.RSI(closes, timeperiod=self.indicator_params['rsi_period'])
                indicators['rsi'] = {
                    'values': rsi.tolist(),
                    'current': float(rsi[-1]) if not np.isnan(rsi[-1]) else None
                }
            
            # 随机指标
            if len(closes) >= self.indicator_params['stoch_k']:
                slowk, slowd = talib.STOCH(
                    highs, lows, closes,
                    fastk_period=self.indicator_params['stoch_k'],
                    slowk_period=self.indicator_params['stoch_d'],
                    slowd_period=self.indicator_params['stoch_d']
                )
                
                indicators['stoch'] = {
                    'k': slowk.tolist(),
                    'd': slowd.tolist(),
                    'current_k': float(slowk[-1]) if not np.isnan(slowk[-1]) else None,
                    'current_d': float(slowd[-1]) if not np.isnan(slowd[-1]) else None
                }
            
            # CCI
            if len(closes) >= self.indicator_params['cci_period']:
                cci = talib.CCI(highs, lows, closes, timeperiod=self.indicator_params['cci_period'])
                indicators['cci'] = {
                    'values': cci.tolist(),
                    'current': float(cci[-1]) if not np.isnan(cci[-1]) else None
                }
            
            # Williams %R
            if len(closes) >= self.indicator_params['willr_period']:
                willr = talib.WILLR(highs, lows, closes, timeperiod=self.indicator_params['willr_period'])
                indicators['willr'] = {
                    'values': willr.tolist(),
                    'current': float(willr[-1]) if not np.isnan(willr[-1]) else None
                }
            
            # ADX
            if len(closes) >= self.indicator_params['adx_period']:
                adx = talib.ADX(highs, lows, closes, timeperiod=self.indicator_params['adx_period'])
                indicators['adx'] = {
                    'values': adx.tolist(),
                    'current': float(adx[-1]) if not np.isnan(adx[-1]) else None
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算动量指标失败: {e}")
            return {}
    
    def calculate_volatility_indicators(self, highs: np.ndarray, lows: np.ndarray, 
                                      closes: np.ndarray) -> Dict[str, Any]:
        """计算波动率指标"""
        try:
            indicators = {}
            
            # 布林带
            if len(closes) >= self.indicator_params['bb_period']:
                upper, middle, lower = talib.BBANDS(
                    closes,
                    timeperiod=self.indicator_params['bb_period'],
                    nbdevup=self.indicator_params['bb_std'],
                    nbdevdn=self.indicator_params['bb_std']
                )
                
                indicators['bbands'] = {
                    'upper': upper.tolist(),
                    'middle': middle.tolist(),
                    'lower': lower.tolist(),
                    'current_upper': float(upper[-1]) if not np.isnan(upper[-1]) else None,
                    'current_middle': float(middle[-1]) if not np.isnan(middle[-1]) else None,
                    'current_lower': float(lower[-1]) if not np.isnan(lower[-1]) else None
                }
            
            # ATR
            if len(closes) >= self.indicator_params['atr_period']:
                atr = talib.ATR(highs, lows, closes, timeperiod=self.indicator_params['atr_period'])
                indicators['atr'] = {
                    'values': atr.tolist(),
                    'current': float(atr[-1]) if not np.isnan(atr[-1]) else None
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算波动率指标失败: {e}")
            return {}
    
    def calculate_volume_indicators(self, highs: np.ndarray, lows: np.ndarray, 
                                  closes: np.ndarray, volumes: np.ndarray) -> Dict[str, Any]:
        """计算成交量指标"""
        try:
            indicators = {}
            
            # OBV
            if len(closes) >= 2:
                obv = talib.OBV(closes, volumes)
                indicators['obv'] = {
                    'values': obv.tolist(),
                    'current': float(obv[-1]) if not np.isnan(obv[-1]) else None
                }
            
            # AD Line
            if len(closes) >= 2:
                ad = talib.AD(highs, lows, closes, volumes)
                indicators['ad'] = {
                    'values': ad.tolist(),
                    'current': float(ad[-1]) if not np.isnan(ad[-1]) else None
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return {}
    
    def calculate_all_indicators(self, exchange: str, symbol: str, timeframe: str, 
                               limit: int = 100) -> Dict[str, Any]:
        """计算所有技术指标"""
        try:
            # 获取OHLCV数据
            ohlcv_data = self.get_ohlcv_data(exchange, symbol, timeframe, limit)
            
            # 准备价格数组
            timestamps, opens, highs, lows, closes, volumes = self._prepare_price_arrays(ohlcv_data)
            
            # 计算各类指标
            trend_indicators = self.calculate_trend_indicators(closes)
            momentum_indicators = self.calculate_momentum_indicators(highs, lows, closes, volumes)
            volatility_indicators = self.calculate_volatility_indicators(highs, lows, closes)
            volume_indicators = self.calculate_volume_indicators(highs, lows, closes, volumes)
            
            # 合并所有指标
            all_indicators = {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'data_points': len(ohlcv_data),
                'current_price': float(closes[-1]),
                'price_change': float(closes[-1] - closes[-2]) if len(closes) >= 2 else 0,
                'price_change_pct': float((closes[-1] - closes[-2]) / closes[-2] * 100) if len(closes) >= 2 else 0,
                'trend_indicators': trend_indicators,
                'momentum_indicators': momentum_indicators,
                'volatility_indicators': volatility_indicators,
                'volume_indicators': volume_indicators
            }
            
            logger.info(f"计算技术指标完成: {symbol} {timeframe}")
            return all_indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {symbol} {timeframe}, 错误: {e}")
            raise MarketDataError(f"计算技术指标失败: {e}")
    
    def get_multi_timeframe_analysis(self, exchange: str, symbol: str,
                                   concurrent: bool = True) -> Dict[str, Any]:
        """获取多时间周期分析"""
        try:
            analysis_result = {
                'symbol': symbol,
                'exchange': exchange,
                'timestamp': datetime.now().isoformat(),
                'timeframes': {}
            }

            if concurrent:
                # 并发处理多个时间周期
                future_to_timeframe = {}

                for timeframe in self.supported_timeframes:
                    future = self._executor.submit(
                        self.calculate_all_indicators,
                        exchange, symbol, timeframe
                    )
                    future_to_timeframe[future] = timeframe

                # 收集结果
                for future in as_completed(future_to_timeframe, timeout=60):
                    timeframe = future_to_timeframe[future]
                    try:
                        indicators = future.result()
                        analysis_result['timeframes'][timeframe] = indicators
                    except Exception as e:
                        logger.warning(f"获取 {timeframe} 数据失败: {e}")
                        analysis_result['timeframes'][timeframe] = {
                            'error': str(e)
                        }
            else:
                # 顺序处理
                for timeframe in self.supported_timeframes:
                    try:
                        indicators = self.calculate_all_indicators(exchange, symbol, timeframe)
                        analysis_result['timeframes'][timeframe] = indicators

                    except Exception as e:
                        logger.warning(f"获取 {timeframe} 数据失败: {e}")
                        analysis_result['timeframes'][timeframe] = {
                            'error': str(e)
                        }

            logger.info(f"多时间周期分析完成: {symbol}")
            return analysis_result

        except Exception as e:
            logger.error(f"多时间周期分析失败: {symbol}, 错误: {e}")
            raise MarketDataError(f"多时间周期分析失败: {e}")
    
    def get_market_summary(self, exchange: str, symbols: List[str] = None) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            if not symbols:
                # 获取活跃交易对
                active_pairs = self.trading_pairs_repo.get_active_pairs()
                symbols = [pair.symbol for pair in active_pairs[:10]]  # 限制10个
            
            market_summary = {
                'exchange': exchange,
                'timestamp': datetime.now().isoformat(),
                'symbols_count': len(symbols),
                'symbols': {}
            }
            
            for symbol in symbols:
                try:
                    # 获取1小时数据进行快速分析
                    indicators = self.calculate_all_indicators(exchange, symbol, '1h', limit=50)
                    
                    # 提取关键信息
                    market_summary['symbols'][symbol] = {
                        'current_price': indicators.get('current_price'),
                        'price_change_pct': indicators.get('price_change_pct'),
                        'rsi': indicators.get('momentum_indicators', {}).get('rsi', {}).get('current'),
                        'trend': self._determine_trend(indicators)
                    }
                    
                except Exception as e:
                    logger.warning(f"获取 {symbol} 市场概览失败: {e}")
                    market_summary['symbols'][symbol] = {'error': str(e)}
            
            logger.info(f"市场概览获取完成: {len(symbols)} 个交易对")
            return market_summary
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            raise MarketDataError(f"获取市场概览失败: {e}")
    
    def _determine_trend(self, indicators: Dict[str, Any]) -> str:
        """判断趋势方向"""
        try:
            trend_indicators = indicators.get('trend_indicators', {})
            momentum_indicators = indicators.get('momentum_indicators', {})
            
            # 简单趋势判断逻辑
            sma_20 = trend_indicators.get('sma_20', {}).get('current')
            sma_50 = trend_indicators.get('sma_50', {}).get('current')
            rsi = momentum_indicators.get('rsi', {}).get('current')
            current_price = indicators.get('current_price')
            
            if not all([sma_20, sma_50, rsi, current_price]):
                return 'unknown'
            
            # 多头趋势：价格在均线上方，RSI适中
            if current_price > sma_20 > sma_50 and 30 < rsi < 70:
                return 'bullish'
            
            # 空头趋势：价格在均线下方，RSI适中
            elif current_price < sma_20 < sma_50 and 30 < rsi < 70:
                return 'bearish'
            
            # 超买
            elif rsi > 70:
                return 'overbought'
            
            # 超卖
            elif rsi < 30:
                return 'oversold'
            
            else:
                return 'sideways'
                
        except Exception as e:
            logger.warning(f"判断趋势失败: {e}")
            return 'unknown'

    def get_multi_symbol_analysis(self, exchange: str, symbols: List[str],
                                timeframe: str = '1h') -> Dict[str, Any]:
        """批量获取多个交易对的分析"""
        try:
            start_time = time.time()
            results = {
                'exchange': exchange,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'symbols': {},
                'summary': {}
            }

            # 并发处理多个交易对
            future_to_symbol = {}

            for symbol in symbols:
                future = self._executor.submit(
                    self.calculate_all_indicators,
                    exchange, symbol, timeframe, 50
                )
                future_to_symbol[future] = symbol

            # 收集结果
            successful = 0
            failed = 0

            for future in as_completed(future_to_symbol, timeout=120):
                symbol = future_to_symbol[future]
                try:
                    indicators = future.result()
                    results['symbols'][symbol] = {
                        'indicators': indicators,
                        'trend': self._determine_trend(indicators),
                        'status': 'success'
                    }
                    successful += 1

                except Exception as e:
                    logger.warning(f"获取 {symbol} 分析失败: {e}")
                    results['symbols'][symbol] = {
                        'error': str(e),
                        'status': 'failed'
                    }
                    failed += 1

            # 统计信息
            processing_time = time.time() - start_time
            results['summary'] = {
                'total_symbols': len(symbols),
                'successful': successful,
                'failed': failed,
                'processing_time': processing_time,
                'avg_time_per_symbol': processing_time / len(symbols) if symbols else 0
            }

            logger.info(f"批量分析完成: {successful}/{len(symbols)} 成功, 耗时 {processing_time:.2f}s")
            return results

        except Exception as e:
            logger.error(f"批量分析失败: {e}")
            raise MarketDataError(f"批量分析失败: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self._cache_lock:
            cache_count = len(self._data_cache)

            # 计算缓存命中率（简化版）
            cache_stats = {
                'cache_entries': cache_count,
                'cache_ttl': self._cache_ttl,
                'max_cache_size': 1000  # 假设最大缓存大小
            }

        return {
            'service_name': 'MarketDataService',
            'supported_timeframes': self.supported_timeframes,
            'indicator_params': self.indicator_params,
            'cache_stats': cache_stats,
            'executor_threads': self._executor._max_workers,
            'min_data_points': self._min_data_points,
            'max_data_points': self._max_data_points,
            'status': 'ready'
        }

    def clear_cache(self):
        """清除缓存"""
        with self._cache_lock:
            self._data_cache.clear()
            logger.info("市场数据缓存已清除")
    
    def get_market_data(self, symbol: str, exchange: str = 'okx') -> Dict[str, Any]:
        """获取市场数据（用于UI显示）"""
        try:
            # 获取最新的ticker数据
            exchange_service = get_exchange_service()
            ticker = exchange_service.get_ticker(exchange, symbol)

            if not ticker:
                return {
                    'symbol': symbol,
                    'price': 0,
                    'change': 0,
                    'volume': 0,
                    'high': 0,
                    'low': 0,
                    'timestamp': datetime.now().isoformat()
                }

            # 计算24小时变化百分比
            change_percent = 0
            if ticker.get('percentage'):
                change_percent = ticker['percentage'] / 100
            elif ticker.get('change') and ticker.get('last'):
                change_percent = ticker['change'] / ticker['last']

            return {
                'symbol': symbol,
                'price': ticker.get('last', 0),
                'change': change_percent,
                'volume': ticker.get('baseVolume', 0),
                'high': ticker.get('high', 0),
                'low': ticker.get('low', 0),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取市场数据失败: {symbol}, 错误: {e}")
            # 返回默认数据而不是抛出异常
            return {
                'symbol': symbol,
                'price': 0,
                'change': 0,
                'volume': 0,
                'high': 0,
                'low': 0,
                'timestamp': datetime.now().isoformat()
            }

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return self.get_performance_stats()


# 全局市场数据服务实例
market_data_service = MarketDataService()


def get_market_data_service() -> MarketDataService:
    """获取市场数据服务实例"""
    return market_data_service
