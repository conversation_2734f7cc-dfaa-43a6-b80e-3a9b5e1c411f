# -*- coding: utf-8 -*-
"""
技术分析模块
包含技术指标计算、技术分析引擎和数据处理器

Author: SuperBot Team
Date: 2025-01-04
"""

from .indicators import (
    TechnicalIndicators,
    IndicatorResult,
    IndicatorType,
    IndicatorError,
    get_technical_indicators
)

from .technical_engine import (
    TechnicalAnalysisEngine,
    TechnicalAnalysisError,
    get_technical_analysis_engine
)

from .data_processor import (
    MarketDataProcessor,
    ProcessedData,
    DataProcessingError,
    get_market_data_processor
)

__all__ = [
    # 技术指标
    'TechnicalIndicators',
    'IndicatorResult',
    'IndicatorType',
    'IndicatorError',
    'get_technical_indicators',

    # 技术分析引擎
    'TechnicalAnalysisEngine',
    'TechnicalAnalysisError',
    'get_technical_analysis_engine',

    # 数据处理器
    'MarketDataProcessor',
    'ProcessedData',
    'DataProcessingError',
    'get_market_data_processor'
]
"""
技术分析模块
包含技术指标计算、数据处理器和技术分析引擎

Author: SuperBot Team
Date: 2025-01-04
"""
