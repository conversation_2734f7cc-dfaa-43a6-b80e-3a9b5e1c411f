# -*- coding: utf-8 -*-
"""
配置数据仓库
管理系统配置和交易参数

Author: SuperBot Team
Date: 2025-01-04
"""

import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from src.data.repositories.base_repository import BaseRepository
from src.data.models import SystemConfig, TradingParams

logger = logging.getLogger(__name__)


class ConfigRepository(BaseRepository):
    """配置数据仓库"""
    
    def __init__(self):
        """初始化配置仓库"""
        super().__init__("system_config", SystemConfig)
    
    def get_repository_name(self) -> str:
        """获取仓库名称"""
        return "ConfigRepository"
    
    def find_by_key(self, key: str, use_cache: bool = True) -> Optional[SystemConfig]:
        """根据配置键查找配置"""
        cache_key = self._generate_cache_key("find_by_key", key)
        
        if use_cache:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
        
        try:
            result = self.db.execute_query(
                "SELECT * FROM system_config WHERE key = ?",
                (key,)
            )
            
            if result:
                config = SystemConfig.from_db_row(result[0])
                if use_cache:
                    self._set_cache(cache_key, config)
                return config
            
            return None
            
        except Exception as e:
            logger.error(f"根据键查找配置失败: {e}")
            raise
    
    def find_by_pattern(self, pattern: str, use_cache: bool = True) -> List[SystemConfig]:
        """根据模式查找配置"""
        cache_key = self._generate_cache_key("find_by_pattern", pattern)
        
        if use_cache:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
        
        try:
            result = self.db.execute_query(
                "SELECT * FROM system_config WHERE key LIKE ? ORDER BY key",
                (pattern,)
            )
            
            configs = [SystemConfig.from_db_row(row) for row in result]
            
            if use_cache:
                self._set_cache(cache_key, configs, ttl=120)
            
            return configs
            
        except Exception as e:
            logger.error(f"根据模式查找配置失败: {e}")
            raise
    
    def get_config_value(self, key: str, default: Any = None, use_cache: bool = True) -> Any:
        """获取配置值（带类型转换）"""
        config = self.find_by_key(key, use_cache)
        if config:
            return config.get_typed_value()
        return default
    
    def set_config_value(self, key: str, value: Any, description: str = None) -> SystemConfig:
        """设置配置值"""
        try:
            # 查找现有配置
            existing_config = self.find_by_key(key, use_cache=False)
            
            if existing_config:
                # 更新现有配置
                existing_config.value = str(value)
                if description:
                    existing_config.description = description
                existing_config.updated_at = datetime.now()
                
                return self.save(existing_config)
            else:
                # 创建新配置
                new_config = SystemConfig(
                    key=key,
                    value=str(value),
                    description=description or f"配置项: {key}",
                    created_at=datetime.now()
                )
                
                return self.save(new_config)
                
        except Exception as e:
            logger.error(f"设置配置值失败: {key} = {value}, 错误: {e}")
            raise
    
    def delete_config(self, key: str) -> bool:
        """删除配置"""
        try:
            config = self.find_by_key(key, use_cache=False)
            if config:
                return self.delete_by_id(config.id)
            return False
            
        except Exception as e:
            logger.error(f"删除配置失败: {key}, 错误: {e}")
            raise
    
    def get_all_configs_dict(self, use_cache: bool = True) -> Dict[str, Any]:
        """获取所有配置的字典形式"""
        cache_key = self._generate_cache_key("get_all_configs_dict")
        
        if use_cache:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
        
        try:
            configs = self.find_all(use_cache=False)
            config_dict = {config.key: config.get_typed_value() for config in configs}
            
            if use_cache:
                self._set_cache(cache_key, config_dict, ttl=180)
            
            return config_dict
            
        except Exception as e:
            logger.error(f"获取所有配置字典失败: {e}")
            raise
    
    def batch_set_configs(self, configs: Dict[str, Any]) -> List[SystemConfig]:
        """批量设置配置"""
        try:
            saved_configs = []
            
            with self.db.transaction():
                for key, value in configs.items():
                    config = self.set_config_value(key, value)
                    saved_configs.append(config)
            
            logger.info(f"批量设置配置完成: {len(saved_configs)} 个配置")
            return saved_configs
            
        except Exception as e:
            logger.error(f"批量设置配置失败: {e}")
            raise
    
    def export_configs(self) -> Dict[str, Dict[str, Any]]:
        """导出所有配置"""
        try:
            configs = self.find_all(use_cache=False)
            
            export_data = {}
            for config in configs:
                export_data[config.key] = {
                    'value': config.value,
                    'description': config.description,
                    'created_at': config.created_at.isoformat() if config.created_at else None,
                    'updated_at': config.updated_at.isoformat() if config.updated_at else None
                }
            
            return export_data
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            raise

    def get_ai_engine_config(self, engine_type: str) -> Optional[Dict[str, Any]]:
        """获取AI引擎配置"""
        try:
            config_key = f"ai_engine_{engine_type}"
            config_value = self.get_config_value(config_key)

            if config_value:
                import json
                return json.loads(config_value)

            return None

        except Exception as e:
            logger.error(f"获取AI引擎配置失败: {engine_type}, 错误: {e}")
            return None

    def save_ai_engine_config(self, engine_type: str, config: Dict[str, Any]) -> bool:
        """保存AI引擎配置"""
        try:
            import json
            config_key = f"ai_engine_{engine_type}"
            config_value = json.dumps(config, ensure_ascii=False)

            self.set_config_value(config_key, config_value, f"AI引擎配置 - {engine_type}")
            logger.info(f"AI引擎配置已保存: {engine_type}")
            return True

        except Exception as e:
            logger.error(f"保存AI引擎配置失败: {engine_type}, 错误: {e}")
            return False


class TradingParamsRepository(BaseRepository):
    """交易参数仓库"""
    
    def __init__(self):
        """初始化交易参数仓库"""
        super().__init__("trading_params", TradingParams)
    
    def get_repository_name(self) -> str:
        """获取仓库名称"""
        return "TradingParamsRepository"
    
    def get_current_params(self, use_cache: bool = True) -> Optional[TradingParams]:
        """获取当前交易参数（通常只有一条记录）"""
        cache_key = self._generate_cache_key("get_current_params")
        
        if use_cache:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
        
        try:
            result = self.db.execute_query(
                "SELECT * FROM trading_params ORDER BY id DESC LIMIT 1"
            )
            
            if result:
                params = TradingParams.from_db_row(result[0])
                if use_cache:
                    self._set_cache(cache_key, params)
                return params
            
            return None
            
        except Exception as e:
            logger.error(f"获取当前交易参数失败: {e}")
            raise
    
    def update_params(self, **kwargs) -> TradingParams:
        """更新交易参数"""
        try:
            current_params = self.get_current_params(use_cache=False)
            
            if current_params:
                # 更新现有参数
                for key, value in kwargs.items():
                    if hasattr(current_params, key):
                        setattr(current_params, key, value)
                
                current_params.updated_at = datetime.now()
                return self.save(current_params)
            else:
                # 创建新参数
                new_params = TradingParams(**kwargs)
                new_params.created_at = datetime.now()
                return self.save(new_params)
                
        except Exception as e:
            logger.error(f"更新交易参数失败: {e}")
            raise
    
    def get_risk_settings(self) -> Dict[str, float]:
        """获取风险设置"""
        params = self.get_current_params()
        if params:
            return {
                'max_leverage': params.max_leverage,
                'max_position_ratio': params.max_position_ratio,
                'stop_loss_ratio': params.stop_loss_ratio,
                'take_profit_ratio': params.take_profit_ratio,
                'confidence_threshold': params.confidence_threshold
            }
        return {}
    
    def validate_trade_params(self, leverage: int, position_ratio: float) -> bool:
        """验证交易参数是否符合风险设置"""
        params = self.get_current_params()
        if not params:
            return False
        
        if leverage > params.max_leverage:
            logger.warning(f"杠杆超出限制: {leverage} > {params.max_leverage}")
            return False
        
        if position_ratio > params.max_position_ratio:
            logger.warning(f"仓位比例超出限制: {position_ratio} > {params.max_position_ratio}")
            return False
        
        return True


# 全局仓库实例
config_repository = ConfigRepository()
trading_params_repository = TradingParamsRepository()


def get_config_repository() -> ConfigRepository:
    """获取配置仓库实例"""
    return config_repository


def get_trading_params_repository() -> TradingParamsRepository:
    """获取交易参数仓库实例"""
    return trading_params_repository
