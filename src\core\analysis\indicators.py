# -*- coding: utf-8 -*-
"""
技术指标计算模块
使用TA-Lib实现趋势类、震荡类、成交量类和波动率类指标

Author: SuperBot Team
Date: 2025-01-04
"""

import numpy as np
import talib
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger

logger = get_logger(__name__)


class IndicatorType(Enum):
    """指标类型枚举"""
    TREND = "trend"
    MOMENTUM = "momentum"
    VOLUME = "volume"
    VOLATILITY = "volatility"


class IndicatorError(Exception):
    """技术指标计算错误"""
    pass


@dataclass
class IndicatorResult:
    """指标计算结果"""
    name: str
    type: IndicatorType
    values: np.ndarray
    current: Optional[float]
    parameters: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'name': self.name,
            'type': self.type.value,
            'values': self.values.tolist() if self.values is not None else None,
            'current': float(self.current) if self.current is not None and not np.isnan(self.current) else None,
            'parameters': self.parameters,
            'timestamp': self.timestamp.isoformat(),
            'is_valid': self.is_valid()
        }
    
    def is_valid(self) -> bool:
        """检查指标结果是否有效"""
        return (self.current is not None and 
                not np.isnan(self.current) and 
                np.isfinite(self.current))


class TechnicalIndicators:
    """技术指标计算器"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.min_periods = {
            'sma': 2,
            'ema': 2,
            'macd': 26,
            'adx': 14,
            'rsi': 14,
            'stoch': 14,
            'cci': 14,
            'willr': 14,
            'obv': 2,
            'ad': 1,
            'adosc': 10,
            'atr': 14,
            'natr': 14,
            'bbands': 20
        }
        
        logger.info("技术指标计算器初始化完成")
    
    def _validate_data(self, data: np.ndarray, min_periods: int, indicator_name: str) -> np.ndarray:
        """验证输入数据"""
        if data is None or len(data) == 0:
            raise IndicatorError(f"{indicator_name}: 输入数据为空")
        
        # 转换为numpy数组
        if not isinstance(data, np.ndarray):
            data = np.array(data, dtype=float)
        
        # 检查数据长度
        if len(data) < min_periods:
            raise IndicatorError(f"{indicator_name}: 数据长度不足，需要至少 {min_periods} 个数据点，实际 {len(data)} 个")
        
        # 检查数据有效性
        if np.all(np.isnan(data)):
            raise IndicatorError(f"{indicator_name}: 所有数据都是NaN")
        
        return data
    
    def _get_current_value(self, values: np.ndarray) -> Optional[float]:
        """获取当前值（最后一个有效值）"""
        if values is None or len(values) == 0:
            return None
        
        # 从后往前找第一个有效值
        for i in range(len(values) - 1, -1, -1):
            if not np.isnan(values[i]) and np.isfinite(values[i]):
                return float(values[i])
        
        return None
    
    # ==================== 趋势类指标 ====================
    
    def sma(self, close: np.ndarray, period: int = 20) -> IndicatorResult:
        """简单移动平均线 (Simple Moving Average)"""
        try:
            close = self._validate_data(close, period, f"SMA({period})")
            
            values = talib.SMA(close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"SMA_{period}",
                type=IndicatorType.TREND,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"SMA计算失败: {e}")
            raise IndicatorError(f"SMA计算失败: {e}")
    
    def ema(self, close: np.ndarray, period: int = 20) -> IndicatorResult:
        """指数移动平均线 (Exponential Moving Average)"""
        try:
            close = self._validate_data(close, period, f"EMA({period})")
            
            values = talib.EMA(close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"EMA_{period}",
                type=IndicatorType.TREND,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"EMA计算失败: {e}")
            raise IndicatorError(f"EMA计算失败: {e}")
    
    def macd(self, close: np.ndarray, fast_period: int = 12, 
             slow_period: int = 26, signal_period: int = 9) -> Dict[str, IndicatorResult]:
        """MACD指标 (Moving Average Convergence Divergence)"""
        try:
            min_periods = max(slow_period, signal_period)
            close = self._validate_data(close, min_periods, f"MACD({fast_period},{slow_period},{signal_period})")
            
            macd_line, macd_signal, macd_hist = talib.MACD(
                close, 
                fastperiod=fast_period,
                slowperiod=slow_period, 
                signalperiod=signal_period
            )
            
            return {
                'macd': IndicatorResult(
                    name="MACD_LINE",
                    type=IndicatorType.TREND,
                    values=macd_line,
                    current=self._get_current_value(macd_line),
                    parameters={'fast_period': fast_period, 'slow_period': slow_period},
                    timestamp=datetime.now()
                ),
                'signal': IndicatorResult(
                    name="MACD_SIGNAL",
                    type=IndicatorType.TREND,
                    values=macd_signal,
                    current=self._get_current_value(macd_signal),
                    parameters={'signal_period': signal_period},
                    timestamp=datetime.now()
                ),
                'histogram': IndicatorResult(
                    name="MACD_HISTOGRAM",
                    type=IndicatorType.TREND,
                    values=macd_hist,
                    current=self._get_current_value(macd_hist),
                    parameters={'fast_period': fast_period, 'slow_period': slow_period, 'signal_period': signal_period},
                    timestamp=datetime.now()
                )
            }
            
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            raise IndicatorError(f"MACD计算失败: {e}")
    
    def adx(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """平均趋向指数 (Average Directional Index)"""
        try:
            high = self._validate_data(high, period, f"ADX({period}) - HIGH")
            low = self._validate_data(low, period, f"ADX({period}) - LOW")
            close = self._validate_data(close, period, f"ADX({period}) - CLOSE")
            
            values = talib.ADX(high, low, close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"ADX_{period}",
                type=IndicatorType.TREND,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"ADX计算失败: {e}")
            raise IndicatorError(f"ADX计算失败: {e}")
    
    # ==================== 震荡类指标 ====================
    
    def rsi(self, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """相对强弱指数 (Relative Strength Index)"""
        try:
            close = self._validate_data(close, period + 1, f"RSI({period})")
            
            values = talib.RSI(close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"RSI_{period}",
                type=IndicatorType.MOMENTUM,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            raise IndicatorError(f"RSI计算失败: {e}")
    
    def stoch(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
              fastk_period: int = 14, slowk_period: int = 3, slowd_period: int = 3) -> Dict[str, IndicatorResult]:
        """随机指标 (Stochastic Oscillator)"""
        try:
            min_periods = max(fastk_period, slowk_period, slowd_period)
            high = self._validate_data(high, min_periods, f"STOCH({fastk_period},{slowk_period},{slowd_period}) - HIGH")
            low = self._validate_data(low, min_periods, f"STOCH({fastk_period},{slowk_period},{slowd_period}) - LOW")
            close = self._validate_data(close, min_periods, f"STOCH({fastk_period},{slowk_period},{slowd_period}) - CLOSE")
            
            slowk, slowd = talib.STOCH(
                high, low, close,
                fastk_period=fastk_period,
                slowk_period=slowk_period,
                slowd_period=slowd_period
            )
            
            return {
                'k': IndicatorResult(
                    name="STOCH_K",
                    type=IndicatorType.MOMENTUM,
                    values=slowk,
                    current=self._get_current_value(slowk),
                    parameters={'fastk_period': fastk_period, 'slowk_period': slowk_period},
                    timestamp=datetime.now()
                ),
                'd': IndicatorResult(
                    name="STOCH_D",
                    type=IndicatorType.MOMENTUM,
                    values=slowd,
                    current=self._get_current_value(slowd),
                    parameters={'slowd_period': slowd_period},
                    timestamp=datetime.now()
                )
            }
            
        except Exception as e:
            logger.error(f"STOCH计算失败: {e}")
            raise IndicatorError(f"STOCH计算失败: {e}")
    
    def cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """商品通道指数 (Commodity Channel Index)"""
        try:
            high = self._validate_data(high, period, f"CCI({period}) - HIGH")
            low = self._validate_data(low, period, f"CCI({period}) - LOW")
            close = self._validate_data(close, period, f"CCI({period}) - CLOSE")
            
            values = talib.CCI(high, low, close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"CCI_{period}",
                type=IndicatorType.MOMENTUM,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"CCI计算失败: {e}")
            raise IndicatorError(f"CCI计算失败: {e}")
    
    def willr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """威廉指标 (Williams %R)"""
        try:
            high = self._validate_data(high, period, f"WILLR({period}) - HIGH")
            low = self._validate_data(low, period, f"WILLR({period}) - LOW")
            close = self._validate_data(close, period, f"WILLR({period}) - CLOSE")
            
            values = talib.WILLR(high, low, close, timeperiod=period)
            current = self._get_current_value(values)
            
            return IndicatorResult(
                name=f"WILLR_{period}",
                type=IndicatorType.MOMENTUM,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"WILLR计算失败: {e}")
            raise IndicatorError(f"WILLR计算失败: {e}")

    # ==================== 成交量类指标 ====================

    def obv(self, close: np.ndarray, volume: np.ndarray) -> IndicatorResult:
        """能量潮指标 (On Balance Volume)"""
        try:
            close = self._validate_data(close, 2, "OBV - CLOSE")
            volume = self._validate_data(volume, 2, "OBV - VOLUME")

            values = talib.OBV(close, volume)
            current = self._get_current_value(values)

            return IndicatorResult(
                name="OBV",
                type=IndicatorType.VOLUME,
                values=values,
                current=current,
                parameters={},
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"OBV计算失败: {e}")
            raise IndicatorError(f"OBV计算失败: {e}")

    def ad(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> IndicatorResult:
        """累积/派发线 (Accumulation/Distribution Line)"""
        try:
            high = self._validate_data(high, 1, "AD - HIGH")
            low = self._validate_data(low, 1, "AD - LOW")
            close = self._validate_data(close, 1, "AD - CLOSE")
            volume = self._validate_data(volume, 1, "AD - VOLUME")

            values = talib.AD(high, low, close, volume)
            current = self._get_current_value(values)

            return IndicatorResult(
                name="AD",
                type=IndicatorType.VOLUME,
                values=values,
                current=current,
                parameters={},
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"AD计算失败: {e}")
            raise IndicatorError(f"AD计算失败: {e}")

    def adosc(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray,
              fast_period: int = 3, slow_period: int = 10) -> IndicatorResult:
        """累积/派发震荡器 (Accumulation/Distribution Oscillator)"""
        try:
            min_periods = max(fast_period, slow_period)
            high = self._validate_data(high, min_periods, f"ADOSC({fast_period},{slow_period}) - HIGH")
            low = self._validate_data(low, min_periods, f"ADOSC({fast_period},{slow_period}) - LOW")
            close = self._validate_data(close, min_periods, f"ADOSC({fast_period},{slow_period}) - CLOSE")
            volume = self._validate_data(volume, min_periods, f"ADOSC({fast_period},{slow_period}) - VOLUME")

            values = talib.ADOSC(high, low, close, volume, fastperiod=fast_period, slowperiod=slow_period)
            current = self._get_current_value(values)

            return IndicatorResult(
                name=f"ADOSC_{fast_period}_{slow_period}",
                type=IndicatorType.VOLUME,
                values=values,
                current=current,
                parameters={'fast_period': fast_period, 'slow_period': slow_period},
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"ADOSC计算失败: {e}")
            raise IndicatorError(f"ADOSC计算失败: {e}")

    # ==================== 波动率类指标 ====================

    def atr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """平均真实波幅 (Average True Range)"""
        try:
            high = self._validate_data(high, period, f"ATR({period}) - HIGH")
            low = self._validate_data(low, period, f"ATR({period}) - LOW")
            close = self._validate_data(close, period, f"ATR({period}) - CLOSE")

            values = talib.ATR(high, low, close, timeperiod=period)
            current = self._get_current_value(values)

            return IndicatorResult(
                name=f"ATR_{period}",
                type=IndicatorType.VOLATILITY,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"ATR计算失败: {e}")
            raise IndicatorError(f"ATR计算失败: {e}")

    def natr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> IndicatorResult:
        """标准化平均真实波幅 (Normalized Average True Range)"""
        try:
            high = self._validate_data(high, period, f"NATR({period}) - HIGH")
            low = self._validate_data(low, period, f"NATR({period}) - LOW")
            close = self._validate_data(close, period, f"NATR({period}) - CLOSE")

            values = talib.NATR(high, low, close, timeperiod=period)
            current = self._get_current_value(values)

            return IndicatorResult(
                name=f"NATR_{period}",
                type=IndicatorType.VOLATILITY,
                values=values,
                current=current,
                parameters={'period': period},
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"NATR计算失败: {e}")
            raise IndicatorError(f"NATR计算失败: {e}")

    def bbands(self, close: np.ndarray, period: int = 20, nbdevup: float = 2.0, nbdevdn: float = 2.0) -> Dict[str, IndicatorResult]:
        """布林带 (Bollinger Bands)"""
        try:
            close = self._validate_data(close, period, f"BBANDS({period},{nbdevup},{nbdevdn})")

            upper, middle, lower = talib.BBANDS(
                close,
                timeperiod=period,
                nbdevup=nbdevup,
                nbdevdn=nbdevdn
            )

            return {
                'upper': IndicatorResult(
                    name="BBANDS_UPPER",
                    type=IndicatorType.VOLATILITY,
                    values=upper,
                    current=self._get_current_value(upper),
                    parameters={'period': period, 'nbdevup': nbdevup},
                    timestamp=datetime.now()
                ),
                'middle': IndicatorResult(
                    name="BBANDS_MIDDLE",
                    type=IndicatorType.VOLATILITY,
                    values=middle,
                    current=self._get_current_value(middle),
                    parameters={'period': period},
                    timestamp=datetime.now()
                ),
                'lower': IndicatorResult(
                    name="BBANDS_LOWER",
                    type=IndicatorType.VOLATILITY,
                    values=lower,
                    current=self._get_current_value(lower),
                    parameters={'period': period, 'nbdevdn': nbdevdn},
                    timestamp=datetime.now()
                )
            }

        except Exception as e:
            logger.error(f"BBANDS计算失败: {e}")
            raise IndicatorError(f"BBANDS计算失败: {e}")

    # ==================== 批量计算方法 ====================

    def calculate_all_trend_indicators(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, Any]:
        """计算所有趋势类指标"""
        try:
            results = {}

            # SMA指标
            for period in [5, 10, 20, 50]:
                try:
                    sma_result = self.sma(close, period)
                    results[f'sma_{period}'] = sma_result.to_dict()
                except Exception as e:
                    logger.warning(f"SMA({period})计算失败: {e}")

            # EMA指标
            for period in [5, 10, 20, 50]:
                try:
                    ema_result = self.ema(close, period)
                    results[f'ema_{period}'] = ema_result.to_dict()
                except Exception as e:
                    logger.warning(f"EMA({period})计算失败: {e}")

            # MACD指标
            try:
                macd_results = self.macd(close)
                results['macd'] = {
                    'macd': macd_results['macd'].to_dict(),
                    'signal': macd_results['signal'].to_dict(),
                    'histogram': macd_results['histogram'].to_dict()
                }
            except Exception as e:
                logger.warning(f"MACD计算失败: {e}")

            # ADX指标
            try:
                adx_result = self.adx(high, low, close)
                results['adx'] = adx_result.to_dict()
            except Exception as e:
                logger.warning(f"ADX计算失败: {e}")

            return results

        except Exception as e:
            logger.error(f"趋势指标批量计算失败: {e}")
            return {}

    def calculate_all_momentum_indicators(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, Any]:
        """计算所有震荡类指标"""
        try:
            results = {}

            # RSI指标
            try:
                rsi_result = self.rsi(close)
                results['rsi'] = rsi_result.to_dict()
            except Exception as e:
                logger.warning(f"RSI计算失败: {e}")

            # STOCH指标
            try:
                stoch_results = self.stoch(high, low, close)
                results['stoch'] = {
                    'k': stoch_results['k'].to_dict(),
                    'd': stoch_results['d'].to_dict()
                }
            except Exception as e:
                logger.warning(f"STOCH计算失败: {e}")

            # CCI指标
            try:
                cci_result = self.cci(high, low, close)
                results['cci'] = cci_result.to_dict()
            except Exception as e:
                logger.warning(f"CCI计算失败: {e}")

            # Williams %R指标
            try:
                willr_result = self.willr(high, low, close)
                results['willr'] = willr_result.to_dict()
            except Exception as e:
                logger.warning(f"WILLR计算失败: {e}")

            return results

        except Exception as e:
            logger.error(f"震荡指标批量计算失败: {e}")
            return {}

    def calculate_all_volume_indicators(self, high: np.ndarray, low: np.ndarray,
                                      close: np.ndarray, volume: np.ndarray) -> Dict[str, Any]:
        """计算所有成交量类指标"""
        try:
            results = {}

            # OBV指标
            try:
                obv_result = self.obv(close, volume)
                results['obv'] = obv_result.to_dict()
            except Exception as e:
                logger.warning(f"OBV计算失败: {e}")

            # AD指标
            try:
                ad_result = self.ad(high, low, close, volume)
                results['ad'] = ad_result.to_dict()
            except Exception as e:
                logger.warning(f"AD计算失败: {e}")

            # ADOSC指标
            try:
                adosc_result = self.adosc(high, low, close, volume)
                results['adosc'] = adosc_result.to_dict()
            except Exception as e:
                logger.warning(f"ADOSC计算失败: {e}")

            return results

        except Exception as e:
            logger.error(f"成交量指标批量计算失败: {e}")
            return {}

    def calculate_all_volatility_indicators(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, Any]:
        """计算所有波动率类指标"""
        try:
            results = {}

            # ATR指标
            try:
                atr_result = self.atr(high, low, close)
                results['atr'] = atr_result.to_dict()
            except Exception as e:
                logger.warning(f"ATR计算失败: {e}")

            # NATR指标
            try:
                natr_result = self.natr(high, low, close)
                results['natr'] = natr_result.to_dict()
            except Exception as e:
                logger.warning(f"NATR计算失败: {e}")

            # 布林带指标
            try:
                bbands_results = self.bbands(close)
                results['bbands'] = {
                    'upper': bbands_results['upper'].to_dict(),
                    'middle': bbands_results['middle'].to_dict(),
                    'lower': bbands_results['lower'].to_dict()
                }
            except Exception as e:
                logger.warning(f"BBANDS计算失败: {e}")

            return results

        except Exception as e:
            logger.error(f"波动率指标批量计算失败: {e}")
            return {}

    def calculate_all_indicators(self, high: np.ndarray, low: np.ndarray,
                               close: np.ndarray, volume: np.ndarray) -> Dict[str, Any]:
        """计算所有技术指标"""
        try:
            start_time = datetime.now()

            # 验证输入数据
            if len(high) != len(low) or len(low) != len(close) or len(close) != len(volume):
                raise IndicatorError("输入数据长度不一致")

            results = {
                'symbol': 'UNKNOWN',
                'timestamp': start_time.isoformat(),
                'data_points': len(close),
                'current_price': float(close[-1]) if len(close) > 0 else None,
                'price_change': float(close[-1] - close[-2]) if len(close) >= 2 else 0,
                'price_change_pct': float((close[-1] - close[-2]) / close[-2] * 100) if len(close) >= 2 else 0,
                'trend_indicators': {},
                'momentum_indicators': {},
                'volume_indicators': {},
                'volatility_indicators': {}
            }

            # 计算各类指标
            results['trend_indicators'] = self.calculate_all_trend_indicators(high, low, close)
            results['momentum_indicators'] = self.calculate_all_momentum_indicators(high, low, close)
            results['volume_indicators'] = self.calculate_all_volume_indicators(high, low, close, volume)
            results['volatility_indicators'] = self.calculate_all_volatility_indicators(high, low, close)

            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            results['processing_time'] = processing_time

            # 统计成功计算的指标数量
            total_indicators = (
                len(results['trend_indicators']) +
                len(results['momentum_indicators']) +
                len(results['volume_indicators']) +
                len(results['volatility_indicators'])
            )
            results['total_indicators'] = total_indicators

            logger.info(f"技术指标计算完成: {total_indicators} 个指标, 耗时 {processing_time:.3f}s")
            return results

        except Exception as e:
            logger.error(f"技术指标批量计算失败: {e}")
            raise IndicatorError(f"技术指标批量计算失败: {e}")

    # ==================== 工具方法 ====================

    def get_indicator_summary(self, indicators_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取指标摘要信息"""
        try:
            summary = {
                'trend_signals': {},
                'momentum_signals': {},
                'volume_signals': {},
                'volatility_signals': {},
                'overall_signal': 'NEUTRAL'
            }

            # 趋势信号分析
            trend_indicators = indicators_data.get('trend_indicators', {})
            if 'rsi' in indicators_data.get('momentum_indicators', {}):
                rsi_current = indicators_data['momentum_indicators']['rsi'].get('current')
                if rsi_current:
                    if rsi_current > 70:
                        summary['momentum_signals']['rsi'] = 'OVERBOUGHT'
                    elif rsi_current < 30:
                        summary['momentum_signals']['rsi'] = 'OVERSOLD'
                    else:
                        summary['momentum_signals']['rsi'] = 'NEUTRAL'

            # MACD信号分析
            if 'macd' in trend_indicators:
                macd_data = trend_indicators['macd']
                if isinstance(macd_data, dict) and 'macd' in macd_data and 'signal' in macd_data:
                    macd_current = macd_data['macd'].get('current')
                    signal_current = macd_data['signal'].get('current')

                    if macd_current and signal_current:
                        if macd_current > signal_current:
                            summary['trend_signals']['macd'] = 'BULLISH'
                        else:
                            summary['trend_signals']['macd'] = 'BEARISH'

            # 布林带信号分析
            volatility_indicators = indicators_data.get('volatility_indicators', {})
            if 'bbands' in volatility_indicators:
                bbands_data = volatility_indicators['bbands']
                if isinstance(bbands_data, dict):
                    upper = bbands_data.get('upper', {}).get('current')
                    lower = bbands_data.get('lower', {}).get('current')
                    current_price = indicators_data.get('current_price')

                    if all([upper, lower, current_price]):
                        if current_price > upper:
                            summary['volatility_signals']['bbands'] = 'OVERBOUGHT'
                        elif current_price < lower:
                            summary['volatility_signals']['bbands'] = 'OVERSOLD'
                        else:
                            summary['volatility_signals']['bbands'] = 'NEUTRAL'

            return summary

        except Exception as e:
            logger.error(f"指标摘要分析失败: {e}")
            return {'error': str(e)}

    def validate_indicator_data(self, indicators_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证指标数据的有效性"""
        try:
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'valid_indicators': 0,
                'invalid_indicators': 0
            }

            # 检查必需字段
            required_fields = ['trend_indicators', 'momentum_indicators', 'volume_indicators', 'volatility_indicators']
            for field in required_fields:
                if field not in indicators_data:
                    validation_result['errors'].append(f"缺少必需字段: {field}")
                    validation_result['is_valid'] = False

            # 验证各类指标
            for category in required_fields:
                if category in indicators_data:
                    category_data = indicators_data[category]
                    if isinstance(category_data, dict):
                        for indicator_name, indicator_data in category_data.items():
                            if isinstance(indicator_data, dict):
                                if indicator_data.get('is_valid', False):
                                    validation_result['valid_indicators'] += 1
                                else:
                                    validation_result['invalid_indicators'] += 1
                                    validation_result['warnings'].append(f"指标 {indicator_name} 数据无效")

            # 检查数据完整性
            if validation_result['valid_indicators'] == 0:
                validation_result['errors'].append("没有有效的技术指标数据")
                validation_result['is_valid'] = False
            elif validation_result['invalid_indicators'] > validation_result['valid_indicators']:
                validation_result['warnings'].append("无效指标数量超过有效指标数量")

            return validation_result

        except Exception as e:
            logger.error(f"指标数据验证失败: {e}")
            return {
                'is_valid': False,
                'errors': [f"验证过程异常: {e}"],
                'warnings': [],
                'valid_indicators': 0,
                'invalid_indicators': 0
            }


# 全局技术指标计算器实例
technical_indicators = TechnicalIndicators()


def get_technical_indicators() -> TechnicalIndicators:
    """获取技术指标计算器实例"""
    return technical_indicators
