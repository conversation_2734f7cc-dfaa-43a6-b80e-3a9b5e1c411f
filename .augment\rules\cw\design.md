---
type: "always_apply"
---

# DeepSeek加密货币永续合约全自动量化交易系统 设计文档

## 概述

### 设计目标
本系统旨在构建一个基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，实现智能化的交易决策和风险管理。系统采用模块化设计，确保高可用性、可扩展性和安全性。

### 范围
- 支持欧易交易所的模拟盘和实盘交易
- 集成DeepSeek AI模型进行智能决策
- 提供人性化的桌面应用界面
- 实现多时间周期技术分析
- 支持全自动化的开仓和持仓管理

### 关键约束
- 使用Python作为主要开发语言
- 采用RESTful API与交易所通信，不使用WebSocket
- 使用SQLite数据库存储配置，不存储交易数据
- 支持保证金交易和全仓模式
- 仅支持单向持仓策略

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[PyWebview桌面应用]
    end
    
    subgraph "应用层"
        API[内置API接口]
        CONFIG[配置管理器]
        SCHEDULER[任务调度器]
    end
    
    subgraph "业务逻辑层"
        OPEN_ENGINE[AI开仓引擎]
        HOLD_ENGINE[AI持仓引擎]
        RISK_MGR[风险管理器]
        TRADE_EXEC[交易执行器]
    end
    
    subgraph "数据处理层"
        MARKET_DATA[市场数据处理器]
        TECH_ANALYSIS[技术分析引擎]
        AI_CLIENT[DeepSeek AI客户端]
    end
    
    subgraph "基础设施层"
        CCXT_CLIENT[CCXT交易所客户端]
        DB[SQLite数据库]
        LOGGER[日志系统]
    end
    
    subgraph "外部服务"
        OKX[欧易交易所]
        DEEPSEEK[DeepSeek API]
    end
    
    UI --> API
    API --> CONFIG
    API --> SCHEDULER
    
    SCHEDULER --> OPEN_ENGINE
    SCHEDULER --> HOLD_ENGINE
    
    OPEN_ENGINE --> TECH_ANALYSIS
    HOLD_ENGINE --> TECH_ANALYSIS
    OPEN_ENGINE --> AI_CLIENT
    HOLD_ENGINE --> AI_CLIENT
    
    OPEN_ENGINE --> RISK_MGR
    HOLD_ENGINE --> RISK_MGR
    RISK_MGR --> TRADE_EXEC
    
    TECH_ANALYSIS --> MARKET_DATA
    MARKET_DATA --> CCXT_CLIENT
    TRADE_EXEC --> CCXT_CLIENT
    
    CCXT_CLIENT --> OKX
    AI_CLIENT --> DEEPSEEK
    
    CONFIG --> DB
    LOGGER --> DB
```

### 部署架构
- **单机部署**：所有组件运行在同一台机器上
- **进程模型**：主进程运行PyWebview界面，后台线程处理交易逻辑
- **数据存储**：本地SQLite数据库存储配置和日志
- **网络通信**：通过HTTPS与外部API通信

### 关键技术选型

| 组件 | 技术选择 | 理由 |
|------|----------|------|
| 前端框架 | PyWebview + HTML/CSS/JS | 跨平台桌面应用，开发效率高 |
| 后端语言 | Python 3.8+ | 丰富的金融库支持，开发效率高 |
| 交易所接口 | CCXT | 统一的交易所API接口 |
| 技术分析 | TA-Lib | 成熟的技术分析库 |
| AI模型 | DeepSeek API | 强大的推理能力 |
| 数据库 | SQLite3 | 轻量级，无需额外部署 |
| 任务调度 | APScheduler | Python原生调度库 |

### 项目目录结构

```
/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包列表
├── setup.py                     # 项目安装配置
├── .env.example                 # 环境变量示例文件
├── .gitignore                   # Git忽略文件配置
├── main.py                      # 应用程序入口
│
├── src/                         # 源代码目录
│   ├── __init__.py
│   │
│   ├── ui/                      # 用户界面层
│   │   ├── __init__.py
│   │   ├── webview_app.py       # PyWebview应用主程序
│   │   ├── api.py               # 前后端通信API
│   │   └── static/              # 静态资源
│   │       ├── index.html       # 主页面
│   │       ├── css/             # 样式文件
│   │       │   └── style.css
│   │       └── js/              # JavaScript文件
│   │           ├── app.js       # 主应用逻辑
│   │           ├── charts.js    # 图表组件
│   │           └── utils.js     # 工具函数
│   │
│   ├── core/                    # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── ai_engines/          # AI引擎模块
│   │   │   ├── __init__.py
│   │   │   ├── base_engine.py   # AI引擎基类
│   │   │   ├── open_engine.py   # AI开仓引擎
│   │   │   ├── hold_engine.py   # AI持仓引擎
│   │   │   └── prompts.py       # AI提示词模板
│   │   │
│   │   ├── trading/             # 交易相关模块
│   │   │   ├── __init__.py
│   │   │   ├── executor.py      # 交易执行器
│   │   │   ├── risk_manager.py  # 风险管理器
│   │   │   └── position_manager.py # 仓位管理器
│   │   │
│   │   ├── analysis/            # 技术分析模块
│   │   │   ├── __init__.py
│   │   │   ├── technical_engine.py # 技术分析引擎
│   │   │   ├── indicators.py    # 技术指标计算
│   │   │   └── data_processor.py # 数据处理器
│   │   │
│   │   └── scheduler/           # 任务调度模块
│   │       ├── __init__.py
│   │       ├── task_scheduler.py # 任务调度器
│   │       └── jobs.py          # 定时任务定义
│   │
│   ├── services/                # 服务层
│   │   ├── __init__.py
│   │   ├── exchange_service.py  # 交易所服务
│   │   ├── ai_service.py        # AI服务
│   │   ├── market_data_service.py # 市场数据服务
│   │   └── notification_service.py # 通知服务
│   │
│   ├── data/                    # 数据访问层
│   │   ├── __init__.py
│   │   ├── database.py          # 数据库连接和操作
│   │   ├── models.py            # 数据模型定义
│   │   ├── repositories/        # 数据仓库
│   │   │   ├── __init__.py
│   │   │   ├── config_repository.py # 配置数据仓库
│   │   │   ├── trading_repository.py # 交易数据仓库
│   │   │   └── log_repository.py # 日志数据仓库
│   │   └── migrations/          # 数据库迁移脚本
│   │       ├── __init__.py
│   │       └── init_db.sql      # 初始化数据库脚本
│   │
│   ├── utils/                   # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py            # 日志工具
│   │   ├── config.py            # 配置管理
│   │   ├── crypto.py            # 加密解密工具
│   │   ├── validators.py        # 数据验证工具
│   │   └── exceptions.py        # 自定义异常
│   │
│   └── constants/               # 常量定义
│       ├── __init__.py
│       ├── trading_constants.py # 交易相关常量
│       ├── api_constants.py     # API相关常量
│       └── ui_constants.py      # UI相关常量
│
├── tests/                       # 测试代码
│   ├── __init__.py
│   ├── conftest.py              # pytest配置
│   ├── unit/                    # 单元测试
│   │   ├── __init__.py
│   │   ├── test_ai_engines.py   # AI引擎测试
│   │   ├── test_trading.py      # 交易模块测试
│   │   ├── test_analysis.py     # 技术分析测试
│   │   └── test_utils.py        # 工具模块测试
│   │
│   ├── integration/             # 集成测试
│   │   ├── __init__.py
│   │   ├── test_trading_flow.py # 交易流程测试
│   │   └── test_api_integration.py # API集成测试
│   │
│   └── fixtures/                # 测试配置
│       ├── __init__.py
│       └── test_config.json     # 测试环境配置
│
├── docs/                        # 文档目录
│   ├── README.md                # 文档说明
│   ├── installation.md          # 安装指南
│   ├── configuration.md         # 配置说明
│   ├── api_reference.md         # API参考
│   └── troubleshooting.md       # 故障排除
│
├── scripts/                     # 脚本目录
│   ├── install_dependencies.py  # 依赖安装脚本
│   ├── setup_database.py        # 数据库初始化脚本
│   ├── backup_config.py         # 配置备份脚本
│   └── run_tests.py             # 测试运行脚本
│
├── config/                      # 配置文件目录
│   ├── default.json             # 默认配置
│   ├── development.json         # 开发环境配置
│   ├── production.json          # 生产环境配置
│   └── logging.json             # 日志配置
│
├── data/                        # 数据文件目录
│   ├── database/                # 数据库文件
│   │   └── superbot.db          # SQLite数据库文件
│   ├── logs/                    # 日志文件
│   │   ├── app.log              # 应用日志
│   │   ├── trading.log          # 交易日志
│   │   └── error.log            # 错误日志
│   └── cache/                   # 缓存文件
│       └── indicators_cache.json # 技术指标缓存
│
└── build/                       # 构建输出目录
    ├── dist/                    # 分发包
    └── temp/                    # 临时文件
```

### 目录结构说明

#### 核心模块职责

**src/ui/** - 用户界面层
- `webview_app.py`: PyWebview应用主程序，负责创建桌面应用窗口
- `api.py`: 前后端通信API，提供JavaScript调用的Python方法
- `static/`: 前端静态资源，包含HTML、CSS、JavaScript文件

**src/core/** - 核心业务逻辑层
- `ai_engines/`: AI引擎模块，包含开仓和持仓两个独立的AI引擎
- `trading/`: 交易相关模块，包含交易执行、风险管理、仓位管理
- `analysis/`: 技术分析模块，负责计算技术指标和数据处理
- `scheduler/`: 任务调度模块，管理定时任务和作业调度

**src/services/** - 服务层
- `exchange_service.py`: 交易所服务，封装CCXT交易所操作
- `ai_service.py`: AI服务，封装DeepSeek API调用
- `market_data_service.py`: 市场数据服务，负责数据获取和缓存
- `notification_service.py`: 通知服务，处理系统通知和告警

**src/data/** - 数据访问层
- `database.py`: 数据库连接和基础操作
- `models.py`: 数据模型定义，对应数据库表结构
- `repositories/`: 数据仓库模式，封装数据访问逻辑
- `migrations/`: 数据库迁移脚本，管理数据库版本

**src/utils/** - 工具模块
- `logger.py`: 统一的日志管理工具
- `config.py`: 配置文件读取和管理
- `crypto.py`: 加密解密工具，用于API密钥保护
- `validators.py`: 数据验证工具，确保数据完整性
- `exceptions.py`: 自定义异常类定义

#### 配置和数据管理

**config/** - 配置文件目录
- 支持多环境配置（开发、生产）
- JSON格式配置文件，易于维护
- 日志配置独立管理

**data/** - 数据文件目录
- `database/`: SQLite数据库文件存储
- `logs/`: 分类日志文件存储
- `cache/`: 临时缓存文件存储

#### 测试和文档

**tests/** - 测试代码
- `unit/`: 单元测试，使用真实API进行功能测试
- `integration/`: 集成测试，测试完整业务流程
- `fixtures/`: 测试环境配置文件

**docs/** - 项目文档
- 安装指南、配置说明、API参考
- 故障排除和常见问题解答

#### 构建和部署

**scripts/** - 自动化脚本
- 依赖安装、数据库初始化
- 配置备份、测试运行脚本

**build/** - 构建输出
- 分发包和临时文件存储

## 组件和接口

### 核心组件详细设计

#### 1. PyWebview桌面应用 (UI层)
**功能职责：**
- 提供用户交互界面
- 显示实时交易状态
- 配置系统参数

**关键接口：**
```python
class WebviewAPI:
    def get_system_status(self) -> dict
    def get_trading_pairs(self) -> list
    def update_global_settings(self, settings: dict) -> bool
    def start_trading(self, pairs: list) -> bool
    def stop_trading(self) -> bool
    def get_positions(self) -> list
    def get_trading_history(self) -> list
```

#### 2. AI开仓引擎 (AIOpenEngine)
**功能职责：**
- 分析市场机会
- 决定开仓时机和方向
- 生成开仓信号

**关键接口：**
```python
class AIOpenEngine:
    def analyze_market(self, symbol: str) -> dict
    def should_open_position(self, analysis: dict) -> bool
    def get_position_direction(self, analysis: dict) -> str  # 'long' or 'short'
    def calculate_position_size(self, confidence: float) -> float
```

**AI提示词模板：**
```
你是一个专业的加密货币交易分析师。基于以下技术指标数据，分析市场趋势并给出开仓建议：

技术指标数据：
- 1分钟周期：{indicators_1m}
- 5分钟周期：{indicators_5m}  
- 15分钟周期：{indicators_15m}
- 1小时周期：{indicators_1h}

请分析：
1. 当前市场趋势方向
2. 入场时机是否合适
3. 建议开仓方向（多头/空头）
4. 置信度评分（0-100）
5. 详细分析理由

返回JSON格式：
{
  "direction": "long/short/none",
  "confidence": 85,
  "reasoning": "详细分析理由"
}
```

#### 3. AI持仓引擎 (AIHoldEngine)
**功能职责：**
- 监控现有持仓
- 决定平仓时机
- 管理止盈止损

**关键接口：**
```python
class AIHoldEngine:
    def analyze_position(self, position: dict, market_data: dict) -> dict
    def should_close_position(self, analysis: dict) -> bool
    def update_stop_loss(self, position: dict, analysis: dict) -> float
    def update_take_profit(self, position: dict, analysis: dict) -> float
```

**AI提示词模板：**
```
你是一个专业的仓位管理专家。基于当前持仓信息和市场数据，给出持仓管理建议：

当前持仓：
- 交易对：{symbol}
- 方向：{direction}
- 开仓价格：{entry_price}
- 当前价格：{current_price}
- 未实现盈亏：{unrealized_pnl}
- 持仓时间：{hold_duration}

技术指标：{technical_indicators}

请分析：
1. 是否应该继续持有
2. 是否需要调整止损位
3. 是否需要调整止盈位
4. 平仓置信度（0-100）
5. 详细分析理由

返回JSON格式：
{
  "action": "hold/close",
  "confidence": 75,
  "stop_loss": 45000,
  "take_profit": 52000,
  "reasoning": "详细分析理由"
}
```

#### 4. 技术分析引擎 (TechnicalAnalysisEngine)
**功能职责：**
- 计算多时间周期技术指标
- 数据预处理和格式化
- 指标结果缓存

**关键接口：**
```python
class TechnicalAnalysisEngine:
    def calculate_indicators(self, symbol: str, timeframes: list) -> dict
    def get_trend_analysis(self, indicators: dict) -> dict
    def format_for_ai(self, indicators: dict) -> str
```

**支持的技术指标：**
- 趋势类：SMA, EMA, MACD, ADX
- 震荡类：RSI, STOCH, CCI, Williams %R
- 成交量类：OBV, AD, ADOSC
- 波动率类：ATR, NATR, BBANDS

#### 5. 风险管理器 (RiskManager)
**功能职责：**
- 验证交易参数
- 计算仓位大小
- 执行风险控制

**关键接口：**
```python
class RiskManager:
    def validate_trade(self, trade_params: dict) -> bool
    def calculate_position_size(self, balance: float, risk_params: dict) -> float
    def check_max_leverage(self, leverage: float) -> bool
    def apply_stop_loss(self, position: dict, price: float) -> dict
```

#### 6. 交易执行器 (TradeExecutor)
**功能职责：**
- 执行开仓和平仓操作
- 管理订单状态
- 处理交易异常

**关键接口：**
```python
class TradeExecutor:
    def open_position(self, symbol: str, side: str, size: float, leverage: int) -> dict
    def close_position(self, position_id: str) -> dict
    def set_stop_loss(self, position_id: str, price: float) -> dict
    def set_take_profit(self, position_id: str, price: float) -> dict
```

## 数据模型

### 数据库表设计

#### 1. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 交易参数表 (trading_params)
```sql
CREATE TABLE trading_params (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    max_leverage INTEGER DEFAULT 10,
    max_position_ratio REAL DEFAULT 0.1,
    stop_loss_ratio REAL DEFAULT 0.05,
    take_profit_ratio REAL DEFAULT 0.1,
    confidence_threshold REAL DEFAULT 70.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. API密钥表 (api_keys)
```sql
CREATE TABLE api_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    exchange TEXT NOT NULL,
    api_key TEXT NOT NULL,
    secret_key TEXT NOT NULL,
    passphrase TEXT,
    is_sandbox BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 交易对配置表 (trading_pairs)
```sql
CREATE TABLE trading_pairs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    min_order_size REAL,
    tick_size REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL,
    module TEXT NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 数据流设计

#### 市场数据流
1. **数据获取**：CCXT客户端从欧易交易所获取K线数据
2. **数据处理**：技术分析引擎计算多时间周期指标
3. **数据格式化**：将指标数据格式化为AI模型输入格式
4. **缓存管理**：将计算结果缓存以提高性能

#### 交易决策流
1. **市场分析**：AI引擎分析技术指标数据
2. **信号生成**：根据AI分析结果生成交易信号
3. **风险评估**：风险管理器验证交易参数
4. **订单执行**：交易执行器提交订单到交易所

## 错误处理

### 异常分类和处理策略

#### 1. 网络异常
- **连接超时**：自动重试，最大重试3次
- **API限流**：延迟重试，动态调整请求频率
- **网络中断**：暂停交易，等待网络恢复

#### 2. 交易异常
- **余额不足**：记录错误，停止新开仓
- **订单失败**：记录详情，尝试重新提交
- **价格异常**：暂停交易，人工介入

#### 3. AI服务异常
- **API调用失败**：使用备用策略或停止交易
- **响应格式错误**：记录错误，跳过该次决策
- **服务不可用**：切换到保守模式

#### 4. 数据异常
- **指标计算失败**：使用历史数据或跳过
- **数据缺失**：等待数据补全
- **数据异常**：数据验证和清洗

### 错误恢复机制
- **自动重试**：网络和临时性错误自动重试
- **降级服务**：关键服务不可用时启用备用方案
- **安全停止**：严重错误时安全停止交易
- **状态恢复**：系统重启后恢复到安全状态

## 安全考虑

### 数据安全
- **API密钥加密**：使用AES加密存储API密钥
- **数据库加密**：敏感数据字段加密存储
- **传输加密**：所有外部通信使用HTTPS
- **访问控制**：数据库文件权限控制

### 交易安全
- **参数验证**：严格验证所有交易参数
- **风险限制**：多层风险控制机制
- **异常监控**：实时监控异常交易行为
- **紧急停止**：提供紧急停止交易功能

### 系统安全
- **输入验证**：验证所有用户输入
- **日志审计**：记录所有关键操作
- **权限管理**：最小权限原则
- **安全更新**：定期更新依赖库

## 性能考虑

### 响应时间要求
- **UI响应**：界面操作响应时间 < 200ms
- **数据获取**：市场数据获取延迟 < 1s
- **AI分析**：AI模型分析时间 < 5s
- **交易执行**：订单提交响应时间 < 2s

### 吞吐量设计
- **并发处理**：支持多个交易对同时分析
- **API限制**：遵守交易所API频率限制
- **缓存策略**：技术指标结果缓存30秒
- **数据库优化**：索引优化和查询优化

### 资源使用
- **内存使用**：系统运行内存 < 512MB
- **CPU使用**：正常运行CPU使用率 < 30%
- **磁盘空间**：日志文件自动轮转，保留30天
- **网络带宽**：API调用带宽需求 < 1Mbps

## 可维护性和可扩展性

### 代码规范
- **PEP 8**：遵循Python代码规范
- **类型注解**：使用类型提示增强代码可读性
- **文档字符串**：所有公共方法提供详细文档
- **单元测试**：核心功能测试覆盖率 > 80%

### 模块化设计
- **松耦合**：模块间通过接口通信
- **高内聚**：相关功能集中在同一模块
- **依赖注入**：使用依赖注入提高可测试性
- **配置外部化**：所有配置参数可外部配置

### 扩展点设计
- **交易所扩展**：支持添加新的交易所
- **指标扩展**：支持添加新的技术指标
- **AI模型扩展**：支持切换不同的AI模型
- **策略扩展**：支持添加新的交易策略

## 部署和运维

### 部署策略
- **单机部署**：适合个人用户的简单部署
- **环境隔离**：开发、测试、生产环境分离
- **配置管理**：使用配置文件管理不同环境
- **依赖管理**：使用requirements.txt管理依赖

### 监控和日志
- **系统监控**：监控CPU、内存、磁盘使用情况
- **业务监控**：监控交易成功率、盈亏情况
- **日志收集**：结构化日志记录所有关键事件
- **告警机制**：异常情况及时告警

### 备份和恢复
- **配置备份**：定期备份数据库配置
- **日志备份**：重要日志文件备份
- **快速恢复**：系统故障后快速恢复机制
- **数据迁移**：支持数据迁移到新环境

## 测试策略

### 测试类型
- **单元测试**：测试单个函数和方法，使用真实API调用
- **集成测试**：测试模块间的集成，使用真实的交易所和AI服务
- **系统测试**：测试完整的业务流程，在模拟盘环境中执行真实交易
- **性能测试**：测试系统性能指标，使用真实的数据负载

### 测试环境
- **模拟盘环境**：使用欧易交易所模拟盘API进行真实测试
- **真实API调用**：所有测试都使用真实的API调用，包括交易所API和DeepSeek API
- **真实数据处理**：从交易所获取真实市场数据进行技术分析测试
- **真实交易测试**：在模拟盘环境中执行真实的开仓、平仓操作

### 测试覆盖率
- **代码覆盖率**：核心业务逻辑覆盖率 > 80%
- **分支覆盖率**：关键分支逻辑覆盖率 > 90%
- **异常覆盖率**：异常处理路径覆盖率 > 70%
- **集成覆盖率**：主要业务流程覆盖率 100%
