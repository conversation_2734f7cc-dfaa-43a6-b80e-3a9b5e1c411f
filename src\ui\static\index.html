<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBot - 加密货币量化交易系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入Alpine.js用于响应式交互 -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body x-data="appData()" x-init="init()">
    <!-- 顶部导航栏 - 简化设计，移除重复的连接状态 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">SuperBot</span>
                    <span class="brand-subtitle">量化交易系统</span>
                </div>
            </div>
            <div class="nav-actions">
                <!-- 环境切换开关 -->
                <div class="env-switch" x-data="{ isLive: false }">
                    <span class="env-label" :class="{ 'active': !isLive }">模拟</span>
                    <label class="switch">
                        <input type="checkbox" x-model="isLive" @change="toggleEnvironment">
                        <span class="slider"></span>
                    </label>
                    <span class="env-label" :class="{ 'active': isLive }">实盘</span>
                </div>
                <!-- 连接状态指示器 - 更简洁的设计 -->
                <div class="connection-indicator" :class="connectionStatus">
                    <div class="status-dot"></div>
                    <span x-text="connectionText"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-menu">
                <div class="menu-item active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item" data-tab="trading">
                    <i class="fas fa-chart-line"></i>
                    <span>交易控制</span>
                </div>
                <div class="menu-item" data-tab="positions">
                    <i class="fas fa-wallet"></i>
                    <span>持仓管理</span>
                </div>
                <div class="menu-item" data-tab="config">
                    <i class="fas fa-cog"></i>
                    <span>系统配置</span>
                </div>
                <div class="menu-item" data-tab="logs">
                    <i class="fas fa-list-alt"></i>
                    <span>日志记录</span>
                </div>
            </div>
        </aside>

        <!-- 内容区域 -->
        <main class="content">
            <!-- 仪表盘页面 -->
            <div id="dashboard" class="tab-content active">
                <!-- 仪表盘头部 - 简化设计，移除重复信息 -->
                <div class="dashboard-header">
                    <div class="header-content">
                        <div class="page-title">
                            <h1>交易仪表盘</h1>
                            <p class="subtitle">实时监控您的量化交易系统</p>
                        </div>
                        <div class="header-actions">
                            <!-- 快速操作按钮 -->
                            <button class="action-btn" @click="refreshData" :disabled="isLoading">
                                <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
                                <span>刷新数据</span>
                            </button>
                            <button class="action-btn primary" @click="toggleTrading" :class="{ 'active': isTrading }">
                                <i class="fas" :class="isTrading ? 'fa-pause' : 'fa-play'"></i>
                                <span x-text="isTrading ? '暂停交易' : '启动交易'"></span>
                            </button>
                        </div>
                    </div>
                    <!-- 实时状态栏 -->
                    <div class="status-bar">
                        <div class="status-item" :class="{ 'active': systemStatus === 'running' }">
                            <i class="fas fa-server"></i>
                            <span>系统运行</span>
                        </div>
                        <div class="status-item" :class="{ 'active': aiStatus === 'active' }">
                            <i class="fas fa-brain"></i>
                            <span>AI分析</span>
                        </div>
                        <div class="status-item" :class="{ 'active': positionsCount > 0 }">
                            <i class="fas fa-briefcase"></i>
                            <span x-text="`${positionsCount} 个持仓`"></span>
                        </div>
                        <div class="status-item update-time">
                            <i class="fas fa-clock"></i>
                            <span x-text="lastUpdateTime"></span>
                        </div>
                    </div>
                </div>

                <!-- 核心指标卡片 - 重新设计为更现代化的布局 -->
                <div class="metrics-grid">
                    <!-- 资产概览卡片 - 突出显示总资产 -->
                    <div class="metric-card primary-card" x-data="{ showDetails: false }">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-wallet"></i>
                                <span>账户资产</span>
                            </div>
                            <button class="toggle-btn" @click="showDetails = !showDetails">
                                <i class="fas" :class="showDetails ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- 主要资产显示 -->
                            <div class="primary-metric">
                                <div class="metric-value" x-text="formatCurrency(totalBalance)">130,839.39</div>
                                <div class="metric-label">总资产 USDT</div>
                                <div class="metric-change" :class="balanceChange >= 0 ? 'positive' : 'negative'">
                                    <i class="fas" :class="balanceChange >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
                                    <span x-text="formatPercentage(balanceChange)">+2.34%</span>
                                </div>
                            </div>
                            <!-- 详细资产信息 -->
                            <div class="asset-details" x-show="showDetails" x-transition>
                                <div class="detail-row">
                                    <span class="detail-label">可用余额</span>
                                    <span class="detail-value" x-text="formatCurrency(availableBalance)">115,628.49</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">已用保证金</span>
                                    <span class="detail-value" x-text="formatCurrency(usedMargin)">15,210.90</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">保证金率</span>
                                    <span class="detail-value" :class="marginRatio > 80 ? 'warning' : ''" x-text="formatPercentage(marginRatio / 100)">11.63%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 今日盈亏卡片 -->
                    <div class="metric-card pnl-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i>
                                <span>今日盈亏</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="pnl-display">
                                <div class="pnl-value" :class="todayPnl >= 0 ? 'positive' : 'negative'" x-text="formatCurrency(todayPnl, true)">+1,234.56</div>
                                <div class="pnl-percentage" :class="todayPnlPercent >= 0 ? 'positive' : 'negative'" x-text="formatPercentage(todayPnlPercent / 100)">+0.94%</div>
                            </div>
                            <div class="pnl-chart">
                                <!-- 简单的盈亏趋势图 -->
                                <canvas id="pnl-mini-chart" width="200" height="60"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 交易统计卡片 -->
                    <div class="metric-card stats-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-trophy"></i>
                                <span>交易统计</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" x-text="winRate">68%</div>
                                    <div class="stat-label">胜率</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" x-text="totalTrades">24</div>
                                    <div class="stat-label">交易次数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" x-text="formatCurrency(avgProfit)">+51.23</div>
                                    <div class="stat-label">平均盈利</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" x-text="formatPercentage(maxDrawdown / 100)">-3.2%</div>
                                    <div class="stat-label">最大回撤</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI引擎状态卡片 -->
                    <div class="metric-card ai-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-brain"></i>
                                <span>AI引擎</span>
                            </div>
                            <div class="ai-status" :class="aiEngineStatus">
                                <div class="status-dot"></div>
                                <span x-text="aiEngineText">运行中</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="ai-metrics">
                                <div class="ai-metric">
                                    <div class="metric-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">市场分析</div>
                                        <div class="metric-value" x-text="lastAnalysisTime">2分钟前</div>
                                    </div>
                                </div>
                                <div class="ai-metric">
                                    <div class="metric-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">信号置信度</div>
                                        <div class="metric-value" x-text="signalConfidence + '%'">78%</div>
                                    </div>
                                </div>
                                <div class="ai-metric">
                                    <div class="metric-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">下次分析</div>
                                        <div class="metric-value" x-text="nextAnalysisTime">3分钟后</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据面板 -->
                <div class="data-panels">
                    <!-- 市场数据面板 -->
                    <div class="data-panel market-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-chart-area"></i> 市场数据</h3>
                            <div class="panel-controls">
                                <select id="market-symbol" class="symbol-selector">
                                    <option value="BTC/USDT:USDT">BTC/USDT</option>
                                    <option value="ETH/USDT:USDT">ETH/USDT</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="market-data" class="market-data-grid">
                                <div class="market-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载市场数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 持仓汇总面板 -->
                    <div class="data-panel position-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-pie-chart"></i> 持仓汇总</h3>
                            <div class="panel-controls">
                                <button class="btn-small" onclick="app.updatePositionSummary()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="position-summary" class="position-summary-grid">
                                <div class="position-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载持仓数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能统计面板 -->
                    <div class="data-panel performance-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-trophy"></i> 交易统计</h3>
                            <div class="panel-controls">
                                <select id="stats-period" class="period-selector">
                                    <option value="today">今日</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="performance-stats" class="performance-grid">
                                <div class="perf-item">
                                    <div class="perf-label">总收益</div>
                                    <div class="perf-value positive" id="total-pnl">+0.00</div>
                                    <div class="perf-unit">USDT</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">胜率</div>
                                    <div class="perf-value" id="win-rate">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">交易次数</div>
                                    <div class="perf-value" id="trade-count">0</div>
                                    <div class="perf-unit">笔</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">最大回撤</div>
                                    <div class="perf-value" id="max-drawdown">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易控制页面 -->
            <div id="trading" class="tab-content">
                <div class="page-header">
                    <h1>交易控制</h1>
                    <p>启动、停止和监控交易系统</p>
                </div>

                <div class="control-panel">
                    <div class="control-section">
                        <h3>交易控制</h3>
                        <div class="control-buttons">
                            <button id="start-trading-btn" class="btn btn-success">
                                <i class="fas fa-play"></i>
                                启动交易
                            </button>
                            <button id="stop-trading-btn" class="btn btn-danger" disabled>
                                <i class="fas fa-stop"></i>
                                停止交易
                            </button>
                        </div>
                    </div>

                    <div class="control-section">
                        <h3>交易对选择</h3>
                        <div class="trading-pairs">
                            <div class="pair-item">
                                <input type="checkbox" id="btc-usdt" checked>
                                <label for="btc-usdt">BTC/USDT</label>
                            </div>
                            <div class="pair-item">
                                <input type="checkbox" id="eth-usdt" checked>
                                <label for="eth-usdt">ETH/USDT</label>
                            </div>
                            <div class="pair-item">
                                <input type="checkbox" id="bnb-usdt">
                                <label for="bnb-usdt">BNB/USDT</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="trading-status">
                    <h3>交易状态详情</h3>
                    <div id="trading-details">
                        <p>交易系统未启动</p>
                    </div>
                </div>
            </div>

            <!-- 持仓管理页面 -->
            <div id="positions" class="tab-content">
                <div class="page-header">
                    <h1>持仓管理</h1>
                    <p>查看和管理当前持仓</p>
                </div>

                <div class="positions-table">
                    <table id="positions-table">
                        <thead>
                            <tr>
                                <th>交易对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>盈亏</th>
                                <th>杠杆</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="no-data">暂无持仓数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 系统配置页面 -->
            <div id="config" class="tab-content">
                <div class="page-header">
                    <h1>系统配置</h1>
                    <p>配置交易参数和系统设置</p>
                </div>

                <div class="config-sections">
                    <div class="config-section">
                        <h3>交易参数</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="max-leverage">最大杠杆</label>
                                <input type="number" id="max-leverage" min="1" max="100" value="10">
                            </div>
                            <div class="form-group">
                                <label for="max-position">最大仓位比例</label>
                                <input type="number" id="max-position" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                            <div class="form-group">
                                <label for="stop-loss">止损比例</label>
                                <input type="number" id="stop-loss" min="0.01" max="0.5" step="0.01" value="0.05">
                            </div>
                            <div class="form-group">
                                <label for="take-profit">止盈比例</label>
                                <input type="number" id="take-profit" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>风险控制</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="confidence-threshold">置信度阈值</label>
                                <input type="number" id="confidence-threshold" min="50" max="95" value="70">
                            </div>
                            <div class="form-group">
                                <label for="max-daily-loss">最大日损失</label>
                                <input type="number" id="max-daily-loss" min="0.01" max="0.5" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="config-actions">
                    <button id="save-config-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存配置
                    </button>
                    <button id="reset-config-btn" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置配置
                    </button>
                </div>
            </div>

            <!-- 日志记录页面 -->
            <div id="logs" class="tab-content">
                <div class="page-header">
                    <h1>日志记录</h1>
                    <p>查看系统运行日志和任务状态</p>
                </div>

                <div class="logs-section">
                    <div class="logs-controls">
                        <select id="log-level">
                            <option value="all">所有级别</option>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                        </select>
                        <button id="clear-logs-btn" class="btn btn-secondary">
                            <i class="fas fa-trash"></i>
                            清空日志
                        </button>
                    </div>
                    <div class="logs-container">
                        <div id="logs-content">
                            <p>正在加载日志...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
